'use client'

import { Box, Flex, useColorModeValue } from '@chakra-ui/react'
import { DragDropCanvas } from '../Canvas/DragDropCanvas'
import { useEditorStore } from '@/lib/stores/editorStore'

export function EditorCanvas() {
  const { currentBreakpoint, isPreviewMode } = useEditorStore()
  
  // Use fixed colors to prevent hydration mismatch
  const bgColor = '#f7fafc'
  const canvasBg = 'white'

  // Canvas dimensions based on breakpoint
  const getCanvasWidth = () => {
    switch (currentBreakpoint) {
      case 'mobile':
        return '375px'
      case 'tablet':
        return '768px'
      case 'desktop':
      default:
        return '100%'
    }
  }

  const getCanvasMaxWidth = () => {
    switch (currentBreakpoint) {
      case 'mobile':
        return '375px'
      case 'tablet':
        return '768px'
      case 'desktop':
      default:
        return '1200px'
    }
  }

  return (
    <Flex
      flex="1"
      bg={bgColor}
      justify="center"
      align="flex-start"
      p={4}
      overflow="auto"
      position="relative"
    >
      {/* Canvas Container */}
      <Box
        w={getCanvasWidth()}
        maxW={getCanvasMaxWidth()}
        minH="100vh"
        bg={canvasBg}
        borderRadius={currentBreakpoint !== 'desktop' ? 'lg' : 'none'}
        boxShadow={currentBreakpoint !== 'desktop' ? 'xl' : 'none'}
        border={currentBreakpoint !== 'desktop' ? '1px' : 'none'}
        borderColor="gray.200"
        overflow="hidden"
        transition="all 0.3s ease"
        position="relative"
      >
        {/* Device Frame for Mobile/Tablet */}
        {currentBreakpoint !== 'desktop' && (
          <>
            {/* Device Header */}
            <Box
              h="20px"
              bg="gray.100"
              borderTopRadius="lg"
              position="relative"
              display="flex"
              alignItems="center"
              justifyContent="center"
            >
              {/* Device Notch/Camera for Mobile */}
              {currentBreakpoint === 'mobile' && (
                <Box
                  w="60px"
                  h="6px"
                  bg="gray.300"
                  borderRadius="full"
                />
              )}
            </Box>
          </>
        )}

        {/* Main Canvas Content */}
        <Box
          minH={currentBreakpoint !== 'desktop' ? 'calc(100vh - 40px)' : '100vh'}
          position="relative"
        >
          <DragDropCanvas />
        </Box>

        {/* Device Footer for Mobile */}
        {currentBreakpoint === 'mobile' && (
          <Box
            h="20px"
            bg="gray.100"
            borderBottomRadius="lg"
            display="flex"
            alignItems="center"
            justifyContent="center"
          >
            <Box
              w="40px"
              h="4px"
              bg="gray.300"
              borderRadius="full"
            />
          </Box>
        )}
      </Box>

      {/* Breakpoint Indicator */}
      <Box
        position="absolute"
        top="4"
        right="4"
        bg="blackAlpha.700"
        color="white"
        px={3}
        py={1}
        borderRadius="md"
        fontSize="sm"
        fontWeight="medium"
        zIndex={10}
      >
        {currentBreakpoint.charAt(0).toUpperCase() + currentBreakpoint.slice(1)} View
        {currentBreakpoint !== 'desktop' && (
          <Box as="span" ml={2} opacity={0.7}>
            {getCanvasWidth()}
          </Box>
        )}
      </Box>
    </Flex>
  )
}
