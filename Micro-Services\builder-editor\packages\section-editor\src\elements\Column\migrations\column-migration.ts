import {
  ImageObjectFit,
  BackgroundType,
  BackgroundRepeat,
  BackgroundPosition,
  BackgroundScrollEffect,
  BackgroundSize,
  Settings,
  ImageAction,
  Background,
} from "@wuilt/section-preview";
import { nanoid } from "nanoid";

function migrateColumnBackground(settings: Settings) {
  if (Array.isArray(settings?.background)) {
    return settings;
  }

  type Image = {
    src?: string;
    alt?: string;
    overlay?: string;
    objectFit?: ImageObjectFit;
    action?: ImageAction;
  };

  type BackgroundMeta = {
    hasImage?: boolean;
    hasOverlay?: boolean;
  };

  type OldBackground = {
    color?: string;
    image?: Image;
    meta?: BackgroundMeta;
  };

  const oldBackground = settings?.background! as OldBackground;
  const migratedBackground: Background[] = [
    {
      id: nanoid(),
      type: oldBackground?.meta?.hasImage
        ? BackgroundType.Image
        : BackgroundType.Color,
      color: oldBackground?.color,
      image: {
        ...oldBackground?.image,
        repeat: BackgroundRepeat["no-repeat"],
        position: BackgroundPosition["center center"],
        scrollEffect: BackgroundScrollEffect.scroll,
        size: BackgroundSize.Cover,
      },
      gradient: undefined,
    },
  ];

  if (oldBackground?.meta?.hasImage && oldBackground?.meta?.hasOverlay) {
    migratedBackground.unshift({
      id: nanoid(),
      type: BackgroundType.Color,
      color: oldBackground?.image?.overlay,
      image: undefined,
      gradient: undefined,
    });
  }

  settings.background = migratedBackground;
  return settings;
}

export function migrateColumnSettings(settings: Settings | undefined) {
  if (!settings) return {};

  return migrateColumnBackground(settings);
}
