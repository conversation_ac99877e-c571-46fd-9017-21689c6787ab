import { FormattedMessage } from "react-intl";

const FADE_OPTIONS = [
  {
    value: "fade",
    label: <FormattedMessage defaultMessage="Fade" id="0sOHRS" />,
  },
  {
    value: "fade-up",
    label: <FormattedMessage defaultMessage="Fade up" id="rQnt31" />,
  },
  {
    value: "fade-down",
    label: <FormattedMessage defaultMessage="Fade down" id="rDLtnh" />,
  },
  {
    value: "fade-left",
    label: <FormattedMessage defaultMessage="Fade left" id="l+xyS1" />,
  },
  {
    value: "fade-right",
    label: <FormattedMessage defaultMessage="Fade right" id="ci/HAj" />,
  },
  {
    value: "fade-up-right",
    label: <FormattedMessage defaultMessage="Fade up right" id="PfvDF7" />,
  },
  {
    value: "fade-up-left",
    label: <FormattedMessage defaultMessage="Fade up left" id="K2cGLr" />,
  },
  {
    value: "fade-down-right",
    label: <FormattedMessage defaultMessage="Fade down right" id="blZQTv" />,
  },
  {
    value: "fade-down-left",
    label: <FormattedMessage defaultMessage="Fade down left" id="37LPJY" />,
  },
];

const FLIP_OPTIONS = [
  {
    value: "flip-up",
    label: <FormattedMessage defaultMessage="Flip up" id="5Yt92O" />,
  },
  {
    value: "flip-down",
    label: <FormattedMessage defaultMessage="Flip down" id="/ZHOF4" />,
  },
  {
    value: "flip-left",
    label: <FormattedMessage defaultMessage="Flip left" id="yWZwD6" />,
  },
  {
    value: "flip-right",
    label: <FormattedMessage defaultMessage="Flip right" id="h4pYXI" />,
  },
];

const SLIDE_OPTIONS = [
  {
    value: "slide-up",
    label: <FormattedMessage defaultMessage="Slide up" id="X9OlAY" />,
  },
  {
    value: "slide-down",
    label: <FormattedMessage defaultMessage="Slide down" id="dqcj0g" />,
  },
  {
    value: "slide-left",
    label: <FormattedMessage defaultMessage="Slide left" id="zREeHH" />,
  },
  {
    value: "slide-right",
    label: <FormattedMessage defaultMessage="Slide right" id="A2LL5m" />,
  },
];

const ZOOM_OPTIONS = [
  {
    value: "zoom-in-up",
    label: <FormattedMessage defaultMessage="Zoom in up" id="AUlEjk" />,
  },
  {
    value: "zoom-in-down",
    label: <FormattedMessage defaultMessage="Zoom in down" id="BPGh6m" />,
  },
  {
    value: "zoom-in-left",
    label: <FormattedMessage defaultMessage="Zoom in left" id="WFaHhT" />,
  },
  {
    value: "zoom-in-right",
    label: <FormattedMessage defaultMessage="Zoom in right" id="R2x0o3" />,
  },
  {
    value: "zoom-out-up",
    label: <FormattedMessage defaultMessage="Zoom out up" id="IJTJJ3" />,
  },
  {
    value: "zoom-out-down",
    label: <FormattedMessage defaultMessage="Zoom out down" id="eI5E+e" />,
  },
  {
    value: "zoom-out-left",
    label: <FormattedMessage defaultMessage="Zoom out left" id="ZlpagE" />,
  },
  {
    value: "zoom-out-right",
    label: <FormattedMessage defaultMessage="Zoom out right" id="ANGfPA" />,
  },
];

export const NONE_OPTION = {
  value: undefined,
  label: <FormattedMessage defaultMessage="None" id="450Fty" />,
};

export const GROUPED_ANIMATIONS_OPTIONS = [
  NONE_OPTION,
  {
    label: <FormattedMessage defaultMessage="Fade Animations" id="vbU1yp" />,
    options: FADE_OPTIONS,
  },
  {
    label: <FormattedMessage defaultMessage="Flip Animations" id="pl7DnF" />,
    options: FLIP_OPTIONS,
  },
  {
    label: <FormattedMessage defaultMessage="Slide Animations" id="dJLe+o" />,
    options: SLIDE_OPTIONS,
  },
  {
    label: <FormattedMessage defaultMessage="Zoom Animations" id="RZDe7H" />,
    options: ZOOM_OPTIONS,
  },
];

export const FLAT_ANIMATIONS_OPTIONS = [
  NONE_OPTION,
  ...FADE_OPTIONS,
  ...FLIP_OPTIONS,
  ...SLIDE_OPTIONS,
  ...ZOOM_OPTIONS,
];

export const EASING_OPTIONS = [
  {
    value: "linear",
    label: <FormattedMessage defaultMessage="Linear" id="XLcCrG" />,
  },
  {
    value: "ease",
    label: <FormattedMessage defaultMessage="Ease" id="MdIuoN" />,
  },
  {
    value: "ease-in",
    label: <FormattedMessage defaultMessage="Ease-in" id="/gjeVF" />,
  },
  {
    value: "ease-out",
    label: <FormattedMessage defaultMessage="Ease-out" id="PXK2b5" />,
  },
  {
    value: "ease-in-out",
    label: <FormattedMessage defaultMessage="Ease-in-out" id="IAkyRF" />,
  },
  {
    value: "ease-in-back",
    label: <FormattedMessage defaultMessage="Ease-in-back" id="e37wfi" />,
  },
  {
    value: "ease-out-back",
    label: <FormattedMessage defaultMessage="Ease-out-back" id="88HOQS" />,
  },
  {
    value: "ease-in-out-back",
    label: <FormattedMessage defaultMessage="Ease-in-out-back" id="NmzPHi" />,
  },
  {
    value: "ease-in-sine",
    label: <FormattedMessage defaultMessage="Ease-in-sine" id="1Uwqcy" />,
  },
  {
    value: "ease-out-sine",
    label: <FormattedMessage defaultMessage="Ease-out-sine" id="gSgPFU" />,
  },
  {
    value: "ease-in-out-sine",
    label: <FormattedMessage defaultMessage="Ease-in-out-sine" id="WjxlkX" />,
  },
  {
    value: "ease-in-quad",
    label: <FormattedMessage defaultMessage="Ease-in-quad" id="dtSlqM" />,
  },
  {
    value: "ease-out-quad",
    label: <FormattedMessage defaultMessage="Ease-out-quad" id="Nnr278" />,
  },
  {
    value: "ease-in-out-quad",
    label: <FormattedMessage defaultMessage="Ease-in-out-quad" id="kil9x5" />,
  },
  {
    value: "ease-in-cubic",
    label: <FormattedMessage defaultMessage="Ease-in-cubic" id="JUHmo9" />,
  },
  {
    value: "ease-out-cubic",
    label: <FormattedMessage defaultMessage="Ease-out-cubic" id="sxDJNF" />,
  },
  {
    value: "ease-in-out-cubic",
    label: <FormattedMessage defaultMessage="Ease-in-out-cubic" id="pFnlys" />,
  },
  {
    value: "ease-in-quart",
    label: <FormattedMessage defaultMessage="Ease-in-quart" id="lfROBY" />,
  },
  {
    value: "ease-out-quart",
    label: <FormattedMessage defaultMessage="Ease-out-quart" id="K9Pl8g" />,
  },
  {
    value: "ease-in-out-quart",
    label: <FormattedMessage defaultMessage="Ease-in-out-quart" id="LtCukq" />,
  },
];
