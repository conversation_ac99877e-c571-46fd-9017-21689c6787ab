import React from "react";
import {
  AppleStoreBlackMedium,
  AppleStoreBrandMedium,
  AppleStoreWhiteMedium,
  AppStoreButtonStyle,
  ButtonDesignSize,
  AppStoreButtonDesign,
} from "@wuilt/section-preview";
import { FormattedMessage } from "react-intl";
import styled, { css } from "styled-components";
import {
  Box,
  Divider,
  Stack,
  FormLabel,
  Alert,
  AlertIcon,
  AlertDescription,
} from "@chakra-ui/react";
import {
  LargeAppStoreButtonIcon,
  MediumAppStoreButtonIcon,
} from "@wuilt/react-icons";

const AppStoreButtonsStyles = [
  {
    illustration: <AppleStoreBrandMedium />,
    label: <FormattedMessage defaultMessage="Brand" id="SOPEeg" />,
    value: AppStoreButtonStyle.Brand,
  },
  {
    illustration: <AppleStoreBlackMedium />,
    label: <FormattedMessage defaultMessage="Black" id="XjGbuY" />,
    value: AppStoreButtonStyle.Black,
  },
  {
    illustration: <AppleStoreWhiteMedium />,
    label: <FormattedMessage defaultMessage="White" id="OAG+sQ" />,
    value: AppStoreButtonStyle.White,
  },
];

const AppStoreButtonsSizes = [
  {
    value: ButtonDesignSize.Large,
    label: <FormattedMessage defaultMessage="Large" id="/06iwc" />,
  },
  {
    value: ButtonDesignSize.Medium,
    label: <FormattedMessage defaultMessage="Medium" id="ovJ26C" />,
  },
];

interface AppStoreButtonsDesignProps {
  design?: AppStoreButtonDesign;
  updateAppStoreDesign: (arg: AppStoreButtonDesign) => void;
}
const AppStoreButtonsDesign: React.FC<AppStoreButtonsDesignProps> = ({
  design,
  updateAppStoreDesign,
}) => {
  return (
    <Box>
      <Box>
        <FormLabel mb="4px" fontSize="16px" fontWeight="400">
          <FormattedMessage defaultMessage="Button style" id="kYSEdK" />
        </FormLabel>
        <Stack direction="row" gap="16px">
          {AppStoreButtonsStyles?.map((style, index) => (
            <Stack
              gap="16px"
              key={index}
              align="center"
              onClick={() =>
                updateAppStoreDesign({ ...design, style: style?.value })
              }
              cursor="pointer"
            >
              <StyledAppStoreButtonStyle
                designStyle={style?.value}
                isActive={style?.value === design?.style}
              >
                {style?.illustration}
              </StyledAppStoreButtonStyle>
              <FormLabel m="0px" fontSize="16px" fontWeight="400">
                {style?.label}
              </FormLabel>
            </Stack>
          ))}
        </Stack>
      </Box>
      <Box mt="20px">
        <FormLabel mb="4px" fontSize="16px" fontWeight="400">
          <FormattedMessage defaultMessage="Button size" id="ubd9HZ" />
        </FormLabel>
        <Stack direction="row" gap="16px">
          {AppStoreButtonsSizes?.map((size, index) => (
            <Stack
              gap="16px"
              key={index}
              align="center"
              onClick={() =>
                updateAppStoreDesign({ ...design, size: size?.value })
              }
              cursor="pointer"
            >
              <Box
                p="8px"
                pt={size.value === "Large" && "6px"}
                width="100px"
                height="49px"
                border="2px solid"
                borderColor={
                  size?.value === design?.size ? "primary.500" : "gray.300"
                }
                borderRadius="4px"
                display="flex"
                justifyContent="center"
              >
                {size?.value === "Medium" ? (
                  <MediumAppStoreButtonIcon />
                ) : (
                  <LargeAppStoreButtonIcon />
                )}
              </Box>
              <FormLabel m="0px" fontSize="16px" fontWeight="400">
                {size?.label}
              </FormLabel>
            </Stack>
          ))}
        </Stack>
      </Box>
    </Box>
  );
};

export default AppStoreButtonsDesign;

const StyledAppStoreButtonStyle = styled.div<{
  designStyle: string;
  isActive: boolean;
}>`
  padding: 8px;
  border-radius: 4px;

  ${({ isActive }) =>
    isActive
      ? css`
          border: 2px solid #0e9384;
        `
      : css`
          border: 2px solid #d0d5dd;
        `}
  ${({ designStyle }) =>
    designStyle === "White"
      ? css`
          background-color: #667085;
        `
      : css`
          background-color: white;
        `}
  svg {
    width: 80px;
    height: 100%;
  }
`;
