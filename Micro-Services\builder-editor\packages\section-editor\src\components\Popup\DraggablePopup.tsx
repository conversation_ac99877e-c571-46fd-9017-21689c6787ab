import React, { ReactNode } from "react";
import { useDraggable } from "@dnd-kit/core";
import { CSS } from "@dnd-kit/utilities";
import styled from "styled-components";

export type PopupChildrenArgs = { closePopup: () => void };

export type DraggablePopupProps = {
  closePopup: () => void;
  children: ReactNode | ((args: PopupChildrenArgs) => ReactNode);
};

const DraggablePopup: React.FC<DraggablePopupProps> = ({
  closePopup,
  children,
}) => {
  const { transform, setNodeRef } = useDraggable({ id: "draggable-popup" });

  const style = {
    transform: CSS.Translate.toString(transform),
  };

  return (
    <SettingsPopupContainer
      onClick={(event) => event?.stopPropagation()}
      ref={setNodeRef}
      style={style}
    >
      {typeof children === "function" ? children({ closePopup }) : children}
    </SettingsPopupContainer>
  );
};

export { DraggablePopup };

/**
 * Styles
 */

const SettingsPopupContainer = styled.div`
  min-width: 300px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0px 0px 1px rgba(26, 32, 36, 0.32),
    0px 8px 16px rgba(91, 104, 113, 0.24);
`;
