import _cloneDeep from "lodash/cloneDeep";
import { nanoid } from "nanoid";
import { getDefaultColumn } from "../utils/getDefault";
import { duplicateElementMutation } from "./elementMutations";
import { resetRowGridTemplatesMutation } from "./rowMutations";
import {
  Column,
  CustomSection,
  WuiltContext,
  SectionElement,
  TextObject,
} from "@wuilt/section-preview";
import { getAllColumnTexts } from "../utils/getTexts";
import { deleteFormApiMutation } from "./formMutations";
import { getColumnForms } from "../utils/getForms";

export const addColumnMutation = (
  section: CustomSection,
  rowIndex: number,
  columnIndex: number,
  location: "before" | "after" = "after"
) => {
  const isToAddBefore = location === "before";
  const newColumnIndex = isToAddBefore ? columnIndex : columnIndex + 1;
  const newColumn = getDefaultColumn();
  let cloned = _cloneDeep(section);
  const columns = cloned?.rows[rowIndex]?.columns;
  columns?.splice(newColumnIndex, 0, newColumn);
  cloned.rows[rowIndex].columns = columns;
  cloned = resetRowGridTemplatesMutation(cloned, rowIndex);
  return cloned;
};

export function duplicateColumnMutation(
  section: CustomSection,
  rowIndex: number,
  columnIndex: number,
  returnNewColumn?: false,
  wuiltContext?: WuiltContext
): Promise<CustomSection>;

export function duplicateColumnMutation(
  section: CustomSection,
  rowIndex: number,
  columnIndex: number,
  returnNewColumn?: true,
  wuiltContext?: WuiltContext
): Promise<{
  newColumn: Column;
  newColumnTexts: TextObject;
}>;

export async function duplicateColumnMutation(
  section: CustomSection,
  rowIndex: number,
  columnIndex: number,
  returnNewColumn = false,
  wuiltContext: WuiltContext = {}
) {
  let cloned = _cloneDeep(section);
  const columns = cloned?.rows[rowIndex]?.columns;
  const newColumn = { ...columns[columnIndex], id: `Column:${nanoid()}` };
  const newElements = await Promise.all(
    newColumn?.elements?.map((e, elementIndex) =>
      duplicateElementMutation(
        section,
        rowIndex,
        columnIndex,
        elementIndex,
        true,
        wuiltContext
      )
    )
  );
  newColumn.elements = newElements?.map((e) => e?.newElement);
  const newTextsArray = newElements?.reduce(
    (acc, curr) => ({ ...acc, ...curr?.newElementTexts }),
    {}
  );
  const newColumnTexts = Object.assign({}, newTextsArray);
  cloned.text = {
    ...cloned.text,
    ...newColumnTexts,
  };

  if (returnNewColumn) return { newColumn, newColumnTexts };
  columns?.splice(columnIndex + 1, 0, newColumn);
  cloned.rows[rowIndex].columns = columns;
  cloned = resetRowGridTemplatesMutation(cloned, rowIndex);
  return cloned;
}

export const deleteColumnMutation = (
  section: CustomSection,
  rowIndex: number,
  columnIndex: number,
  wuiltContext: WuiltContext
) => {
  let cloned = _cloneDeep(section);
  const columns = cloned?.rows[rowIndex]?.columns;
  const column = columns[columnIndex];
  const text = cloned?.text;
  const deletedTexts = getAllColumnTexts(column);
  deletedTexts?.forEach((textId: string) => delete text[textId]);
  const columnFormsIds = getColumnForms(column);
  columnFormsIds && deleteFormApiMutation(columnFormsIds, wuiltContext);
  columns?.splice(columnIndex, 1);
  cloned.rows[rowIndex].columns = columns;
  cloned = resetRowGridTemplatesMutation(cloned, rowIndex);
  return cloned;
};

export const updateColumnMutation = (
  section: CustomSection,
  rowIndex: number,
  columnIndex: number,
  newSettings: Column["settings"]
) => {
  const cloned = _cloneDeep(section);
  cloned.rows[rowIndex].columns[columnIndex].settings = newSettings;
  return cloned;
};

export const sortColumnElementsMutation = (
  section: CustomSection,
  rowIndex: number,
  columnIndex: number,
  sortedElements: SectionElement[]
) => {
  const cloned = _cloneDeep(section);
  cloned.rows[rowIndex].columns[columnIndex].elements = sortedElements;
  return cloned;
};
