import React, { useState } from "react";
import { FormattedMessage } from "react-intl";
import type { ElementSettingsProps } from "../../Element/components/ElementSettings";
import AppStoreButtonDetails from "./AppStoreButtonDetails";
import AppStoreButtons from "./AppStoreButtons";
import AppStoreButtonsDesign from "./AppStoreButtonsDesign";
import SortableAppStoreButtons from "./SortableAppStoreButtons";
import {
  AppStoreButtonsSettings,
  AppStoreButtonSettings as AppStoreButtonSettingsType,
  AppStoreButtonDesign,
} from "@wuilt/section-preview";
import {
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Stack,
  Heading,
  Button,
} from "@chakra-ui/react";
import { ChevronLeftIcon } from "@wuilt/react-icons";
import { useTheme } from "styled-components";
import { Popup } from "../../../components/Popup";

export enum AppStoreButtonsSettingsViews {
  "List",
  "Edit",
  "Settings",
}

enum AppSoreButtonsSettingsTabs {
  "Buttons",
  "Design",
}

const TABS = [
  {
    content: <FormattedMessage defaultMessage="Buttons" id="E2rh1p" />,
    id: AppSoreButtonsSettingsTabs.Buttons,
  },
  {
    content: <FormattedMessage defaultMessage="Design" id="zmjMfM" />,
    id: AppSoreButtonsSettingsTabs.Design,
  },
];

interface AppStoreButtonSettingsProps extends ElementSettingsProps {}

const AppStoreButtonSettings: React.FC<AppStoreButtonSettingsProps> = ({
  element,
  updateElementUi,
}) => {
  const [view, setView] = useState(AppStoreButtonsSettingsViews.List);
  const [activeButton, setActiveButton] = useState({});
  const elementSettings = element?.settings as AppStoreButtonsSettings;
  const dir = useTheme()?.dir;

  const addAppStoreButton = (appStoreButton: AppStoreButtonSettingsType) => {
    updateElementUi({
      ...element,
      settings: {
        ...elementSettings,
        appStoreButtons: [...elementSettings.appStoreButtons!, appStoreButton],
      },
    });
  };

  const updateAppStoreButtonAction = (
    appStoreButton: AppStoreButtonSettingsType
  ) => {
    const updatedAppStoreButtons: AppStoreButtonSettingsType[] =
      elementSettings?.appStoreButtons?.map((btn) =>
        btn?.id === appStoreButton?.id ? appStoreButton : btn
      )!;
    setActiveButton(appStoreButton);
    updateElementUi({
      ...element,
      settings: {
        ...elementSettings,
        appStoreButtons: updatedAppStoreButtons,
      },
    });
  };

  const updateAppStoreDesign = (design: AppStoreButtonDesign) => {
    updateElementUi({ ...element, settings: { ...elementSettings, design } });
  };

  const sortAppStoreButtonsUi = (buttons: AppStoreButtonSettingsType[]) => {
    updateElementUi({
      ...element,
      settings: {
        ...elementSettings,
        appStoreButtons: buttons,
      },
    });
  };

  const deleteAppStoreButton = (id: string) => {
    const updatedAppStoreButtons: AppStoreButtonSettingsType[] =
      elementSettings?.appStoreButtons?.filter((btn) => btn?.id !== id)!;
    updateElementUi({
      ...element,
      settings: {
        ...elementSettings,
        appStoreButtons: updatedAppStoreButtons,
      },
    });
  };
  return (
    <>
      <Popup.Header
        headerContentControls={{
          justifyContent: `${
            view === AppStoreButtonsSettingsViews.List
              ? "space-between"
              : "start"
          }`,
        }}
      >
        {view === AppStoreButtonsSettingsViews.Settings ? (
          <>
            <Button
              variant="plain"
              size="sm"
              paddingInline={0}
              color="white"
              onClick={() => setView(AppStoreButtonsSettingsViews.List)}
              leftIcon={
                <ChevronLeftIcon
                  size="16px"
                  transform={dir === "rtl" && "rotate(180deg)"}
                />
              }
            >
              <FormattedMessage defaultMessage="Back" id="cyR7Kh" />
            </Button>
            <Heading variant="h5" p="10px">
              <FormattedMessage defaultMessage="App store button" id="imf6rn" />
            </Heading>
          </>
        ) : (
          <>
            {view === AppStoreButtonsSettingsViews.Edit && (
              <Button
                variant="plain"
                size="sm"
                paddingInline={0}
                color="white"
                onClick={() => setView(AppStoreButtonsSettingsViews.List)}
                leftIcon={
                  <ChevronLeftIcon
                    size="16px"
                    transform={dir === "rtl" && "rotate(180deg)"}
                  />
                }
              >
                <FormattedMessage defaultMessage="Back" id="cyR7Kh" />
              </Button>
            )}
            <Heading marginInline="10px" variant="h5">
              <FormattedMessage
                defaultMessage="App store buttons"
                id="kP3kdg"
              />
            </Heading>
          </>
        )}
      </Popup.Header>
      <Popup.Body
        width="370px"
        pt={view === AppStoreButtonsSettingsViews.Settings ? undefined : "0"}
      >
        {view === AppStoreButtonsSettingsViews.List && (
          <Stack gap="16px">
            <Tabs>
              <TabList>
                {TABS.map(({ content, id }) => {
                  return (
                    <Tab
                      _selected={{
                        borderColor: "primary.500",
                        color: "primary.500",
                      }}
                      _focusWithin={{ outline: "none" }}
                      key={id}
                      width={"100%"}
                      p={"12px"}
                      fontSize={"14px"}
                    >
                      {content}
                    </Tab>
                  );
                })}
              </TabList>
              <TabPanels>
                <TabPanel paddingInline={0} paddingBottom={0}>
                  <AppStoreButtons
                    setView={setView}
                    appStoreButtons={elementSettings?.appStoreButtons}
                    setActiveButton={setActiveButton}
                    addAppStoreButton={addAppStoreButton}
                    sortAppStoreButtonsUi={sortAppStoreButtonsUi}
                    deleteAppStoreButton={deleteAppStoreButton}
                  />
                </TabPanel>
                <TabPanel paddingInline={0} paddingBottom={0}>
                  <AppStoreButtonsDesign
                    design={elementSettings?.design}
                    updateAppStoreDesign={updateAppStoreDesign}
                  />
                </TabPanel>
              </TabPanels>
            </Tabs>
          </Stack>
        )}
        {view === AppStoreButtonsSettingsViews.Settings && (
          <AppStoreButtonDetails
            button={activeButton}
            updateAppStoreButtonAction={updateAppStoreButtonAction}
          />
        )}
      </Popup.Body>
    </>
  );
};

export default AppStoreButtonSettings;
