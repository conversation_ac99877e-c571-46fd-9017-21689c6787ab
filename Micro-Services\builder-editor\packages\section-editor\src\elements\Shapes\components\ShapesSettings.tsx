import { Popup } from "../../../components/Popup";
import { Heading } from "@chakra-ui/react";
import { FormattedMessage } from "react-intl";
import ImageSettingsTabs from "../../Image/components/ImageSettingsTabs";
import React, { useState } from "react";
import ShapesDropdown from "./ShapesDropdown";
import styled from "styled-components";

export default function ShapesSettings(props) {
  const [toggleStroke, setToggleStroke] = useState(false);
  const [toggleDropShadow, setToggleDropShadow] = useState(false);
  const [dropShadow, setDropoShadow] = useState([3, 3, 3, "#000"]);
  const [toggleBlur, setToggleBlur] = useState(false);
  const selectHandler = (val: string) => {
    props.updateElementUi({
      ...props.element,
      shape: val,
    });
  };
  const valueHandler = (e, prop) => {
    props.updateElementUi({
      ...props.element,
      [prop]: e.target.value,
    });
  };
  const blendModeHandler = (e) => {
    props.updateElementUi({
      ...props.element,
      mixBlendMode: e.target.value,
    });
  };
  const toggleStrokeHandler = () => {
    let toggle;
    if (toggleStroke || props.element.strokeWidth > 0) {
      props.updateElementUi({
        ...props.element,
        stroke: "#000",
        strokeWidth: 0,
      });
      toggle = false;
    } else {
      props.updateElementUi({
        ...props.element,
        stroke: "#000",
        strokeWidth: 3,
      });
      toggle = true;
    }
    setToggleStroke(toggle);
  };
  const toggleBlurHandler = () => {
    let toggle;
    if (toggleBlur || props.element.blur > 0) {
      props.updateElementUi({
        ...props.element,
        blur: 0,
      });
      toggle = false;
    } else {
      props.updateElementUi({
        ...props.element,
        blur: 10,
      });
      toggle = true;
    }
    setToggleBlur(toggle);
  };
  const toggleDropShadowHandler = () => {
    let toggle;
    if (toggleDropShadow || props.element.shadow.color != "transparent") {
      props.updateElementUi({
        ...props.element,
        shadow: {
          x: 0,
          y: 0,
          blur: 0,
          color: "transparent",
        },
      });
      toggle = false;
    } else {
      props.updateElementUi({
        ...props.element,
        shadow: {
          x: 3,
          y: 3,
          blur: 3,
          color: "#000",
        },
      });
      toggle = true;
    }
    setToggleDropShadow(toggle);
  };
  const dropShadowValuesHandler = (e, name) => {
    props.updateElementUi({
      ...props.element,
      shadow: {
        ...props.element.shadow,
        [name]: e.target.value,
      },
    });
  };
  return (
    <>
      <Popup.Header>
        <Heading variant="h5">
          {/*<FormattedMessage defaultMessage="Shapes Settings" id="Bl2raZ" />*/}
          Shapes Settings
        </Heading>
      </Popup.Header>
      <Popup.Body width="350px" pt="0">
        <span>Shape</span>
        <ShapesDropdown val={props.element.shape} onSelect={selectHandler} />
        <Input>
          <span>color</span>
          <input
            type="color"
            defaultValue={props.element.fill}
            onChange={(e) => valueHandler(e, "fill")}
          />
        </Input>
        <Input>
          <span>blend mode</span>
          <select
            defaultValue={props.element.mixBlendMode}
            onChange={blendModeHandler}
          >
            <option value="normal">Normal</option>
            <option value="multiply">Multiply</option>
            <option value="screen">Screen</option>
            <option value="overlay">Overlay</option>
            <option value="darken">Darken</option>
            <option value="lighten">Lighten</option>
            <option value="color-dodge">Color Dodge</option>
            <option value="color-burn">Color Burn</option>
            <option value="hard-light">Hard Light</option>
            <option value="soft-light">Soft Light</option>
            <option value="difference">Difference</option>
            <option value="exclusion">Exclusion</option>
            <option value="hue">Hue</option>
            <option value="saturation">Saturation</option>
            <option value="color">Color</option>
            <option value="luminosity">Luminosity</option>
            <option value="plus-darker">Plus Darker</option>
            <option value="plus-lighter">Plus Lighter</option>
          </select>
        </Input>
        <Input>
          <span>stroke</span>
          <Switcher
            onClick={toggleStrokeHandler}
            className={
              (toggleStroke || props.element.strokeWidth > 0) && "active"
            }
          />
        </Input>
        {(toggleStroke || props.element.strokeWidth > 0) && (
          <>
            <Input>
              <span>stroke color</span>
              <input
                type="color"
                defaultValue={props.element.stroke}
                onChange={(e) => valueHandler(e, "stroke")}
              />
            </Input>
            <Input>
              <span>Thickness</span>
              <input
                type="number"
                min={0}
                defaultValue={props.element.strokeWidth}
                style={{ width: "50px" }}
                onChange={(e) => valueHandler(e, "strokeWidth")}
              />
            </Input>
          </>
        )}
        <Input>
          <span>Drop Shadow</span>
          <Switcher
            onClick={toggleDropShadowHandler}
            className={
              (toggleDropShadow ||
                props.element.shadow.color != "transparent") &&
              "active"
            }
          />
        </Input>
        {(toggleDropShadow || props.element.shadow.color != "transparent") && (
          <>
            <Input>
              <span>left</span>
              <input
                type="number"
                min={0}
                defaultValue={props.element.shadow.x}
                style={{ width: "50px" }}
                onChange={(e) => dropShadowValuesHandler(e, "x")}
              />
            </Input>
            <Input>
              <span>top</span>
              <input
                type="number"
                min={0}
                defaultValue={props.element.shadow.y}
                style={{ width: "50px" }}
                onChange={(e) => dropShadowValuesHandler(e, "y")}
              />
            </Input>
            <Input>
              <span>amount</span>
              <input
                type="number"
                min={0}
                defaultValue={props.element.shadow.blur}
                style={{ width: "50px" }}
                onChange={(e) => dropShadowValuesHandler(e, "blur")}
              />
            </Input>
            <Input>
              <span>color</span>
              <input
                type="color"
                defaultValue={props.element.shadow.color}
                onChange={(e) => dropShadowValuesHandler(e, "color")}
              />
            </Input>
          </>
        )}
        <Input>
          <span>Blur</span>
          <Switcher
            onClick={toggleBlurHandler}
            className={(toggleBlur || props.element.blur > 0) && "active"}
          />
        </Input>
        {toggleBlur || props.element.blur > 0 ? (
          <Input>
            <span>Amount</span>
            <input
              type="number"
              min={0}
              defaultValue={props.element.blur}
              style={{ width: "50px" }}
              onChange={(e) => valueHandler(e, "blur")}
            />
          </Input>
        ) : null}
      </Popup.Body>
    </>
  );
}

const Input = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
  padding: 8px;
  option {
    text-transform: capitalize;
  }
`;
const Switcher = styled.div`
  height: 21px;
  width: 43px;
  background: rgb(204, 204, 204);
  border-radius: 30px;
  position: relative;
  transition: all 0.5s cubic-bezier(0.92, -0.74, 0.09, 1.57);

  &::after {
    transition: all 0.5s cubic-bezier(0.92, -0.74, 0.09, 1.57);
    position: absolute;
    top: 0px;
    left: 0px;
    height: 21px;
    width: 21px;
    background: rgb(161 161 161);
    content: "";
    border-radius: 50%;
  }

  &.active {
    background: rgb(107 107 107);

    &::after {
      background: rgb(0, 0, 0);
      left: calc(100% - 21px);
    }
  }
`;
