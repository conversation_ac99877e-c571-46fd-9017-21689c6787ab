import {
  ArrowRightIcon,
  ButtonIcon,
  Heading,
  PhotoCameraIcon,
  Popup,
  SettingsGeneralIcon,
  Stack,
  Tooltip,
} from "@wuilt/quilt";
import React, { useState } from "react";
import { FormattedMessage } from "react-intl";
import { SectionElement } from "@wuilt/section-preview";
import styled from "styled-components";
import ImageSettings from "./ImageSettings";

interface ImageSettingsButtonProps {
  element: SectionElement;
  pages?: any[];
  onUploadImage: (cb: (src: string) => void) => void;
  updateElementUi: (v: SectionElement) => void;
  mutateElementApi: (v: SectionElement) => void;
}

const ImageSettingsButton: React.FC<ImageSettingsButtonProps> = ({
  element,
  pages,
  onUploadImage,
  updateElementUi,
  mutateElementApi,
}) => {
  const [imageSettingsActive, setImageSettingsActive] = useState(true); // TODO: to be false when implementing crop

  return (
    <StyledWrapper data-test="ImageSettingsButton">
      <Popup
        onPopupClose={() => mutateElementApi(element)}
        activator={
          <span>
            <Tooltip
              content={
                <FormattedMessage defaultMessage="Image Settings" id="Bl2raZ" />
              }
            >
              <ButtonIcon rounded color="white">
                <PhotoCameraIcon size="lg" />
              </ButtonIcon>
            </Tooltip>
          </span>
        }
      >
        {({}) => (
          <>
            <Popup.Header>
              {imageSettingsActive ? (
                <Stack direction="row" align="center">
                  {/* <Button
                    compact
                    plain
                    color="white"
                    onClick={() => setImageSettingsActive(false)}
                    prefixIcon={<ArrowLeftIcon reverseOnRtl />}
                  >
                    <FormattedMessage defaultMessage="Back" id="cyR7Kh" />
                  </Button> */}
                  <Heading color="white" fontWeight="semiBold">
                    <FormattedMessage
                      defaultMessage="Image Settings"
                      id="Bl2raZ"
                    />
                  </Heading>
                </Stack>
              ) : (
                <FormattedMessage defaultMessage="Image" id="+0zv6g" />
              )}
            </Popup.Header>
            <Popup.Body width="350px" pt="0">
              {imageSettingsActive ? (
                <ImageSettings
                  element={element}
                  pages={pages}
                  onUploadImage={onUploadImage}
                  updateElementUi={updateElementUi}
                />
              ) : (
                <Stack pt="16px">
                  <Stack
                    direction="row"
                    justify="between"
                    align="center"
                    cursor="pointer"
                    onHover={{ color: "primary" }}
                    onClick={() => setImageSettingsActive(true)}
                  >
                    <Stack direction="row" align="center" spacing="tight">
                      <SettingsGeneralIcon />
                      <Heading>
                        <FormattedMessage
                          defaultMessage="Image Settings"
                          id="Bl2raZ"
                        />
                      </Heading>
                    </Stack>
                    <ArrowRightIcon reverseOnRtl />
                  </Stack>

                  {/* <Stack
                    direction="row"
                    align="center"
                    spacing="tight"
                    cursor="pointer"
                    onHover={{ color: "primary" }}
                  >
                    <CropIcon />
                    <Heading>
                      <FormattedMessage
                        defaultMessage="Adjust & Crop"
                        id="EFHzDQ"
                      />
                    </Heading>
                  </Stack> */}
                </Stack>
              )}
            </Popup.Body>
          </>
        )}
      </Popup>
    </StyledWrapper>
  );
};

export default ImageSettingsButton;

/**
 * Styles
 */

export const StyledWrapper = styled.div`
  display: flex;
  position: absolute;
  top: 0;
  justify-content: flex-end;
  align-items: center;
  box-sizing: border-box;
  width: 100%;
  z-index: 5;
  padding: 20px;
`;
