import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Site from '@/components/Site';
import { getSiteData, getPageData } from '@/lib/api';

interface PageProps {
  params: {
    slug: string[];
  };
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  try {
    const domain = process.env.VERCEL_URL || 'localhost:3004';
    const subdomain = domain.split('.')[0];
    
    const siteData = await getSiteData(subdomain);
    if (!siteData) {
      return {
        title: 'Site Not Found',
        description: 'The requested site could not be found.',
      };
    }

    const pageSlug = params.slug?.[0] || 'home';
    const pageData = await getPageData(siteData.id, pageSlug);
    
    if (!pageData) {
      return {
        title: 'Page Not Found',
        description: 'The requested page could not be found.',
      };
    }

    return {
      title: pageData.meta?.title || pageData.title || siteData.title,
      description: pageData.meta?.description || siteData.description,
      keywords: pageData.meta?.keywords,
      openGraph: {
        title: pageData.meta?.title || pageData.title || siteData.title,
        description: pageData.meta?.description || siteData.description,
        images: siteData.settings?.seo?.ogImage ? [siteData.settings.seo.ogImage] : [],
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Error',
      description: 'An error occurred while loading the page.',
    };
  }
}

export default async function SitePage({ params }: PageProps) {
  try {
    // Extract subdomain from request
    const domain = process.env.VERCEL_URL || 'localhost:3004';
    const subdomain = domain.split('.')[0];
    
    // Get site data
    const siteData = await getSiteData(subdomain);
    if (!siteData) {
      notFound();
    }

    // Get page data
    const pageSlug = params.slug?.[0] || 'home';
    const pageData = await getPageData(siteData.id, pageSlug);
    
    if (!pageData) {
      notFound();
    }

    // Determine current locale
    const currentLocale = siteData.settings?.language || 'en';

    return (
      <Site
        websiteId={siteData.id}
        siteData={siteData}
        pageData={pageData}
        currentLocale={currentLocale}
        animation={{
          enabled: true,
          duration: 1000,
          easing: 'ease-out',
        }}
      />
    );
  } catch (error) {
    console.error('Error rendering site page:', error);
    notFound();
  }
}
