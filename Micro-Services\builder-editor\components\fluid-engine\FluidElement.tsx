import React, { useState, useRef, useEffect } from 'react';
import { Box } from '@chakra-ui/react';
import { FluidElementControls } from './FluidElementControls';
import { ElementRenderer } from '@/app/editor/components/Canvas/ElementRenderer';

const ROW_HEIGHT = 30;

interface FluidElementProps {
  element: any;
  index: number;
  rows: number;
  columns: number;
  gridCellWidth: number;
  currentBreakpoint: string;
  isDragging: boolean;
  isResizing: boolean;
  isSelected: boolean;
  onSelect: () => void;
  onClearSelection: () => void;
  onUpdate: (element: any) => void;
  setIsDragging: (dragging: boolean) => void;
  setIsResizing: (resizing: boolean) => void;
  setGridGuideLines: (guidelines: any) => void;
  setElementGuideLines: (guidelines: any) => void;
  gridContainer: HTMLDivElement | undefined;
  gridContainerWidth: number;
}

export const FluidElement: React.FC<FluidElementProps> = ({
  element,
  index,
  rows,
  columns,
  gridCellWidth,
  currentBreakpoint,
  isDragging,
  isResizing,
  isSelected,
  onSelect,
  onClearSelection,
  onUpdate,
  setIsDragging,
  setIsResizing,
  setGridGuideLines,
  setElementGuideLines,
  gridContainer,
  gridContainerWidth,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [dragStart, setDragStart] = useState<{ x: number; y: number } | null>(null);
  const [resizeStart, setResizeStart] = useState<any>(null);
  const elementRef = useRef<HTMLDivElement>(null);

  // Get element layout for current breakpoint
  const layout = element.layout?.[currentBreakpoint] || {
    offset: { top: 0, left: 0 },
    size: { width: 4, height: 2 }
  };

  const { offset, size } = layout;
  const rowGap = 10; // Default row gap
  const columnGap = 10; // Default column gap

  // Calculate position and dimensions
  const elementWidth = size.width * gridCellWidth + (size.width - 1) * columnGap;
  const elementHeight = size.height * ROW_HEIGHT + (size.height - 1) * rowGap;
  const elementLeft = offset.left * gridCellWidth + offset.left * columnGap;
  const elementTop = offset.top * ROW_HEIGHT + offset.top * rowGap;

  const handleMouseDown = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSelect();
    
    if (e.detail === 2) {
      // Double click - enter edit mode
      return;
    }

    setDragStart({ x: e.clientX, y: e.clientY });
    setIsDragging(true);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!dragStart || !gridContainer) return;

    const deltaX = e.clientX - dragStart.x;
    const deltaY = e.clientY - dragStart.y;

    // Calculate new grid position
    const newLeft = Math.max(0, Math.min(columns - size.width, 
      Math.round((elementLeft + deltaX) / (gridCellWidth + columnGap))
    ));
    const newTop = Math.max(0, Math.min(rows - size.height,
      Math.round((elementTop + deltaY) / (ROW_HEIGHT + rowGap))
    ));

    // Update element layout
    const updatedElement = {
      ...element,
      layout: {
        ...element.layout,
        [currentBreakpoint]: {
          ...layout,
          offset: { left: newLeft, top: newTop }
        }
      }
    };

    onUpdate(updatedElement);
  };

  const handleMouseUp = () => {
    setDragStart(null);
    setIsDragging(false);
    setGridGuideLines({ vertical: [], horizontal: [] });
  };

  const handleResize = (direction: string, delta: { x: number; y: number }) => {
    let newWidth = size.width;
    let newHeight = size.height;
    let newLeft = offset.left;
    let newTop = offset.top;

    const gridDeltaX = Math.round(delta.x / (gridCellWidth + columnGap));
    const gridDeltaY = Math.round(delta.y / (ROW_HEIGHT + rowGap));

    switch (direction) {
      case 'se': // Southeast
        newWidth = Math.max(1, Math.min(columns - offset.left, size.width + gridDeltaX));
        newHeight = Math.max(1, Math.min(rows - offset.top, size.height + gridDeltaY));
        break;
      case 'sw': // Southwest
        newWidth = Math.max(1, size.width - gridDeltaX);
        newHeight = Math.max(1, Math.min(rows - offset.top, size.height + gridDeltaY));
        newLeft = Math.max(0, offset.left + gridDeltaX);
        break;
      case 'ne': // Northeast
        newWidth = Math.max(1, Math.min(columns - offset.left, size.width + gridDeltaX));
        newHeight = Math.max(1, size.height - gridDeltaY);
        newTop = Math.max(0, offset.top + gridDeltaY);
        break;
      case 'nw': // Northwest
        newWidth = Math.max(1, size.width - gridDeltaX);
        newHeight = Math.max(1, size.height - gridDeltaY);
        newLeft = Math.max(0, offset.left + gridDeltaX);
        newTop = Math.max(0, offset.top + gridDeltaY);
        break;
    }

    const updatedElement = {
      ...element,
      layout: {
        ...element.layout,
        [currentBreakpoint]: {
          offset: { left: newLeft, top: newTop },
          size: { width: newWidth, height: newHeight }
        }
      }
    };

    onUpdate(updatedElement);
  };

  useEffect(() => {
    if (dragStart) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [dragStart]);

  return (
    <Box
      ref={elementRef}
      position="absolute"
      left={`${elementLeft}px`}
      top={`${elementTop}px`}
      width={`${elementWidth}px`}
      height={`${elementHeight}px`}
      border={isSelected ? '2px solid' : '1px solid transparent'}
      borderColor={isSelected ? 'blue.500' : isHovered ? 'blue.300' : 'transparent'}
      borderRadius="4px"
      cursor={isDragging ? 'grabbing' : 'grab'}
      zIndex={isSelected ? 1000 : index + 1}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onMouseDown={handleMouseDown}
      onClick={(e) => e.stopPropagation()}
      transition="border-color 0.2s ease"
      overflow="hidden"
    >
      {/* Element Content */}
      <Box width="100%" height="100%" position="relative">
        <ElementRenderer element={element} />
      </Box>

      {/* Element Controls */}
      {(isSelected || isHovered) && !isDragging && !isResizing && (
        <FluidElementControls
          element={element}
          isSelected={isSelected}
          onResize={handleResize}
          onDelete={() => {
            // Handle delete
          }}
          onDuplicate={() => {
            // Handle duplicate
          }}
          setIsResizing={setIsResizing}
        />
      )}
    </Box>
  );
};
