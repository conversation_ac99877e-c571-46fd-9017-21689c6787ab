import { Borders } from "@wuilt/section-preview";

export enum BorderPosition {
  "all" = "all",
  "top" = "top",
  "bottom" = "bottom",
  "left" = "left",
  "right" = "right",
}

export const updateBorderValue = (activeBorder: BorderPosition, value: any) => {
  return {
    [activeBorder]: value,
    ...(activeBorder === BorderPosition.all && {
      left: value,
      right: value,
      bottom: value,
      top: value,
    }),
  };
};

export function checkIndicator(
  borders: Borders | undefined,
  position: BorderPosition
) {
  return (
    !!borders?.borderStyle?.[position] &&
    borders?.borderStyle?.[position] !== "none" &&
    !borders?.isAllSides
  );
}
