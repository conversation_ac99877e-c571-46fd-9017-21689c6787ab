'use client';

import React, { createContext, useContext } from 'react';

interface SiteContextType {
  siteId: string;
  siteData: any;
  pageData: any;
  currentLocale: string;
  createPageLink: (pageId: string) => string;
  navigateToPage: (pageId: string) => void;
  baseApiUrl: string;
}

const SiteElementsContext = createContext<SiteContextType | null>(null);

export function SiteElementsProvider({
  children,
  value,
}: {
  children: React.ReactNode;
  value: SiteContextType;
}) {
  return (
    <SiteElementsContext.Provider value={value}>
      {children}
    </SiteElementsContext.Provider>
  );
}

export function useSiteElements() {
  const context = useContext(SiteElementsContext);
  return context;
}
