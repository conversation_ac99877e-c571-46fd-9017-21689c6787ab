import React from "react";
import { ElementsViewer } from "@wuilt/section-preview";
import { ElementProps } from "../Element";
import { Box } from "@wuilt/quilt";

export interface SocialLinksEditProps extends ElementProps {}
const SocialLinksEdit: React.FC<SocialLinksEditProps> = ({
  element,
  wuiltContext,
}) => {
  return (
    <Box id={element?.id} dataTest="SocialLinksEdit" p="4px 0">
      <ElementsViewer.SocialLinks
        noAction
        element={element}
        wuiltContext={wuiltContext}
      ></ElementsViewer.SocialLinks>
    </Box>
  );
};

export default SocialLinksEdit;
