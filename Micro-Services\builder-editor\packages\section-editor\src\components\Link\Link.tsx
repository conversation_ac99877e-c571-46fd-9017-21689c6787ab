import {
  NavLink as ReactRouterLink,
  NavLinkProps as ReactRouterLinkProps,
} from "react-router-dom";
import {
  Link as ChakraLink,
  forwardRef,
  LinkProps as ChakraLinkProps,
} from "@chakra-ui/react";

type MergedLinkProps = Omit<ChakraLinkProps, "className" | "color"> &
  Omit<ReactRouterLinkProps, "children" | "className" | "color"> & {
    color?: string;
    className?: string;
  };

interface LinkProps extends MergedLinkProps {}

export const Link = forwardRef<LinkProps, "a">(
  ({ to, href, children, ...props }, ref) => {
    return (
      <ChakraLink
        ref={ref}
        to={to ?? href}
        as={ReactRouterLink}
        _hover={{
          textDecoration: "underline",
        }}
        {...props}
      >
        {children}
      </ChakraLink>
    );
  }
);
