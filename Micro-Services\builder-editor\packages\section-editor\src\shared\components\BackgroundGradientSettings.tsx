import { Stack, Box, Label, InputColor, InputField } from "@wuilt/quilt";
import React from "react";
import styled from "styled-components";
import { FormattedMessage } from "react-intl";
import { Background, Gradient } from "@wuilt/section-preview";

interface BackgroundGradientSettingsProps {
  background: Background;
  updateBackground: (newBackground: Partial<Background>) => void;
}

const BackgroundGradientSettings: React.FC<BackgroundGradientSettingsProps> = ({
  background,
  updateBackground,
}) => {
  const gradient = background?.gradient;
  const updateGradient = (g: Partial<Gradient>) => {
    updateBackground({ gradient: { ...gradient, ...g } });
  };
  return (
    <Stack>
      <Box>
        <Label>
          <FormattedMessage defaultMessage="Gradient preview" id="60yH6k" />
        </Label>
        <StyledPreview
          startColor={gradient?.startColor!}
          endColor={gradient?.endColor!}
          startPosition={gradient?.startPosition!}
          endPosition={gradient?.endPosition!}
          angle={gradient?.angle!}
        />
      </Box>

      <Stack direction="row">
        <Box>
          <Label>
            <FormattedMessage defaultMessage="Start color" id="VhhUBa" />
          </Label>
          <InputColor
            value={gradient?.startColor || "#FFFFFF"}
            onChange={(startColor) => {
              updateGradient({ startColor });
            }}
          />
        </Box>
        <Box>
          <Label>
            <FormattedMessage defaultMessage="End color" id="OlX+qB" />
          </Label>
          <InputColor
            value={gradient?.endColor || "#FFFFFF"}
            onChange={(endColor) => {
              updateGradient({ endColor });
            }}
          />
        </Box>
      </Stack>

      <Stack direction="row">
        <Box>
          <Label>
            <FormattedMessage defaultMessage="Start position" id="3ZV1w5" />
          </Label>
          <InputField
            type="number"
            minValue={0}
            maxValue={100}
            noFraction
            prefix="%"
            value={gradient?.startPosition}
            onChange={(startPosition: any) => {
              updateGradient({ startPosition });
            }}
          />
        </Box>
        <Box>
          <Label>
            <FormattedMessage defaultMessage="End position" id="TAM4h1" />
          </Label>
          <InputField
            type="number"
            minValue={0}
            maxValue={100}
            noFraction
            prefix="%"
            value={gradient?.endPosition}
            onChange={(endPosition: any) => {
              updateGradient({ endPosition });
            }}
          />
        </Box>
      </Stack>

      <Box>
        <Label>
          <FormattedMessage defaultMessage="Angle" id="Pvso9W" />
        </Label>
        <InputField
          type="number"
          minValue={0}
          maxValue={360}
          width="47%"
          noFraction
          prefix={<FormattedMessage defaultMessage="deg" id="CZZzOv" />}
          value={gradient?.angle}
          onChange={(angle: any) => {
            updateGradient({ angle });
          }}
        />
      </Box>
    </Stack>
  );
};

export default BackgroundGradientSettings;

/**
 * Styles
 */

const StyledPreview = styled.div<{
  angle: number;
  startColor: string;
  endColor: string;
  startPosition: number;
  endPosition: number;
}>`
  width: 100%;
  height: 75px;
  border-radius: 6px;
  background-image: ${({
    angle,
    startColor,
    endColor,
    startPosition,
    endPosition,
  }) =>
    `linear-gradient(${angle}deg,${startColor} ${startPosition}%,${endColor} ${endPosition}% )`};
`;
