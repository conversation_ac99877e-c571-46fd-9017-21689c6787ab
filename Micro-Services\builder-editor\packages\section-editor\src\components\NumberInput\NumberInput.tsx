import React from "react";
import {
  NumberInput as ChakraNumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  NumberInputProps as ChakraNumberInputProps,
  NumberInputFieldProps,
  NumberInputStepperProps,
  NumberIncrementStepperProps,
  NumberDecrementStepperProps,
} from "@chakra-ui/react";
import { ChevronSmallDown, ChevronSmallUp } from "@wuilt/react-icons";

interface NumberInputProps extends ChakraNumberInputProps {
  inputFieldProps?: NumberInputFieldProps;
  stepperProps?: NumberInputStepperProps;
  incrementStepperProps?: NumberIncrementStepperProps;
  decrementStepperProps?: NumberDecrementStepperProps;
}

function NumberInput({
  inputFieldProps,
  stepperProps,
  incrementStepperProps,
  decrementStepperProps,
  ...props
}: NumberInputProps) {
  return (
    <ChakraNumberInput {...props}>
      <NumberInputField {...inputFieldProps} />
      <NumberInputStepper {...stepperProps}>
        <NumberIncrementStepper {...incrementStepperProps}>
          <ChevronSmallUp size="12px" />
        </NumberIncrementStepper>
        <NumberDecrementStepper {...decrementStepperProps}>
          <ChevronSmallDown size="12px" />
        </NumberDecrementStepper>
      </NumberInputStepper>
    </ChakraNumberInput>
  );
}

export default NumberInput;
