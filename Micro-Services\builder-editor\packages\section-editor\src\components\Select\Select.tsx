import React from "react";
import { Select as ChakraSelect, Props, ActionMeta } from "chakra-react-select";
import { HStack, Text } from "@chakra-ui/react";

export interface Option {
  label: any;
  value: any;
  [key: string]: any;
}

interface SelectProps extends Props<Option> {
  onChange?: (newValue: Option, actionMeta: ActionMeta<Option>) => void;
}

const Select: React.FC<SelectProps> = ({ chakraStyles, ...props }) => {
  return (
    <ChakraSelect
      useBasicStyles
      isMulti={false}
      isSearchable={false}
      selectedOptionStyle="check"
      menuPortalTarget={document.body}
      styles={{
        menuPortal: (base) => ({ ...base, zIndex: 9999 }),
      }}
      formatOptionLabel={props?.formatOptionLabel ?? formatOptionLabel}
      chakraStyles={{
        container: (base) => ({
          ...base,
          width: "full",
        }),
        control: (base) => ({
          ...base,
          padding: "10px",
          borderColor: "gray.300",
        }),
        valueContainer: (base) => ({
          ...base,
          paddingTop: "0px",
          paddingBottom: "0px",
          paddingInlineStart: "0px",
        }),
        singleValue: (base) => ({
          ...base,
          margin: "0px",
        }),
        dropdownIndicator: (base) => ({
          ...base,
          color: "gray.500",
        }),
        option: (base) => ({
          ...base,
          display: "flex",
          flexDirection: "row-reverse",
          justifyContent: "space-between",
          _selected: { color: "primary.600", backgroundColor: "gray.50" },
        }),
        ...chakraStyles,
      }}
      {...props}
    />
  );
};

// function createOption(optionValue: any, options: Option[]) {
//   if (!optionValue) return options[0];
//   return options.find((option) => option.value === optionValue);
// }

function formatOptionLabel(option: Option) {
  return (
    <HStack gap="8px">
      <Text variant="textMd" fontWeight="medium" color="gray.800">
        {option?.label}
      </Text>
    </HStack>
  );
}

export default Select;
