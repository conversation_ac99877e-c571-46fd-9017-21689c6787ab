import { Box, InputColor, Label } from "@wuilt/quilt";
import { Background } from "@wuilt/section-preview";
import React from "react";
import { FormattedMessage } from "react-intl";

interface BackgroundColorSettingsProps {
  background: Background;
  updateBackground: (newBackground: Partial<Background>) => void;
}

const BackgroundColorSettings: React.FC<BackgroundColorSettingsProps> = ({
  background,
  updateBackground,
}) => {
  const color = background?.color;
  const updateColor = (color: string) => {
    updateBackground({ color });
  };
  return (
    <Box>
      <Label>
        <FormattedMessage defaultMessage="Background color" id="CMansq" />
      </Label>
      <InputColor value={color || "#FFFFFF01"} onChange={updateColor} />
    </Box>
  );
};

export default BackgroundColorSettings;
