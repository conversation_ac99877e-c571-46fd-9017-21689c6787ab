{"name": "@wuilt/section-editor", "version": "2.0.3", "files": ["build"], "main": "./build/main.cjs.js", "module": "./build/main.es.js", "types": "./build/index.d.ts", "exports": {".": {"import": "./build/main.es.js", "require": "./build/main.cjs.js"}}, "repository": {"type": "git", "url": "https://github.com/wuilt/builder-frontend"}, "scripts": {"build": "tsc && vite build", "watch": "tsc && vite build --watch", "lint": "eslint . --fix --ext .ts,.tsx"}, "peerDependencies": {"@chakra-ui/react": "^2.8.2", "chakra-react-select": "4.7.6", "@wuilt/quilt": "*", "@wuilt/section-preview": "*", "@wuilt/react-icons": "*", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@emotion/server": "^11.11.0", "framer-motion": "^11.1.9", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-intl": "^6.2.10", "react-debounce-input": "^3.3.0", "styled-components": "^5.3.8"}, "dependencies": {"react-hook-form": "^7.48.2", "yup": "^1.0.2", "react-select": "^5.7.0"}, "devDependencies": {"@babel/core": "^7.21.0", "@chakra-ui/react": "^2.8.2", "chakra-react-select": "4.7.6", "@formatjs/cli": "^6.0.4", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@emotion/server": "^11.11.0", "@tinymce/tinymce-react": "^4.3.0", "@types/lodash": "^4.14.191", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^5.54.1", "@typescript-eslint/parser": "^5.54.1", "@vitejs/plugin-react": "^3.1.0", "@wuilt/quilt": "latest", "@wuilt/section-preview": "*", "@wuilt/react-icons": "*", "accent-cli": "^0.12.0", "eslint": "^8.35.0", "eslint-config-prettier": "^8.7.0", "eslint-plugin-formatjs": "^4.9.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.11", "eslint-plugin-react-hooks": "^4.6.0", "framer-motion": "^11.1.9", "lodash": "^4.17.21", "nanoid": "^4.0.1", "prettier": "^2.8.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-intl": "^6.2.10", "react-debounce-input": "^3.3.0", "react-is": "^18.2.0", "styled-components": "^5.3.8", "typescript": "^4.9.5", "tinymce": "^6.0.0", "vite": "^4.1.4", "vite-plugin-dts": "^2.0.2"}, "gitHead": "12a3c00aa9589fa1f00e65fc5a05cd89220dd94c"}