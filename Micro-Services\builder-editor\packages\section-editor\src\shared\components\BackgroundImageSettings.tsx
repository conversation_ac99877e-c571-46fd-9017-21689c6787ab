import {
  Box,
  Button,
  ButtonIcon,
  Divider,
  GarbageIcon,
  Label,
  RadioButton,
  RadioGroup,
  SelectTab,
  SelectTabs,
  Stack,
  Text,
  Tooltip,
  UploadIcon,
} from "@wuilt/quilt";
import {
  BackgroundPosition,
  BackgroundSize,
  BackgroundRepeat,
  BackgroundScrollEffect,
  Image,
  Background,
} from "@wuilt/section-preview";
import React from "react";
import { FormattedMessage } from "react-intl";

const POSITIONS = [
  {
    value: BackgroundPosition["left top"],
    label: <FormattedMessage defaultMessage="Top left" id="UBZpAW" />,
  },
  {
    value: BackgroundPosition["center top"],
    label: <FormattedMessage defaultMessage="Top" id="X/9XdX" />,
  },
  {
    value: BackgroundPosition["right top"],
    label: <FormattedMessage defaultMessage="Top right" id="Wcr1kq" />,
  },
  {
    value: BackgroundPosition["left center"],
    label: <FormattedMessage defaultMessage="Left" id="lJEnpw" />,
  },
  {
    value: BackgroundPosition["center center"],
    label: <FormattedMessage defaultMessage="Center" id="QPmWTa" />,
  },
  {
    value: BackgroundPosition["right center"],
    label: <FormattedMessage defaultMessage="Right" id="gAnLDP" />,
  },
  {
    value: BackgroundPosition["left bottom"],
    label: <FormattedMessage defaultMessage="Bottom left" id="KR6ZL3" />,
  },
  {
    value: BackgroundPosition["center bottom"],
    label: <FormattedMessage defaultMessage="Bottom" id="hFAjsX" />,
  },
  {
    value: BackgroundPosition["right bottom"],
    label: <FormattedMessage defaultMessage="Bottom right" id="ZOYrk7" />,
  },
];

interface BackgroundImageSettingsProps {
  background: Background;
  updateBackground: (newBackground: Partial<Background>) => void;
  onUploadImage: (cb: any) => void;
}

const BackgroundImageSettings: React.FC<BackgroundImageSettingsProps> = ({
  background,
  updateBackground,
  onUploadImage,
}) => {
  const image = background?.image;
  const updateImage = (g: Partial<Image>) => {
    updateBackground({ image: { ...image, ...g } });
  };

  const backgroundImageStyles = background?.image?.src
    ? {
        backgroundImage: `url(${background?.image?.src})`,
        backgroundPosition: "center center",
        backgroundSize: "cover",
      }
    : {};

  return (
    <Stack>
      <Stack
        justify={background?.image?.src ? "start" : "center"}
        align={background?.image?.src ? "end" : "center"}
        width="100%"
        padding="6px"
        height="150px"
        borderRadius="6px"
        border="1px solid"
        borderColor="overlay"
        backgroundColor={{ cloud: "light" }}
        style={backgroundImageStyles}
        onClick={(event: any) => {
          event.stopPropagation();
          const callback = (src: string) => {
            updateImage({ src });
          };
          onUploadImage(callback);
        }}
      >
        {background?.image?.src ? (
          <Tooltip
            content={
              <FormattedMessage defaultMessage="Remove image" id="7vM5rK" />
            }
          >
            <ButtonIcon
              size="small"
              color="white"
              onClick={(event) => {
                event.stopPropagation();
                updateImage({ src: "" });
              }}
            >
              <GarbageIcon color="danger" />
            </ButtonIcon>
          </Tooltip>
        ) : (
          <Button
            color="white"
            size="small"
            squared={!!background?.image?.src}
            prefixIcon={<UploadIcon />}
          >
            <FormattedMessage defaultMessage="Upload" id="p4N05H" />
          </Button>
        )}
      </Stack>

      <Divider />

      <Box>
        <Label>
          <FormattedMessage defaultMessage="Background size" id="ECqBmx" />
        </Label>
        <RadioGroup
          value={image?.size}
          onChange={(size) => updateImage({ size })}
        >
          <RadioButton
            label={
              <Stack spacing="tight">
                <Text fontSize="medium" fontWeight="semiBold" color="secondary">
                  <FormattedMessage defaultMessage="Auto" id="NXI/XL" />
                </Text>
                <Text>
                  <FormattedMessage
                    defaultMessage="Keep the image in its original size"
                    id="sa7qmc"
                  />
                </Text>
              </Stack>
            }
            value={BackgroundSize.Auto}
          />

          <RadioButton
            label={
              <Stack spacing="tight">
                <Text fontSize="medium" fontWeight="semiBold" color="secondary">
                  <FormattedMessage defaultMessage="Cover" id="hl9bd4" />
                </Text>
                <Text>
                  <FormattedMessage
                    defaultMessage="Resize the image to cover the entire container"
                    id="Ms3Zt2"
                  />
                </Text>
              </Stack>
            }
            value={BackgroundSize.Cover}
          />

          <RadioButton
            label={
              <Stack spacing="tight">
                <Text fontSize="medium" fontWeight="semiBold" color="secondary">
                  <FormattedMessage defaultMessage="Contain" id="U9AgWv" />
                </Text>
                <Text>
                  <FormattedMessage
                    defaultMessage="Resize the image to make sure the image is fully visible"
                    id="XOZ0cv"
                  />
                </Text>
              </Stack>
            }
            value={BackgroundSize.Contain}
          />
        </RadioGroup>
      </Box>

      <Divider />

      <Box>
        <Label>
          <FormattedMessage defaultMessage="Background position" id="lVHnKy" />
        </Label>
        <Box display="grid" gridTemplateColumns="1fr 1fr 1fr" width="130px">
          {POSITIONS.map((p) => {
            const isSelected = p.value === image?.position;
            return (
              <Tooltip key={p.value} content={p.label}>
                <ButtonIcon
                  color={isSelected ? "primary" : "white"}
                  onClick={() => {
                    updateImage({ position: p.value });
                  }}
                >
                  <Box
                    width="10px"
                    height="10px"
                    borderRadius="10px"
                    bg={isSelected ? "disabled" : "white"}
                    display="inline-block"
                  />
                </ButtonIcon>
              </Tooltip>
            );
          })}
        </Box>
      </Box>

      <Divider />

      <Box>
        <Label>
          <FormattedMessage defaultMessage="Background repeat" id="/VoXYb" />
        </Label>
        <SelectTabs
          value={image?.repeat || BackgroundRepeat.repeat}
          name="background-repeat"
          onChange={(repeat) => {
            updateImage({ repeat });
          }}
        >
          <SelectTab
            label={<FormattedMessage defaultMessage="Repeat" id="tw5j2+" />}
            value={BackgroundRepeat.repeat}
          />
          <SelectTab
            label={<FormattedMessage defaultMessage="No repeat" id="UbB6wW" />}
            value={BackgroundRepeat["no-repeat"]}
          />
        </SelectTabs>
      </Box>

      <Divider />

      <Box>
        <Label>
          <FormattedMessage
            defaultMessage="Background scroll effects"
            id="hL6vDI"
          />
        </Label>
        <SelectTabs
          value={image?.scrollEffect || BackgroundScrollEffect.scroll}
          name="background-scroll-effect"
          onChange={(scrollEffect) => {
            updateImage({ scrollEffect });
          }}
        >
          <SelectTab
            label={<FormattedMessage defaultMessage="Scroll" id="SxCvYA" />}
            value={BackgroundScrollEffect.scroll}
          />
          <SelectTab
            label={<FormattedMessage defaultMessage="Fixed" id="jzcSPW" />}
            value={BackgroundScrollEffect.fixed}
          />
        </SelectTabs>
      </Box>
    </Stack>
  );
};

export default BackgroundImageSettings;
