// Adapter to integrate Old Builder sections-designs with new builder
// This bridges the Old Builder section designs with our new Supabase-based architecture

export interface OldBuilderSectionDesign {
  id: string
  category: string
  name: string
  description: string
  thumbnail: string
  component: any
  meta: any
  styles: any
  defaultProps?: Record<string, any>
}

export interface SectionCategory {
  id: string
  name: string
  icon: string
  count: number
  designs: OldBuilderSectionDesign[]
}

// Map Old Builder categories to our new structure
const CATEGORY_MAPPING = {
  hero: { name: 'Hero', icon: '🎯', description: 'Eye-catching hero sections' },
  features: { name: 'Features', icon: '⭐', description: 'Showcase your features' },
  about: { name: 'About', icon: '📖', description: 'Tell your story' },
  services: { name: 'Services', icon: '🔧', description: 'Display your services' },
  team: { name: 'Team', icon: '👥', description: 'Meet the team' },
  testimonials: { name: 'Testimonials', icon: '💬', description: 'Customer reviews' },
  contact: { name: 'Contact', icon: '📞', description: 'Contact information' },
  gallery: { name: 'Gallery', icon: '🖼️', description: 'Image galleries' },
  pricing: { name: 'Pricing', icon: '💰', description: 'Pricing tables' },
  'call-to-action': { name: 'Call to Action', icon: '🚀', description: 'Drive conversions' },
  content: { name: 'Content', icon: '📝', description: 'Rich content sections' },
  logos: { name: 'Logos', icon: '🏢', description: 'Logo showcases' },
  video: { name: 'Video', icon: '🎬', description: 'Video content' },
  footer: { name: 'Footer', icon: '📄', description: 'Page footers' },
  header: { name: 'Header', icon: '📋', description: 'Page headers' },
  embed: { name: 'Embed', icon: '🔗', description: 'HTML embeds' },
  map: { name: 'Map', icon: '🗺️', description: 'Location maps' },
}

// Generate thumbnails for sections (placeholder implementation)
function generateThumbnail(category: string, designId: string): string {
  const colors = {
    hero: '4299e1',
    features: '48bb78',
    about: 'ed8936',
    services: '9f7aea',
    team: 'f56565',
    testimonials: '38b2ac',
    contact: 'ed64a6',
    gallery: '667eea',
    pricing: 'f6ad55',
    'call-to-action': 'fc8181',
    content: '68d391',
    logos: 'a78bfa',
    video: 'fbb6ce',
    footer: '9ae6b4',
    header: 'fbd38d',
    embed: 'bee3f8',
    map: 'c6f6d5'
  }
  
  const color = colors[category as keyof typeof colors] || '718096'
  const categoryName = CATEGORY_MAPPING[category as keyof typeof CATEGORY_MAPPING]?.name || category
  
  return `/api/placeholder-image?width=300&height=200&text=${encodeURIComponent(categoryName + ' ' + designId)}&bg=${color}&color=ffffff`
}

// Convert Old Builder section design to our format
function convertOldBuilderDesign(category: string, designId: string, design: any): OldBuilderSectionDesign {
  const categoryInfo = CATEGORY_MAPPING[category as keyof typeof CATEGORY_MAPPING]
  
  return {
    id: `${category}-${designId}`,
    category,
    name: `${categoryInfo?.name || category} ${designId}`,
    description: design.meta?.description || `Professional ${categoryInfo?.name || category} section`,
    thumbnail: generateThumbnail(category, designId),
    component: design.default || design.component,
    meta: design.meta,
    styles: design.styles,
    defaultProps: design.defaultProps || {}
  }
}

// Load Old Builder sections (static data for now to avoid import.meta issues)
export async function loadOldBuilderSections(): Promise<SectionCategory[]> {
  try {
    // For now, return static data based on Old Builder structure
    // This will be replaced with dynamic loading once import issues are resolved
    const categories: SectionCategory[] = []

    // Generate mock sections for each category
    for (const [categoryKey, categoryInfo] of Object.entries(CATEGORY_MAPPING)) {
      const designs: OldBuilderSectionDesign[] = []

      // Generate 10-15 mock designs per category (matching Old Builder counts)
      const designCount = Math.floor(Math.random() * 6) + 10 // 10-15 designs

      for (let i = 1; i <= designCount; i++) {
        const designId = String(i).padStart(4, '0') // 0001, 0002, etc.
        designs.push({
          id: `${categoryKey}-${designId}`,
          category: categoryKey,
          name: `${categoryInfo.name} ${designId}`,
          description: `Professional ${categoryInfo.name.toLowerCase()} section design`,
          thumbnail: generateThumbnail(categoryKey, designId),
          component: null, // Will be loaded dynamically when needed
          meta: { description: `${categoryInfo.name} section template` },
          styles: {},
          defaultProps: {}
        })
      }

      categories.push({
        id: categoryKey,
        name: categoryInfo.name,
        icon: categoryInfo.icon,
        count: designs.length,
        designs
      })
    }

    return categories
  } catch (error) {
    console.error('Error loading Old Builder sections:', error)
    return []
  }
}

// Get sections for a specific category
export async function getSectionsByCategory(categoryId: string): Promise<OldBuilderSectionDesign[]> {
  const categories = await loadOldBuilderSections()
  const category = categories.find(c => c.id === categoryId)
  return category?.designs || []
}

// Get a specific section design
export async function getSectionDesign(category: string, designId: string): Promise<OldBuilderSectionDesign | null> {
  try {
    const categories = await loadOldBuilderSections()
    const categoryData = categories.find(c => c.id === category)
    if (!categoryData) return null

    const design = categoryData.designs.find(d => d.id === `${category}-${designId}`)
    return design || null
  } catch (error) {
    console.error('Error getting section design:', error)
    return null
  }
}

// Create a new section instance from Old Builder design
export function createSectionFromDesign(design: OldBuilderSectionDesign, afterSectionId?: string) {
  return {
    id: `section-${Date.now()}`,
    type: design.category as any,
    name: design.name,
    designId: design.id,
    elements: [], // Will be populated from design.component
    style: design.styles || {},
    props: design.defaultProps || {},
    responsive: {
      desktop: {},
      tablet: {},
      mobile: {}
    },
    // Store Old Builder specific data
    oldBuilderDesign: {
      category: design.category,
      designId: design.id.split('-')[1],
      component: design.component,
      meta: design.meta
    }
  }
}

// Helper to get all available categories
export async function getAvailableCategories(): Promise<Array<{id: string, name: string, icon: string, count: number}>> {
  const categories = await loadOldBuilderSections()
  return categories.map(cat => ({
    id: cat.id,
    name: cat.name,
    icon: cat.icon,
    count: cat.count
  }))
}

// Helper to search sections across all categories
export async function searchSections(query: string): Promise<OldBuilderSectionDesign[]> {
  const categories = await loadOldBuilderSections()
  const allDesigns = categories.flatMap(cat => cat.designs)
  
  return allDesigns.filter(design => 
    design.name.toLowerCase().includes(query.toLowerCase()) ||
    design.description.toLowerCase().includes(query.toLowerCase()) ||
    design.category.toLowerCase().includes(query.toLowerCase())
  )
}
