import {
  Box,
  Label,
  InputField,
  BoxProps,
  Text,
  InputFieldProps,
} from "@wuilt/quilt";
import { FormattedMessage } from "react-intl";

interface GapInputProps extends Omit<InputFieldProps, "onChange"> {
  value: number;
  boxProps?: BoxProps;
  onChange?: (v: number) => void;
}

const GapInput: React.FC<GapInputProps> = ({
  boxProps,
  onChange,
  ...inputProps
}) => {
  return (
    <Box {...boxProps}>
      <Label>
        <FormattedMessage defaultMessage="Column gap" id="+E5rEP" />
      </Label>
      <InputField
        {...inputProps}
        onChange={onChange as any}
        minValue={0}
        prefix={<Text fontSize="medium" children="px" />}
        type="number"
      />
    </Box>
  );
};

export default GapInput;
