import { useState, useEffect, useRef } from "react";
// import ResizeObserver from "resize-observer-polyfill";

export function useResizeObserver(refProp?: React.RefObject<HTMLDivElement>) {
  const refHook = useRef<HTMLElement>(null);
  const ref = refProp || refHook;
  const [size, setSize] = useState({ height: 1, width: 1 });

  useEffect(() => {
    if (ref.current == null) return;
    const observer = new ResizeObserver(([entry]) => {
      setSize({
        width: entry.borderBoxSize?.[0]?.inlineSize,
        height: entry.borderBoxSize?.[0]?.blockSize,
      });
    });
    observer.observe(ref.current);
    return () => observer.disconnect();
  }, [ref]);

  return { ref, size };
}
