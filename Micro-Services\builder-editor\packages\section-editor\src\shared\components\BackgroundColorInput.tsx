import {
  Box,
  BoxProps,
  InputColor,
  Label,
  InputColorProps,
} from "@wuilt/quilt";
import React from "react";

interface BackgroundColorInputProps extends InputColorProps {
  boxProps?: BoxProps;
  label?: React.ReactNode;
}

const BackgroundColorInput: React.FC<BackgroundColorInputProps> = ({
  label,
  boxProps,
  ...inputColorProps
}) => {
  return (
    <Box {...boxProps}>
      {label && <Label>{label}</Label>}
      <InputColor {...inputColorProps} />
    </Box>
  );
};

export default BackgroundColorInput;
