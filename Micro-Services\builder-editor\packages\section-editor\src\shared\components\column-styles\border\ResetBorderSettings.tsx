import { Box, Button, ResetIcon, Stack, Text } from "@wuilt/quilt";
import { FormattedMessage } from "react-intl";
import { BorderPosition } from "./helper";
import { Borders } from "@wuilt/section-preview";
interface BorderProps {
  borders: Borders | undefined;
  onChange: (v: Borders) => void;
  activeBorder: BorderPosition;
}

const BORDER_TEXT = {
  [BorderPosition.all]: (
    <FormattedMessage defaultMessage="all sides" id="SlCURl" />
  ),
  [BorderPosition.bottom]: (
    <FormattedMessage defaultMessage="bottom border" id="5Nf0Xq" />
  ),
  [BorderPosition.right]: (
    <FormattedMessage defaultMessage="right border" id="R/CyAp" />
  ),
  [BorderPosition.top]: (
    <FormattedMessage defaultMessage="top border" id="/S3A1O" />
  ),
  [BorderPosition.left]: (
    <FormattedMessage defaultMessage="left border" id="wW8wyA" />
  ),
};

function ResetBorderSettings({ borders, onChange, activeBorder }: BorderProps) {
  const resetBorder = () => {
    onChange({
      ...borders,
      borderColor: {
        ...borders?.borderColor,
        [activeBorder]: "#000000",
      },
      borderStyle: {
        ...borders?.borderStyle,
        [activeBorder]: "none",
      },
      borderWidth: {
        ...borders?.borderWidth,
        [activeBorder]: 1,
      },
    });
  };
  return (
    <Stack my="16px" direction="row" justify="between" align="center">
      <Box>
        <Text transform="uppercase" fontWeight="600">
          {BORDER_TEXT[activeBorder]}
        </Text>
      </Box>
      <Button
        onClick={resetBorder}
        plain
        color="info"
        padding="0"
        prefixIcon={<ResetIcon color="transparent" />}
      >
        <FormattedMessage defaultMessage="Reset" id="jm/spn" />
      </Button>
    </Stack>
  );
}

export default ResetBorderSettings;
