import {
  Accordion,
  Box,
  InfoIcon,
  InputField,
  Label,
  PlayCircleIcon,
  Stack,
  Text,
  ToggleButton,
  Tooltip,
  PlaySquareIcon,
  VimeoIcon,
  YoutubeIcon,
} from "@wuilt/quilt";
import React from "react";
import { FormattedMessage, useIntl } from "react-intl";
import trackEvent from "../utils/trackEvent";
import { VideoBackground } from "@wuilt/section-preview";

interface VideoBackgroundSettingsProps {
  source?: "SECTION" | "COLUMN" | "ROW";
  videoBackground: VideoBackground | undefined;
  updateVideoBackground: (v: VideoBackground) => void;
}

const VideoBackgroundSettings: React.FC<VideoBackgroundSettingsProps> = ({
  source,
  videoBackground,
  updateVideoBackground,
}) => {
  const intl = useIntl();
  const fireEvent = (active: boolean) => {
    trackEvent(
      source === "COLUMN"
        ? active
          ? "columnVideoBackgroundEnabled"
          : "columnVideoBackgroundDisabled"
        : source === "ROW"
        ? active
          ? "rowVideoBackgroundEnabled"
          : "rowVideoBackgroundDisabled"
        : active
        ? "sectionVideoBackgroundEnabled"
        : "sectionVideoBackgroundDisabled"
    );
  };

  const isYoutube = !!videoBackground?.src?.includes("youtube");
  const isVimeo = !!videoBackground?.src?.includes("vimeo");

  return (
    <Stack>
      <ToggleButton
        label={
          <FormattedMessage defaultMessage="Add video background" id="cmctr8" />
        }
        value={videoBackground?.active}
        onChange={(active) => {
          fireEvent(active);
          updateVideoBackground({ active });
        }}
      />

      {videoBackground?.active && (
        <Accordion initial borderRadius="4px">
          <Accordion.Header p="9px 12px">
            <Stack direction="row" align="center" spacing="compact">
              <PlaySquareIcon size="xl" color="primary" />
              {!isYoutube &&
                !isVimeo &&
                BorderBox(<PlayCircleIcon size="xl" color="info" />)}
              {isYoutube &&
                BorderBox(<YoutubeIcon size="xl" customColor="#FF3D00" />)}
              {isVimeo &&
                BorderBox(<VimeoIcon size="xl" customColor="#29B6F6" />)}
              <Text color="secondary" fontSize="medium">
                <FormattedMessage defaultMessage="Video" id="kBJUtE" />
              </Text>
            </Stack>
          </Accordion.Header>

          <Accordion.Body>
            <Stack>
              <Box>
                <Label>
                  <FormattedMessage defaultMessage="Video link" id="LX0iAn" />
                </Label>
                <InputField
                  placeholder={intl.formatMessage({
                    defaultMessage: "Paste video link here",
                    id: "9qB9mf",
                  })}
                  value={videoBackground?.src}
                  onChange={(event) => {
                    updateVideoBackground({ src: event?.target?.value });
                  }}
                />
              </Box>

              <Stack>
                <Stack direction="row" spacing="tight" align="center">
                  <Label>
                    <FormattedMessage
                      defaultMessage="Supported platforms"
                      id="cKXXDA"
                    />
                  </Label>
                  <Tooltip
                    content={
                      <FormattedMessage
                        defaultMessage="Acceptable URL formats {br} https://youtube.com/watch?v=xxxxx {br} https://youtube.com/embed/xxxxx {br} https://vimeo.com/xxxxx"
                        values={{ br: <br /> }}
                        id="zeLmIk"
                      />
                    }
                  >
                    <span>
                      <InfoIcon color="info" size="xl" />
                    </span>
                  </Tooltip>
                </Stack>
                <Stack direction="row" spacing="tight" align="center">
                  <Tooltip
                    content={
                      <FormattedMessage defaultMessage="Youtube" id="ix27PQ" />
                    }
                  >
                    <span>
                      <YoutubeIcon size="xxl" color="info" />
                    </span>
                  </Tooltip>
                  <Tooltip
                    content={
                      <FormattedMessage defaultMessage="Vimeo" id="D0ecjA" />
                    }
                  >
                    <span>
                      <VimeoIcon size="xxl" color="info" />
                    </span>
                  </Tooltip>
                </Stack>
              </Stack>
            </Stack>
          </Accordion.Body>
        </Accordion>
      )}
    </Stack>
  );
};

const BorderBox = (icon: React.ReactNode) => (
  <Stack border="1px solid" borderColor="disabled" borderRadius="4px">
    {icon}
  </Stack>
);

export default VideoBackgroundSettings;
