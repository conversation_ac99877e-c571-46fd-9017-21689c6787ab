import { BackgroundType, Background } from "@wuilt/section-preview";
import React from "react";
import BackgroundColorSettings from "./BackgroundColorSettings";
import BackgroundGradientSettings from "./BackgroundGradientSettings";
import BackgroundImageSettings from "./BackgroundImageSettings";

interface BackgroundTypeSettingsProps {
  background: Background;
  updateBackground: (newBackground: Partial<Background>) => void;
  onUploadImage: (cb: any) => void;
}

const BackgroundTypeSettings: React.FC<BackgroundTypeSettingsProps> = ({
  background,
  updateBackground,
  onUploadImage,
}) => {
  if (background?.type === BackgroundType.Gradient) {
    return (
      <BackgroundGradientSettings
        background={background}
        updateBackground={updateBackground}
      />
    );
  }

  if (background?.type === BackgroundType.Color) {
    return (
      <BackgroundColorSettings
        background={background}
        updateBackground={updateBackground}
      />
    );
  }

  if (background?.type === BackgroundType.Image) {
    return (
      <BackgroundImageSettings
        background={background}
        updateBackground={updateBackground}
        onUploadImage={onUploadImage}
      />
    );
  }

  return null;
};

export default BackgroundTypeSettings;
