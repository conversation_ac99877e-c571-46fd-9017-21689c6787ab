import React from "react";
import { FormattedMessage } from "react-intl";
import { ElementSettingsProps } from "../../Element/components/ElementSettings";
import { CounterSettings, CounterValueAndLabel } from "@wuilt/section-preview";
import CounterLabelAndValueSettings from "./CounterLabelAndValueSettings";
import {
  Box,
  Divider,
  Slider,
  SliderFilledTrack,
  SliderThumb,
  SliderTrack,
  Stack,
  Switch,
  Text,
} from "@chakra-ui/react";
import { SliderControlIcon } from "@wuilt/react-icons";
import { Popup } from "../../../components/Popup";

const PERCENT = "%";

interface CounterSettingProps extends ElementSettingsProps {}

const CounterSetting: React.FC<CounterSettingProps> = ({
  element,
  updateElementUi,
}) => {
  const elementSettings = element?.settings as CounterSettings;

  const updateCounterValue = (counterValue: CounterValueAndLabel) => {
    updateElementUi({
      ...element,
      settings: { ...element?.settings, counterValue },
    });
  };

  const updateCounterLabel = (counterLabel: CounterValueAndLabel) => {
    updateElementUi({
      ...element,
      settings: { ...element?.settings, counterLabel },
    });
  };

  return (
    <>
      <Popup.Header>
        <Text fontSize="18px" fontWeight="600" color="white">
          <FormattedMessage defaultMessage="Counter Settings" id="u5NDUE" />
        </Text>
      </Popup.Header>
      <Popup.Body width="350px" p="0">
        <Box p="16px">
          <CounterLabelAndValueSettings
            counterValueAndLabelSettings={elementSettings.counterValue}
            updateCounterValueAndLabel={updateCounterValue}
          />

          <Divider borderColor="gray.200" my="12px" />

          <Stack direction="row" align="center" gap="8px" mb="12px" zIndex={0}>
            <Switch
              mb="0"
              size="md"
              alignSelf="end"
              isChecked={elementSettings.LabelEnabled}
              onChange={() => {
                updateElementUi({
                  ...element,
                  settings: {
                    ...element?.settings,
                    LabelEnabled: !elementSettings.LabelEnabled,
                  },
                });
              }}
            />
            <Text as="span" fontSize="14px" fontWeight="500" color="gray.700">
              <FormattedMessage defaultMessage="Counter label" id="izNcvy" />
            </Text>
          </Stack>

          {elementSettings.LabelEnabled && (
            <CounterLabelAndValueSettings
              valueLabel={false}
              counterValueAndLabelSettings={elementSettings.counterLabel}
              updateCounterValueAndLabel={updateCounterLabel}
            />
          )}
          <Divider borderColor="gray.200" my="12px" />
          <Box>
            <Text
              as="label"
              htmlFor="animation"
              fontSize="12px"
              fontWeight="500"
              mb="6px"
              color="gray.700"
            >
              <FormattedMessage defaultMessage="Animation speed" id="Yge6ZS" />
            </Text>
            <Stack
              direction="row"
              my="6px"
              gap="16px"
              align="center"
              justify="center"
              mb="4px"
            >
              <Slider
                height="24px"
                value={elementSettings.animationSpeed}
                onChange={(value) =>
                  updateElementUi({
                    ...element,
                    settings: { ...element?.settings, animationSpeed: value },
                  })
                }
              >
                <SliderTrack height="6px" borderRadius="4px" bg="gray.200">
                  <SliderFilledTrack bg="primary.600" />
                </SliderTrack>
                <SliderThumb boxShadow="md" width="24px" height="24px">
                  <SliderControlIcon color="gray.400" size="28px" />
                </SliderThumb>
              </Slider>

              <Text as="span" fontSize="12px" fontWeight="500" minW="35px">
                {elementSettings.animationSpeed}
                {PERCENT}
              </Text>
            </Stack>
          </Box>
        </Box>
      </Popup.Body>
    </>
  );
};

export default CounterSetting;
