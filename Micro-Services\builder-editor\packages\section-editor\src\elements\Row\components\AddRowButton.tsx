import { Divider, IconButton, Tooltip } from "@chakra-ui/react";
import { AddIcon } from "@wuilt/react-icons";
import React from "react";
import { FormattedMessage } from "react-intl";
import styled from "styled-components";

interface AddRowButtonProps {
  disableAdding: boolean;
  addRow: (location?: "before" | "after") => void;
}

const AddRowButton: React.FC<AddRowButtonProps> = ({
  disableAdding,
  addRow,
}) => {
  if (disableAdding) return null;
  return (
    <StyledWrapper data-test="AddRowButton">
      <Divider position="absolute" color="primary" height="3px" />
      <Tooltip
        label={<FormattedMessage defaultMessage="Add Row" id="Cc0PzF" />}
      >
        <IconButton aria-label="Add Row" color="white" onClick={() => addRow()}>
          <AddIcon color="transparent" />
        </IconButton>
      </Tooltip>
    </StyledWrapper>
  );
};

export default AddRowButton;

/**
 * Styles
 */

const StyledWrapper = styled.div`
  display: flex;
  position: absolute;
  bottom: -20px;
  justify-content: flex-end;
  align-items: center;
  box-sizing: border-box;
  width: 100%;
  height: 20px;
  background: transparent;
  justify-content: center;
  z-index: 5;
  transform: translateY(50%);

  * {
    display: none;
  }
  &:hover {
    * {
      display: block;
    }
  }
`;
