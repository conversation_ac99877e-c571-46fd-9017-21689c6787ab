'use client'

import { useEffect } from 'react'
import { useEditorStore } from '@/lib/stores/editorStore'
import EditorPage from '../page'
import { ClientOnly } from '../../../components/ClientOnly'
import { Box, Spinner, Flex, Text } from '@chakra-ui/react'

// Demo page with sample data to test the editor
export default function DemoEditorPage() {
  const { setCurrentPage } = useEditorStore()

  useEffect(() => {
    // Create a demo page with sample content
    const demoPage = {
      id: 'demo-page-1',
      name: 'Demo Landing Page',
      slug: 'demo-landing',
      sections: [],
      seoSettings: {
        title: 'Demo Landing Page - Website Builder',
        description: 'A demo landing page showcasing the website builder capabilities',
        keywords: ['website builder', 'demo', 'landing page', 'responsive']
      },
      settings: {
        customCSS: '/* Custom styles for demo page */',
        customJS: '// Custom JavaScript for demo page'
      }
    }

    setCurrentPage(demoPage)
  }, [setCurrentPage])

  return (
    <ClientOnly
      fallback={
        <Flex
          height="100vh"
          align="center"
          justify="center"
          bg="#283340"
          direction="column"
          gap={4}
        >
          <Spinner size="xl" color="blue.500" />
          <Text color="white" fontSize="lg">
            Loading Editor...
          </Text>
        </Flex>
      }
    >
      <EditorPage />
    </ClientOnly>
  )
}
