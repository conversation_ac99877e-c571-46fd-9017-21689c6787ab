'use client'

import { useState } from 'react'
import {
  Box,
  VStack,
  Button,
  IconButton,
  Text,
  useColorModeValue,
  Collapse,
  Tooltip
} from '@chakra-ui/react'
import {
  AddIcon,
  DragHandleIcon,
  ViewIcon,
  SettingsIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@chakra-ui/icons'
import { ComponentPalette } from '../Palette/ComponentPalette'
import { TemplatePalette } from '../Palette/TemplatePalette'
import { SectionPalette } from '../Palette/SectionPalette'
import { useTranslation } from '@/lib/contexts/LanguageContext'

interface EditorSidebarProps {
  collapsed: boolean
  onToggleCollapse: () => void
}

export function EditorSidebar({ collapsed, onToggleCollapse }: EditorSidebarProps) {
  const [activeTab, setActiveTab] = useState<'sections' | 'elements'>('elements')
  const { t } = useTranslation()

  // Use fixed colors to prevent hydration mismatch
  const bgColor = 'white'
  const borderColor = 'gray.200'
  const textColor = 'gray.700'

  const sidebarWidth = collapsed ? '60px' : '280px'

  const tabs = [
    { id: 'sections', label: t('editor.sections'), icon: DragHandleIcon },
    { id: 'elements', label: t('editor.elements'), icon: AddIcon }
  ]

  return (
    <Box
      w={sidebarWidth}
      bg={bgColor}
      borderRight="1px"
      borderColor={borderColor}
      transition="width 0.3s ease-in-out"
      position="relative"
      overflow="hidden"
      zIndex={100}
      height="100%"
    >
      {/* Collapse Toggle */}
      <IconButton
        aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        icon={collapsed ? <ChevronRightIcon /> : <ChevronLeftIcon />}
        size="xs"
        position="absolute"
        top="50%"
        right="-12px"
        transform="translateY(-50%)"
        zIndex={10}
        bg={bgColor}
        border="1px"
        borderColor={borderColor}
        borderRadius="full"
        onClick={onToggleCollapse}
      />

      {!collapsed && (
        <VStack spacing={0} align="stretch" h="100%">
          {/* Tab Navigation */}
          <Box borderBottom="1px" borderColor={borderColor} p={2}>
            <VStack spacing={1}>
              {tabs.map((tab) => (
                <Button
                  key={tab.id}
                  leftIcon={<tab.icon />}
                  size="sm"
                  variant={activeTab === tab.id ? 'solid' : 'ghost'}
                  colorScheme={activeTab === tab.id ? 'blue' : 'gray'}
                  w="100%"
                  justifyContent="flex-start"
                  onClick={() => setActiveTab(tab.id as any)}
                >
                  {tab.label}
                </Button>
              ))}
            </VStack>
          </Box>

          {/* Tab Content */}
          <Box flex="1" overflow="hidden">
            {activeTab === 'sections' && (
              <Box h="100%" overflow="auto">
                <SectionPalette />
              </Box>
            )}

            {activeTab === 'elements' && (
              <Box h="100%" overflow="auto">
                <ComponentPalette />
              </Box>
            )}
          </Box>
        </VStack>
      )}

      {/* Collapsed State - Icon Only */}
      {collapsed && (
        <VStack spacing={2} p={2} align="center">
          {tabs.map((tab) => (
            <Tooltip key={tab.id} label={tab.label} placement="right">
              <IconButton
                aria-label={tab.label}
                icon={<tab.icon />}
                size="sm"
                variant={activeTab === tab.id ? 'solid' : 'ghost'}
                colorScheme={activeTab === tab.id ? 'blue' : 'gray'}
                onClick={() => setActiveTab(tab.id as any)}
              />
            </Tooltip>
          ))}
        </VStack>
      )}
    </Box>
  )
}
