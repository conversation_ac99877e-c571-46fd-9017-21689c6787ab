import { Box } from "@wuilt/quilt";
import { ElementsViewer } from "@wuilt/section-preview";
import React from "react";
import { ElementProps } from "../Element";

export interface VideoEditProps extends ElementProps {}
const VideoEdit: React.FC<VideoEditProps> = ({ element, wuiltContext }) => {
  return (
    <Box id={element?.id} dataTest="videoSettings" p="4px 0">
      <ElementsViewer.Video element={element} wuiltContext={wuiltContext} />
    </Box>
  );
};

export default VideoEdit;
