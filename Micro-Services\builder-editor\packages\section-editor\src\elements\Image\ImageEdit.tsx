import React from "react";
import { ElementsViewer } from "@wuilt/section-preview";
import { ElementProps } from "../Element";
import { Box } from "@wuilt/quilt";

interface ImageEditProps extends ElementProps {}

const ImageEdit: React.FC<ImageEditProps> = ({ element, wuiltContext }) => {
  return (
    <Box id={element?.id} dataTest="ImageEdit">
      <ElementsViewer.Image
        stopAction
        element={element}
        wuiltContext={wuiltContext}
      />
    </Box>
  );
};

export default ImageEdit;
