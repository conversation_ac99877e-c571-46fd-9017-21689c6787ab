import React, { useRef } from "react";
import { FormattedMessage } from "react-intl";
import styled from "styled-components";
import AddElementPopup from "../../Element/components/AddElementPopup";
import { SectionElement, WuiltContext } from "@wuilt/section-preview";
import { Popup } from "../../../components/Popup";
import { Button, Heading, Text } from "@chakra-ui/react";
import { useResizeObserver } from "../../../shared/utils/useResizeObserver";
import { PlusCircleIcon } from "@wuilt/react-icons";

interface EmptyColumnProps {
  wuiltContext?: WuiltContext;
  addElement: (value: SectionElement) => void;
  isSingleColumnEmpty: boolean;
}

const EmptyColumn: React.FC<EmptyColumnProps> = ({
  wuiltContext,
  addElement,
  // isSingleColumnEmpty,
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const {
    size: { width },
  } = useResizeObserver(ref);
  const isSmallSpace = width < 300;

  return (
    <StyledEmptyColumnState
      className="ui"
      ref={ref}
      isSingleColumnEmpty
      data-test="EmptyColumn"
    >
      <Heading fontSize="16px" fontWeight="700" color="ink.light">
        <FormattedMessage defaultMessage="Empty Column" id="PX61cr" />
      </Heading>
      <Text
        fontSize="12px"
        fontWeight="400"
        align="center"
        color="ink.light"
        className="col-text"
      >
        <FormattedMessage
          defaultMessage="Click “Add Element” to start customizing the section"
          id="L8l/I/"
        />
      </Text>

      <Popup
        activator={
          <Button
            borderRadius="50px"
            bg="primary.500"
            _hover={{ bg: "primary.600" }}
            h="36px"
            paddingInline="20px"
            leftIcon={<PlusCircleIcon size="18px" />}
          >
            {!isSmallSpace && (
              <FormattedMessage defaultMessage="Add Element" id="Y/H8Vd" />
            )}
          </Button>
        }
      >
        {({ closePopup }) => (
          <AddElementPopup
            wuiltContext={wuiltContext}
            closePopup={closePopup}
            addElement={addElement}
          />
        )}
      </Popup>
    </StyledEmptyColumnState>
  );
};

export default EmptyColumn;

/**
 * Styles
 */

export const StyledEmptyColumnState = styled.div<{
  isSingleColumnEmpty: boolean;
}>`
  background-color: ${(props) =>
    props?.isSingleColumnEmpty
      ? "var(--chakra-colors-cloud-dark)"
      : "transparent"};
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 30px 5px;
  height: 100%;
  gap: 15px;
  * {
    visibility: ${(props) =>
      props?.isSingleColumnEmpty
        ? "var(--chakra-colors-cloud-dark)"
        : "hidden"};
  }
  &:hover {
    background-color: "#e8edf1";
    outline: 10px solid white;
    outline-offset: -10px;
    * {
      visibility: visible;
    }
  }
  .col-text {
    text-align: center !important;
  }
`;
