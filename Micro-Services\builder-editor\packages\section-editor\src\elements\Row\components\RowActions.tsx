import React, { useRef, useState } from "react";
import { FormattedMessage } from "react-intl";
import trackEvent from "../../../shared/utils/trackEvent";
import { DuplicateIcon, GarbageIcon, MoreHorizIcon } from "@wuilt/react-icons";
import {
  AlertDialog,
  AlertDialogBody,
  AlertDialogCloseButton,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogOverlay,
  Box,
  IconButton,
  Popover,
  PopoverBody,
  PopoverContent,
  PopoverTrigger,
  Button,
  Portal,
  useDisclosure,
  useOutsideClick,
  Stack,
} from "@chakra-ui/react";

interface RowActionsProps {
  disableAdding: boolean;
  disableDeleting: boolean;
  deleteRow: () => void;
  duplicateRow: () => void;
}

const RowActions: React.FC<RowActionsProps> = ({
  disableAdding,
  disableDeleting,
  duplicateRow,
  deleteRow,
}) => {
  const [openConfirmModal, setOpenConfirmModal] = useState(false);
  const { isOpen, onToggle, onClose } = useDisclosure();
  const popoverRef = useRef(null);
  const cancelRef = useRef();

  useOutsideClick({
    ref: popoverRef,
    handler: onClose,
  });
  return (
    <>
      <Popover isOpen={isOpen} onClose={onClose}>
        <PopoverTrigger>
          <IconButton
            aria-label="Options"
            variant="plain"
            size="none"
            onClick={onToggle}
          >
            <MoreHorizIcon size="16px" />
          </IconButton>
        </PopoverTrigger>
        <Portal>
          <Box zIndex="popover">
            <PopoverContent ref={popoverRef} maxW="max-content">
              <PopoverBody>
                <Stack gap="16px" direction="column">
                  {!disableAdding && (
                    <Button
                      variant="plain"
                      size="sm"
                      paddingInline={0}
                      leftIcon={<DuplicateIcon color="black" size="16px" />}
                      onClick={() => {
                        duplicateRow();
                        trackEvent("rowDuplicated");
                        onClose();
                      }}
                    >
                      <FormattedMessage
                        defaultMessage="Duplicate"
                        id="4fHiNl"
                      />
                    </Button>
                  )}
                  {!disableDeleting && (
                    <Button
                      variant="plain"
                      size="sm"
                      color="error.500"
                      paddingInline={0}
                      onClick={() => {
                        setOpenConfirmModal(true);
                        onClose();
                      }}
                      leftIcon={<GarbageIcon size="16px" />}
                    >
                      <FormattedMessage defaultMessage="Delete" id="K3r6DQ" />
                    </Button>
                  )}
                </Stack>
              </PopoverBody>
            </PopoverContent>
          </Box>
        </Portal>
      </Popover>

      <AlertDialog
        motionPreset="slideInBottom"
        leastDestructiveRef={cancelRef}
        onClose={() => setOpenConfirmModal(false)}
        isOpen={openConfirmModal}
        isCentered
        returnFocusOnClose={false}
      >
        <AlertDialogOverlay />

        <AlertDialogContent>
          <AlertDialogHeader>
            <FormattedMessage defaultMessage="Deleting Row" id="6WmxNe" />
          </AlertDialogHeader>
          <AlertDialogCloseButton />
          <AlertDialogBody>
            <p>
              <FormattedMessage
                defaultMessage="Are you sure you want to delete this row? This action cannot be undone."
                id="Lx0THb"
              />
            </p>
          </AlertDialogBody>
          <AlertDialogFooter>
            <Button
              size="sm"
              variant="outline"
              ref={cancelRef}
              onClick={() => setOpenConfirmModal(false)}
            >
              <FormattedMessage defaultMessage="Cancel" id="47FYwb" />
            </Button>
            <Button
              size="sm"
              colorScheme="red"
              onClick={() => {
                deleteRow();
                trackEvent("rowDeleted");
              }}
              ml={3}
            >
              <FormattedMessage defaultMessage="Delete" id="K3r6DQ" />
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default RowActions;
