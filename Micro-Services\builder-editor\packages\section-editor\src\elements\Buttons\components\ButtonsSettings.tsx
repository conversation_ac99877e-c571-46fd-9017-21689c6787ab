import { useState } from "react";
import { FormattedMessage } from "react-intl";
import { updateButtonsSettingsMutation } from "../../../shared/mutations/buttonsMutations";
import { getDefaultButton } from "../../../shared/utils/getDefault";
import ButtonSettingsTabs from "../../Button/components/ButtonSettingsTabs";
import { ElementSettingsProps } from "../../Element/components/ElementSettings";
import {
  SectionElement,
  ButtonsSettings as ButtonsSettingsType,
  ButtonSettings as ButtonSettingsType,
} from "@wuilt/section-preview";
import HtmlRenderer from "../../../shared/components/HtmlRenderer";
import {
  Box,
  Button,
  Heading,
  Divider,
  Stack,
  IconButton,
} from "@chakra-ui/react";
import {
  AddIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  TrashIcon,
} from "@wuilt/react-icons";
import { DragHandle, VerticalSort } from "../../../components/DragAndDrop";
import { useTheme } from "styled-components";
import { Popup } from "../../../components/Popup";

enum ButtonsSettingsViews {
  "List",
  "Edit",
  "Settings",
}

interface ButtonsSettingsProps extends ElementSettingsProps {}

const ButtonsSettings: React.FC<ButtonsSettingsProps> = ({
  element,
  pages,
  updateElementUi,
  mutateElementApi,
  text,
}) => {
  const values = element?.settings as ButtonsSettingsType;
  const [view, setView] = useState(ButtonsSettingsViews.List);
  const [buttonOnSettingsIndex, setButtonOnSettingsIndex] = useState(0);
  const buttonOnSettings = values?.buttons?.[buttonOnSettingsIndex];
  const rtlDir = useTheme()?.rtl;

  const onMutateButtonApi = (newButton: SectionElement, index: number) => {
    const newButtons = updateButtonsSettingsMutation(element, newButton, index);
    mutateElementApi(newButtons);
  };

  const onUpdateButtonUi = (newButton: SectionElement, index: number) => {
    const newButtons = updateButtonsSettingsMutation(element, newButton, index);
    updateElementUi(newButtons);
  };

  const onAddButton = () => {
    const { settings, texts } = getDefaultButton();
    updateElementUi(
      {
        ...element,
        settings: {
          ...element?.settings,
          buttons: [...(values?.buttons || []), settings],
        },
      },
      texts
    );
  };

  const onSortButtons = (sortedButtons: ButtonSettingsType[]) => {
    updateElementUi({
      ...element,
      settings: { ...element?.settings, buttons: sortedButtons },
    });
  };

  const onRemoveButton = (button: ButtonSettingsType) => {
    if (values?.buttons?.length === 1) return;
    delete text[button?.textId!];
    updateElementUi(
      {
        ...element,
        settings: {
          ...element?.settings,
          buttons: values?.buttons?.filter((btn) => btn?.id !== button?.id),
        },
      },
      text
    );
  };

  return (
    <>
      <Popup.Header
        headerContentControls={{
          justifyContent: `${
            view === ButtonsSettingsViews.List ? "space-between" : "start"
          }`,
        }}
      >
        {view === ButtonsSettingsViews.List && (
          <>
            <Heading variant="h5">
              <FormattedMessage defaultMessage="Buttons Settings" id="DediUE" />
            </Heading>
            <Button
              size="sm"
              variant="plain"
              onClick={() => setView(ButtonsSettingsViews.Edit)}
            >
              <FormattedMessage defaultMessage="Edit" id="wEQDC6" />
            </Button>
          </>
        )}

        {view === ButtonsSettingsViews.Edit && (
          <Stack direction="row" gap="16px" align="center">
            <Button
              variant="plain"
              size="sm"
              paddingInline={0}
              leftIcon={
                <ChevronLeftIcon
                  size="16px"
                  transform={rtlDir && "rotate(180deg)"}
                />
              }
              onClick={() => setView(ButtonsSettingsViews.List)}
            >
              <FormattedMessage defaultMessage="Back" id="cyR7Kh" />
            </Button>
            <Heading variant="h5">
              <FormattedMessage defaultMessage="Buttons Settings" id="DediUE" />
            </Heading>
          </Stack>
        )}

        {view === ButtonsSettingsViews.Settings && (
          <Stack direction="row" align="center" gap="40px">
            <Button
              variant="plain"
              size="sm"
              paddingInline={0}
              leftIcon={
                <ChevronLeftIcon
                  size="16px"
                  transform={rtlDir && "rotate(180deg)"}
                />
              }
              onClick={() => setView(ButtonsSettingsViews.List)}
            >
              <FormattedMessage defaultMessage="Back" id="cyR7Kh" />
            </Button>
            <Stack spacing="tight" align="center">
              <Heading color="white" fontSize="sm" fontWeight="semiBold">
                <FormattedMessage
                  defaultMessage="Buttons Settings"
                  id="DediUE"
                />
              </Heading>
              <HtmlRenderer
                htmlText={text[buttonOnSettings?.textId!]}
                color="white"
              />
            </Stack>
          </Stack>
        )}
      </Popup.Header>

      <Popup.Body
        width="350px"
        pt={view === ButtonsSettingsViews.List ? undefined : "0"}
      >
        {view === ButtonsSettingsViews.List && (
          <>
            {values.buttons?.map((button, index) => (
              <Box key={button?.id} pb="6px">
                <Stack
                  py="10px"
                  align="center"
                  direction="row"
                  justify="space-between"
                  cursor="pointer"
                  _hover={{ color: "primary.500" }}
                  onClick={() => {
                    setView(ButtonsSettingsViews.Settings);
                    setButtonOnSettingsIndex(index);
                  }}
                >
                  <HtmlRenderer htmlText={text[button?.textId!]} />
                  <div>
                    <ChevronRightIcon
                      size="16px"
                      transform={rtlDir && "rotate(-180deg)"}
                    />
                  </div>
                </Stack>
                <Divider my="0" />
              </Box>
            ))}
            <Button
              variant="plain"
              color="primary.500"
              size="sm"
              paddingInline={0}
              leftIcon={<AddIcon size="20px" />}
              onClick={() => onAddButton()}
            >
              <FormattedMessage defaultMessage="Add button" id="sXpJes" />
            </Button>
          </>
        )}

        {view === ButtonsSettingsViews.Edit && (
          <VerticalSort
            useHandleOnly
            value={values?.buttons || []}
            onChange={onSortButtons}
          >
            {({ item: button }) => (
              <>
                <Stack
                  direction="row"
                  justify="space-between"
                  align="center"
                  className="dnd-item"
                  py="20px"
                >
                  <Stack direction="row" spacing="tight">
                    <DragHandle height="16px" width="16px" id={button?.id} />
                    <Box alignSelf="center">
                      <HtmlRenderer htmlText={text[button?.textId!]} />
                    </Box>
                  </Stack>
                  <IconButton
                    aria-label="Delete"
                    size="xs"
                    variant="plain"
                    disabled={values?.buttons?.length === 1}
                    onClick={() => onRemoveButton(button)}
                  >
                    <TrashIcon size="18px" />
                  </IconButton>
                </Stack>
                <Divider my="0" />
              </>
            )}
          </VerticalSort>
        )}

        {view === ButtonsSettingsViews.Settings && (
          <ButtonSettingsTabs
            pages={pages}
            element={{
              id: buttonOnSettings?.id!,
              type: "Button",
              settings: buttonOnSettings,
            }}
            updateElementUi={(newElement) => {
              onUpdateButtonUi(newElement, buttonOnSettingsIndex);
            }}
            mutateElementApi={(newElement) => {
              onMutateButtonApi(newElement, buttonOnSettingsIndex);
            }}
          />
        )}
      </Popup.Body>
    </>
  );
};

export default ButtonsSettings;
