export default function shapesMapper(style, id = null) {
  const shapes = {
    shape1: (
      <svg width="108%" height="108%" style={style}>
        <rect width="100%" height="100%"></rect>
      </svg>
    ),
    shape2: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={style}
      >
        <circle cx="50" cy="50" r="50" />
      </svg>
    ),
    shape3: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M50 0 L63 38 L100 38 L69 59 L82 100 L50 75 L18 100 L31 59 L0 38 L37 38 Z" />
      </svg>
    ),
    shape4: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={style}
      >
        <polygon points="50 0, 100 100, 0 100"></polygon>
      </svg>
    ),
    shape5: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M50 1l49 49-49 49L1 50z"></path>
      </svg>
    ),
    shape6: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M99.5 13.5h-13V.5h-73v13H.5v73h13v13h73v-13h13v-73z"></path>
      </svg>
    ),
    shape7: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M50 .5l43.1 6.4L99.5 50l-6.4 43.1L50 99.5 6.9 93.1.5 50 6.9 6.9 50 .5z"></path>
      </svg>
    ),
    shape8: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M90 95a190 190 0 01-80 0 8 8 0 01-5-5 188 188 0 010-80 8 8 0 015-5 190 190 0 0180 0 8 8 0 015 5 188 188 0 010 80 8 8 0 01-5 5z"></path>
      </svg>
    ),
    shape9: (
      <svg
        className="sqs-shape"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M70.5.5h-41l-29 29v41l29 29h41l29-29v-41l-29-29z"></path>
      </svg>
    ),
    shape10: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M36.1 4.1a28.3 28.3 0 0127.8 0 279.7 279.7 0 0126.4 19.6c6.9 6 10.8 18 8.6 27a289.4 289.4 0 01-10.1 31.7A28.8 28.8 0 0166.3 99a273.8 273.8 0 01-32.6 0c-9-.6-19.1-8.1-22.5-16.6A289.4 289.4 0 011.1 50.7c-2.2-9 1.7-21 8.6-27A279.7 279.7 0 0136 4.1z"></path>
      </svg>
    ),
    shape11: (
      <svg
        className="sqs-shape"
        viewBox="0 0 152 152"
        xmlns="http://www.w3.org/2000/svg"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M75.9957 152C74.4393 152 72.7717 151.047 72.5493 148.904C72.2757 146.25 70.112 145.051 68.2221 145.051C66.2979 145.051 64.7329 146.19 64.1343 148.028C63.6468 149.525 62.441 150.418 60.9016 150.418C59.8583 150.418 58.8492 149.984 58.1907 149.253C57.5579 148.555 57.2842 147.637 57.3868 146.599C57.5237 145.348 57.1559 144.175 56.352 143.29C55.5225 142.38 54.2825 141.835 53.0254 141.835C51.4775 141.835 50.1348 142.627 49.3395 144.004C48.681 145.136 47.6462 145.757 46.4233 145.757C45.2603 145.757 44.1229 145.187 43.4473 144.26C42.8144 143.401 42.669 142.312 43.0368 141.206C43.4815 139.845 43.2848 138.493 42.4723 137.387C41.6428 136.256 40.266 135.55 38.872 135.55C37.6747 135.55 36.5544 136.06 35.7078 136.996C35.0065 137.77 34.1343 138.178 33.185 138.178C31.9279 138.178 30.705 137.455 30.055 136.349C29.4393 135.294 29.4649 134.095 30.1234 132.964C30.9188 131.594 30.9359 130.038 30.1662 128.711C29.3794 127.358 27.8914 126.482 26.3777 126.482C25.4883 126.482 24.6075 126.78 23.8378 127.333C23.1879 127.801 22.4951 128.039 21.7853 128.039C20.4512 128.039 19.1599 127.171 18.5784 125.87C18.0225 124.62 18.2534 123.293 19.2198 122.229C20.3999 120.928 20.6907 119.21 19.9809 117.645C19.2711 116.054 17.7317 115.025 16.0556 115.025C15.4484 115.025 14.8327 115.161 14.2426 115.425C13.738 115.646 13.2335 115.756 12.746 115.756C11.0698 115.756 9.90678 114.515 9.50484 113.273C9.03448 111.818 9.51339 110.398 10.7962 109.479C12.3697 108.348 12.9769 106.511 12.3783 104.699C11.7882 102.896 10.1633 101.688 8.33323 101.688C8.00826 101.688 7.68329 101.722 7.35832 101.79C7.0761 101.849 6.79389 101.883 6.52878 101.883C4.66447 101.883 3.52707 100.488 3.23631 99.102C2.9541 97.8006 3.33893 96.1079 5.2289 95.2658C7.44384 94.2876 8.06812 92.1442 7.70039 90.443C7.34121 88.7503 5.88739 87.0321 3.47576 87.0321C1.30358 87.0321 0.174736 85.4756 0.0208025 83.9445C-0.141683 82.4049 0.64509 80.6527 2.74885 80.2104C5.11772 79.7086 6.17816 77.7352 6.17816 76C6.17816 74.2648 5.11772 72.2914 2.74885 71.7896C0.636538 71.3473 -0.141683 69.5951 0.0208025 68.0555C0.183288 66.516 1.31214 64.9679 3.46721 64.9679C5.89595 64.9679 7.34121 63.2582 7.70894 61.557C8.06812 59.8644 7.44384 57.7124 5.23745 56.7342C3.33893 55.9006 2.96265 54.2079 3.24486 52.898C3.54418 51.5116 4.67303 50.1166 6.53734 50.1166C6.811 50.1166 7.08466 50.1506 7.36687 50.2102C7.69184 50.2782 8.02536 50.3123 8.34178 50.3123C10.1719 50.3123 11.8053 49.1044 12.3868 47.3012C12.9769 45.4809 12.3697 43.6522 10.8047 42.5209C9.52194 41.5937 9.04304 40.1818 9.51339 38.7273C9.91533 37.4939 11.0784 36.2435 12.7546 36.2435C13.2506 36.2435 13.7551 36.3541 14.2511 36.5753C14.8498 36.839 15.4569 36.975 16.0641 36.975C17.7403 36.975 19.2796 35.9458 19.9894 34.3552C20.6907 32.7816 20.4085 31.072 19.2283 29.7706C18.262 28.7073 18.0311 27.3804 18.5869 26.1301C19.1685 24.8372 20.4512 23.9611 21.7939 23.9611C22.5122 23.9611 23.1964 24.1992 23.8463 24.667C24.616 25.2284 25.4969 25.5176 26.3863 25.5176C27.8999 25.5176 29.388 24.6415 30.1747 23.2891C30.9444 21.9537 30.9273 20.4056 30.132 19.0361C29.4735 17.9049 29.4478 16.7055 30.0636 15.6508C30.705 14.5365 31.9364 13.8135 33.1936 13.8135C34.1428 13.8135 35.0151 14.2218 35.7164 14.9959C36.563 15.9315 37.6833 16.4419 38.8806 16.4419C40.2745 16.4419 41.6514 15.7359 42.4809 14.6046C43.2848 13.4988 43.49 12.1379 43.0453 10.7855C42.6861 9.67969 42.8315 8.59944 43.4558 7.74035C44.1314 6.82171 45.2688 6.24331 46.4319 6.24331C47.6548 6.24331 48.6896 6.86424 49.3481 7.99552C50.1434 9.37348 51.486 10.1645 53.0339 10.1645C54.291 10.1645 55.5311 9.62015 56.3606 8.71002C57.1645 7.82541 57.5237 6.6516 57.3954 5.40123C57.2842 4.36351 57.5579 3.44488 58.1993 2.7474C58.8577 2.01589 59.8754 1.58209 60.9102 1.58209C62.4495 1.58209 63.6553 2.47521 64.1428 3.97224C64.7414 5.80951 66.3064 6.9493 68.2306 6.9493C70.1206 6.9493 72.2756 5.74147 72.5579 3.09614C72.7802 0.961164 74.4478 0 76.0043 0C77.5607 0 79.2283 0.952658 79.4507 3.09614C79.7243 5.74997 81.888 6.9493 83.7779 6.9493C85.7021 6.9493 87.2671 5.80951 87.8657 3.97224C88.3532 2.47521 89.559 1.58209 91.0983 1.58209C92.1417 1.58209 93.1508 2.01589 93.8093 2.7474C94.4421 3.44488 94.7158 4.36351 94.6132 5.40123C94.4763 6.6516 94.8441 7.82541 95.6479 8.71002C96.4775 9.62015 97.7175 10.1645 98.9746 10.1645C100.523 10.1645 101.865 9.37348 102.66 7.99552C103.319 6.86424 104.354 6.24331 105.577 6.24331C106.74 6.24331 107.877 6.81321 108.553 7.74035C109.186 8.59944 109.331 9.68819 108.963 10.7855C108.519 12.1464 108.715 13.4988 109.528 14.6046C110.357 15.7359 111.734 16.4419 113.128 16.4419C114.325 16.4419 115.446 15.9315 116.292 14.9959C116.993 14.2218 117.866 13.8135 118.815 13.8135C120.072 13.8135 121.304 14.5365 121.945 15.6423C122.552 16.697 122.535 17.8964 121.877 19.0276C121.081 20.3971 121.064 21.9537 121.834 23.2806C122.621 24.633 124.109 25.5091 125.622 25.5091C126.512 25.5091 127.393 25.2114 128.162 24.6585C128.812 24.1907 129.505 23.9525 130.215 23.9525C131.549 23.9525 132.84 24.8201 133.422 26.1215C133.977 27.3719 133.747 28.6988 132.78 29.7621C131.6 31.0635 131.309 32.7816 132.019 34.3467C132.729 35.9373 134.268 36.9665 135.944 36.9665C136.552 36.9665 137.167 36.8304 137.757 36.5668C138.262 36.3456 138.767 36.235 139.254 36.235C140.93 36.235 142.093 37.4854 142.495 38.7188C142.965 40.1733 142.487 41.5937 141.204 42.5124C139.63 43.6437 139.023 45.4809 139.622 47.2927C140.212 49.0959 141.837 50.3037 143.667 50.3037C143.992 50.3037 144.317 50.2697 144.642 50.2017C144.924 50.1421 145.206 50.1081 145.471 50.1081C147.336 50.1081 148.473 51.5031 148.764 52.8895C149.037 54.1909 148.661 55.8836 146.771 56.7257C144.556 57.7039 143.932 59.8473 144.3 61.5485C144.659 63.2412 146.113 64.9594 148.524 64.9594C150.696 64.9594 151.825 66.516 151.979 68.047C152.142 69.5866 151.355 71.3388 149.251 71.7811C146.882 72.2829 145.822 74.2563 145.822 75.9915C145.822 77.7267 146.882 79.7001 149.251 80.2019C151.363 80.6442 152.142 82.3964 151.979 83.936C151.817 85.4755 150.688 87.0236 148.533 87.0236C146.104 87.0236 144.659 88.7333 144.291 90.4345C143.932 92.1271 144.556 94.2791 146.763 95.2573C148.661 96.0909 149.037 97.7835 148.755 99.0935C148.456 100.48 147.327 101.875 145.463 101.875C145.189 101.875 144.915 101.841 144.633 101.781C144.308 101.713 143.975 101.679 143.658 101.679C141.828 101.679 140.195 102.887 139.613 104.69C139.023 106.511 139.63 108.339 141.195 109.471C142.478 110.398 142.957 111.81 142.487 113.264C142.085 114.498 140.922 115.748 139.245 115.748C138.749 115.748 138.245 115.637 137.749 115.416C137.15 115.153 136.543 115.016 135.936 115.016C134.26 115.016 132.72 116.046 132.011 117.636C131.309 119.21 131.591 120.92 132.772 122.221C133.738 123.284 133.969 124.611 133.413 125.861C132.832 127.154 131.549 128.03 130.206 128.03C129.488 128.03 128.804 127.792 128.154 127.324C127.384 126.763 126.503 126.474 125.614 126.474C124.1 126.474 122.612 127.35 121.825 128.702C121.056 130.038 121.073 131.586 121.868 132.955C122.526 134.087 122.552 135.286 121.936 136.341C121.295 137.455 120.064 138.178 118.806 138.178C117.857 138.178 116.985 137.77 116.284 136.996C115.437 136.06 114.317 135.55 113.119 135.55C111.725 135.55 110.349 136.256 109.519 137.387C108.715 138.493 108.51 139.854 108.955 141.206C109.314 142.312 109.168 143.392 108.544 144.251C107.869 145.17 106.731 145.748 105.568 145.748C104.345 145.748 103.31 145.127 102.652 143.996C101.857 142.618 100.514 141.827 98.9661 141.827C97.7089 141.827 96.4689 142.371 95.6394 143.281C94.8355 144.166 94.4763 145.34 94.6046 146.59C94.7158 147.628 94.4421 148.547 93.8007 149.244C93.1422 149.976 92.1246 150.409 91.0898 150.409C89.5505 150.409 88.3446 149.516 87.8572 148.019C87.2585 146.182 85.6935 145.042 83.7694 145.042C81.8794 145.042 79.7243 146.25 79.4421 148.895C79.2198 151.039 77.5522 152 75.9957 152Z"></path>
      </svg>
    ),
    shape12: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M50 15.3L99 1 84.7 50 99 99 50 84.7 1 99l14.3-49L1 1l49 14.3z"></path>
      </svg>
    ),
    shape13: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M99.5 99.5V.5L50 10.5.5.5v99l49.5-10 49.5 10z"></path>
      </svg>
    ),
    shape14: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M99.5 30.5h-15v-15h-15V.5h-39v15h-15v15H.5v39h15v15h15v15h39v-15h15v-15h15v-39z"></path>
      </svg>
    ),
    shape15: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M40.8 8.5c5-10.7 13.3-10.7 18.4 0S77.7 29.4 89 31.1s13.9 10 5.7 18.3S81.4 74.2 83.3 86s-4.8 16.9-14.9 11.3-26.7-5.6-36.8 0-16.9.5-15-11.3-3.1-28.3-11.3-36.6S-.3 32.8 11 31.1 35.7 19.2 40.8 8.5z"></path>
      </svg>
    ),
    shape16: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M61.2 84.8c6.3 19.6-28.7 19.6-22.4 0-26.7 19.6-43.2 3-23.6-23.6-19.6 6.3-19.6-28.7 0-22.4-19.6-26.7-3-43.2 23.6-23.6-6.3-19.6 28.7-19.6 22.4 0 26.7-19.6 43.2-3 23.6 23.6 19.6-6.3 19.6 28.7 0 22.4 19.6 26.7 3 43.2-23.6 23.6z"></path>
      </svg>
    ),
    shape17: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M99.5 31.5A30 30 0 0050 8.7 30 30 0 007.3 50.5 30 30 0 0050 92.3a30 30 0 0042.7-41.8 29.9 29.9 0 006.8-19z"></path>
      </svg>
    ),
    shape18: (
      <svg
        className="sqs-shape"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M50 100c-6 0-12-1-17-3-5-2-9-5-10-8-1-2-1-5 0-8 1-3 4-7 7-11l0 0c-6 6-11 8-15 8h0c-4 0-8-2-10-7-3-5-5-13-5-21 0-8 2-16 5-21 3-5 6-7 10-7 4 0 10 3 15 8l0 0c-4-4-6-7-7-11-1-3-1-6 0-8 1-4 5-6 10-8 5-2 11-3 17-3 6 0 12 1 17 3 5 2 9 5 10 8 1 2 1 5 0 8-1 3-4 7-7 11l0 0c6-6 11-8 15-8 5 0 9 3 11 9 2 5 4 12 4 20 0 7-1 14-4 20-3 6-7 9-11 9h0c-4 0-10-3-15-8l0 0c4 4 6 7 7 11 1 3 1 6 0 8-1 4-5 6-10 8-5 2-11 3-17 3Z"></path>
      </svg>
    ),
    shape19: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 150 100"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M100 .5a49.3 49.3 0 00-25 6.8 49.5 49.5 0 100 85.4A49.5 49.5 0 10100 .5z"></path>
      </svg>
    ),
    shape20: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 150 100"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M119.5.5a29.9 29.9 0 00-22.3 9.9 30 30 0 00-44.5 0A30 30 0 00.5 30.5v39a30 30 0 0052.3 20.1 30 30 0 0044.5 0 30 30 0 0052.2-20.1v-39a30 30 0 00-30-30z"></path>
      </svg>
    ),
    shape21: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 150 100"
        preserveAspectRatio="none"
        style={style}
      >
        <ellipse
          cx="75"
          cy="50"
          rx="78"
          ry="43.7"
          transform="rotate(-21.1 75 50)"
        ></ellipse>
      </svg>
    ),
    shape22: (
      <svg
        className="sqs-shape"
        viewBox="0 0 241 111"
        xmlns="http://www.w3.org/2000/svg"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M56.1335 0H240.266L184.132 111H0L56.1335 0Z"></path>
      </svg>
    ),
    shape23: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 100 150"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M50 .5A49.5 49.5 0 00.5 50v99.5h99V50A49.5 49.5 0 0050 .5z"></path>
      </svg>
    ),
    shape24: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 100 150"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M88.2 18.5a49.4 49.4 0 00-76.3 0H.6v131h99v-131z"></path>
      </svg>
    ),
    shape25: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 100 150"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M99.5 49.9a49.4 49.4 0 10-89.1 29.4H.5v70.2h99V79.3h-9.7A49.1 49.1 0 0099.5 50z"></path>
      </svg>
    ),
    shape26: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 100 150"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M68 150c-6-25-30-25-36 0-6-25-15-35-31-32 16-14 16-72 0-86 16 3 25-7 31-31 6 24 30 24 36 0 6 24 15 34 32 31-17 14-17 72 0 86-17-3-26 7-32 32z"></path>
      </svg>
    ),
    shape27: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 100 150"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M50 149.5h0c-13.5.3-26.4-8.9-35.4-19.5A58 58 0 01.5 90.8h0a152.6 152.6 0 0112.3-55C21 17.2 34 0 50 .5h0C66 0 79 17.2 87.2 35.8a152.6 152.6 0 0112.3 55h0c.2 15.8-5 28.2-14.1 39.2-9 10.6-22 19.8-35.4 19.5z"></path>
      </svg>
    ),
    shape28: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 100 150"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M99.5 30.5h0V.5H.5v30h0A49.5 49.5 0 0029.4 75 49.5 49.5 0 00.5 119.5h0v30h99v-30h0A49.5 49.5 0 0070.6 75a49.5 49.5 0 0028.9-44.5z"></path>
      </svg>
    ),
    shape29: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="sqs-shape"
        viewBox="0 0 100 150"
        preserveAspectRatio="none"
        style={style}
      >
        <path d="M79.2 149.5H20.8L1 .5h98l-19.8 149z"></path>
      </svg>
    ),
  };
  if (id) {
    return shapes[id];
  } else {
    return shapes;
  }
}
