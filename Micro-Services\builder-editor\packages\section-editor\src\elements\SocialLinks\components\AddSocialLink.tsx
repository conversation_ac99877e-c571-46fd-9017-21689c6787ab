import React, { <PERSON><PERSON><PERSON> } from "react";
import { FormattedMessage } from "react-intl";
import { SocialLinksSettingsViews } from "./SocialLinksSettings";
import { Stack, Button } from "@chakra-ui/react";
import { AddIcon } from "@wuilt/react-icons";

interface AddSocialLinkProps {
  setView: Dispatch<React.SetStateAction<SocialLinksSettingsViews>>;
}
const AddSocialLink: React.FC<AddSocialLinkProps> = ({ setView }) => {
  return (
    <Stack gap="16px" maxWidth="fit-content">
      <Button
        variant="plain"
        color="primary.500"
        size="sm"
        paddingInline={0}
        leftIcon={<AddIcon size="20px" />}
        onClick={() => {
          setView(SocialLinksSettingsViews.ChooseIcon);
        }}
      >
        <FormattedMessage defaultMessage="Add social link" id="FH4TgN" />
      </Button>
    </Stack>
  );
};

export default AddSocialLink;
