import React, { <PERSON><PERSON><PERSON> } from "react";
import { SocialLinksSettingsViews } from "./SocialLinksSettings";
import styled from "styled-components";
import {
  SocialLinkSettings,
  SocialLinkName,
  SocialLinksIcons,
} from "@wuilt/section-preview";

import { nanoid } from "nanoid";
import SingleIcon from "./SingleIcon";
export const SocialLinks: any = [
  {
    Icon: SocialLinksIcons.BehanceFilled,
    name: SocialLinkName.Behance,
    titleName: "Behance",
    id: 1,
  },
  {
    Icon: SocialLinksIcons.DiscordFilled,
    name: SocialLinkName.Discord,
    titleName: "Discord",
    id: 2,
  },
  {
    Icon: SocialLinksIcons.DribbbleFilled,
    name: SocialLinkName.Dribbble,
    titleName: "Dribbble",
    id: 3,
  },
  {
    Icon: SocialLinksIcons.FacebookFilled,
    name: SocialLinkName.Facebook,
    titleName: "Facebook",
    id: 4,
  },
  {
    Icon: SocialLinksIcons.FacebookMessengerFilled,
    name: SocialLinkName.Messenger,
    titleName: "Messenger",
    id: 5,
  },
  {
    Icon: SocialLinksIcons.GithubFilled,
    name: SocialLinkName.Github,
    titleName: "Github",
    id: 6,
  },
  {
    Icon: SocialLinksIcons.InstagramFilled,
    name: SocialLinkName.Instagram,
    titleName: "Instagram",
    id: 7,
  },
  {
    Icon: SocialLinksIcons.LinkedinFilled,
    name: SocialLinkName.Linkedin,
    titleName: "Linkedin",
    id: 8,
  },
  {
    Icon: SocialLinksIcons.PinterestFilled,
    name: SocialLinkName.Pinterest,
    titleName: "Pinterest",
    id: 9,
  },
  {
    Icon: SocialLinksIcons.SkypeFilled,
    name: SocialLinkName.Skype,
    titleName: "Skype",
    id: 10,
  },
  {
    Icon: SocialLinksIcons.TelegramFilled,
    name: SocialLinkName.Telegram,
    titleName: "Telegram",
    id: 11,
  },
  {
    Icon: SocialLinksIcons.TiktokFilled,
    name: SocialLinkName.Tiktok,
    titleName: "Tiktok",
    id: 12,
  },
  {
    Icon: SocialLinksIcons.TwitterFilled,
    name: SocialLinkName.Twitter,
    titleName: "TwitterX",
    id: 13,
  },
  {
    Icon: SocialLinksIcons.WhatsappFilled,
    name: SocialLinkName.Whatsapp,
    titleName: "Whatsapp",
    id: 14,
  },
  {
    Icon: SocialLinksIcons.YoutubeFilled,
    name: SocialLinkName.Youtube,
    titleName: "Youtube",
    id: 15,
  },
  {
    Icon: SocialLinksIcons.SnapchatFilled,
    name: SocialLinkName.Snapchat,
    titleName: "Snapchat",
    id: 16,
  },
];

interface ChooseIconProps {
  setView: Dispatch<React.SetStateAction<SocialLinksSettingsViews>>;
  addSocialLinkIcon?: (arg: SocialLinkSettings) => void;
}
const ChooseIcon: React.FC<ChooseIconProps> = ({
  setView,
  addSocialLinkIcon,
}) => {
  return (
    <ChooseIconsContainer>
      {SocialLinks.map(({ Icon, name, titleName, id }) => (
        <SingleIcon
          key={id}
          Icon={Icon}
          text={titleName}
          onClick={() => {
            addSocialLinkIcon({
              id: `SocialIcon:${nanoid()}`,
              name: name,
            });
            setView(SocialLinksSettingsViews.List);
          }}
        />
      ))}
    </ChooseIconsContainer>
  );
};

export default ChooseIcon;

const ChooseIconsContainer = styled.div`
  padding-top: 20px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
  height: 250px;
  overflow-y: scroll;
  padding-inline-end: 10px;
`;
