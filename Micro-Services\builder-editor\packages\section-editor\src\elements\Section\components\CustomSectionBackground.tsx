import React from "react";
import ColumnBackground from "../../Column/components/ColumnBackground";
import { Settings } from "@wuilt/section-preview";

interface CustomSectionBackgroundProps {
  settings: Settings | undefined;
  updateSettings: (v: Settings) => void;
  onUploadImage: (cb: (src: string) => void) => void;
}

const CustomSectionBackground: React.FC<CustomSectionBackgroundProps> = ({
  settings,
  updateSettings,
  onUploadImage,
}) => {
  return (
    <ColumnBackground
      settings={settings!}
      updateSettings={updateSettings}
      onUploadImage={onUploadImage}
      source="SECTION"
    />
  );
};

export default CustomSectionBackground;
