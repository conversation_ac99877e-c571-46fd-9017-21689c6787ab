import { utils, Drag<PERSON>andle, ExpandIcon } from "@wuilt/quilt";
import React from "react";
import { FormattedMessage } from "react-intl";
import styled from "styled-components";
import ElementActions from "./ElementActions";
import ElementSettings, { StyledIconWrapper } from "./ElementSettings";
import {
  SectionElement,
  TextObject,
  WuiltContext,
} from "@wuilt/section-preview";
import { Text, Tooltip } from "@chakra-ui/react";

interface ElementSettingsMenuProps {
  pages: any[];
  element: SectionElement;
  duplicateElement: () => void;
  deleteElement: () => void;
  updateElementUi: (element: SectionElement) => void;
  mutateElementApi: (element: SectionElement) => void;
  onClosePopup: () => void;
  onUploadImage: (cb: (src: string) => void) => void;
  appDirection: string;
  text: TextObject;
  updateTextApi?: (newTexts?: TextObject) => void;
  updateTextUi?: (newTexts?: TextObject) => void;
  wuiltContext?: WuiltContext;
  openSettingsPopup?: boolean;
  setOpenSettingsPopup: React.Dispatch<React.SetStateAction<boolean>>;
}

const ElementSettingsMenu: React.FC<ElementSettingsMenuProps> = ({
  pages,
  element,
  deleteElement,
  duplicateElement,
  updateElementUi,
  mutateElementApi,
  onClosePopup,
  onUploadImage,
  appDirection,
  text,
  updateTextApi,
  updateTextUi,
  wuiltContext,
  openSettingsPopup,
  setOpenSettingsPopup,
}) => {
  return (
    <StyledSettingsButtons
      appDirection={appDirection}
      data-test="ElementSettingsMenu"
      isTextOrFormElement={element?.type === "Text" || element?.type === "Form"}
    >
      <StyledIconWrapper>
        <Tooltip
          hasArrow
          placement="top"
          backgroundColor="gray.900"
          label={
            <Text variant="textXs" fontWeight="semibold" color="white">
              <FormattedMessage defaultMessage="Move element" id="boxhyT" />
            </Text>
          }
        >
          <span>
            <DragHandle
              id={element?.id}
              DragIcon={<ExpandIcon />}
              buttonIconProps={{ color: "white", stopOpacity: true }}
            />
          </span>
        </Tooltip>
      </StyledIconWrapper>

      <ElementSettings
        pages={pages}
        element={element}
        wuiltContext={wuiltContext}
        openSettingsPopup={openSettingsPopup}
        setOpenSettingsPopup={setOpenSettingsPopup}
        updateElementUi={updateElementUi}
        mutateElementApi={mutateElementApi}
        onClosePopup={onClosePopup}
        onUploadImage={onUploadImage}
        text={text}
        updateTextApi={updateTextApi}
        updateTextUi={updateTextUi}
      />

      <StyledIconWrapper>
        <Tooltip
          hasArrow
          placement="top"
          backgroundColor="gray.900"
          label={
            <Text variant="textXs" fontWeight="semibold" color="white">
              <FormattedMessage defaultMessage="Actions" id="wL7VAE" />
            </Text>
          }
        >
          <span>
            <ElementActions
              duplicateElement={duplicateElement}
              deleteElement={deleteElement}
              element={element}
              wuiltContext={wuiltContext}
            />
          </span>
        </Tooltip>
      </StyledIconWrapper>
    </StyledSettingsButtons>
  );
};

export default ElementSettingsMenu;

export const StyledSettingsButtons = styled.div<{
  isTextOrFormElement: boolean;
  appDirection: string;
}>`
  width: ${({ isTextOrFormElement }) =>
    isTextOrFormElement
      ? "75px"
      : "100px"}; // No settings icon for Text and Form elements
  background-color: white;
  border: solid 1px #0e9384;
  border-radius: ${utils.borderRadius("0 0 0 4px")};
  direction: ${(props) => props?.appDirection};
  position: absolute;
  ${utils.right}: -5px;
  top: -5px;
  display: flex;
  align-items: stretch;
  z-index: 150;
  height: 24px;
`;
