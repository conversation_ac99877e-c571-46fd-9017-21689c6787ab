import React, { <PERSON><PERSON><PERSON>, SetStateAction } from "react";
import { FormattedMessage } from "react-intl";
import { FormSettingsViews } from "../FormSettings";
import { FormSettings } from "@wuilt/section-preview";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  ExternalLinkIcon,
} from "@wuilt/react-icons";
import { Button, Divider, VStack, Text } from "@chakra-ui/react";
import InputField from "../../../../components/InputField";
import { Link } from "../../../../components/Link";

interface FormSettingsProps {
  siteId: string;
  isAppRtl: boolean;
  formSettings: FormSettings;
  setView: Dispatch<SetStateAction<FormSettingsViews>>;
  updateSettings: (formSettings: FormSettings) => void;
}
const FormTab: React.FC<FormSettingsProps> = ({
  siteId,
  isAppRtl,
  formSettings,
  setView,
  updateSettings,
}) => {
  return (
    <VStack gap="12px" padding="16px">
      <InputField
        label={<FormattedMessage defaultMessage="Form name" id="fwSnLB" />}
        placeholder={
          <FormattedMessage defaultMessage="Form name" id="fwSnLB" />
        }
        hint={
          <FormattedMessage
            defaultMessage="Use descriptive form name to identify form submissions"
            id="NUa2NG"
          />
        }
        value={formSettings?.formName}
        onChange={(event) => updateSettings({ formName: event.target.value })}
      />
      <Divider />
      <Button
        size="sm"
        width="full"
        variant="linkGray"
        justifyContent="space-between"
        onClick={() => {
          setView(FormSettingsViews.FormButton);
        }}
        rightIcon={
          isAppRtl ? (
            <ChevronLeftIcon size="20px" color="gray.500" />
          ) : (
            <ChevronRightIcon size="20px" color="gray.500" />
          )
        }
      >
        <Text variant="textSm" fontWeight="medium" color="gray.700">
          <FormattedMessage defaultMessage="Form action button" id="4IT7Jk" />
        </Text>
      </Button>
      <Divider />
      <Button
        size="sm"
        width="full"
        variant="linkGray"
        justifyContent="space-between"
        onClick={() => {
          setView(FormSettingsViews.PostSubmit);
        }}
        rightIcon={
          isAppRtl ? (
            <ChevronLeftIcon size="20px" color="gray.500" />
          ) : (
            <ChevronRightIcon size="20px" color="gray.500" />
          )
        }
      >
        <Text variant="textSm" fontWeight="medium" color="gray.700">
          <FormattedMessage defaultMessage="Post-submit" id="qmnDsn" />
        </Text>
      </Button>
      <Divider />
      <Link
        width="full"
        to={`/site/${siteId}/settings/forms/${formSettings?.formId}`}
        target="_blank"
      >
        <Button
          size="sm"
          width="full"
          variant="linkGray"
          justifyContent="space-between"
          rightIcon={<ExternalLinkIcon size="20px" color="gray.500" />}
        >
          <Text variant="textSm" fontWeight="medium" color="gray.700">
            <FormattedMessage defaultMessage="Manage submission" id="iTclct" />
          </Text>
        </Button>
      </Link>
    </VStack>
  );
};

export default FormTab;
