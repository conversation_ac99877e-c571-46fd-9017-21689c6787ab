import { Box, InputField, Label, Text } from "@wuilt/quilt";
import { BorderRadius } from "@wuilt/section-preview";
import { FormattedMessage } from "react-intl";
interface SelectedBorderRadiusProps {
  onChange: (v: BorderRadius) => void;
  borderRadius: BorderRadius | undefined;
}

function AllCorners({ onChange, borderRadius }: SelectedBorderRadiusProps) {
  return (
    <Box mt="10px">
      <Label>
        <FormattedMessage defaultMessage="Radius" id="nDRMt4" />
      </Label>
      <InputField
        value={borderRadius?.borderRadiusAll || 1}
        onChange={(value: any) => {
          onChange({
            ...borderRadius,
            borderRadiusAll: value,
            borderBottomLeftRadius: value,
            borderBottomRightRadius: value,
            borderTopLeftRadius: value,
            borderTopRightRadius: value,
          });
        }}
        width="50%"
        prefix={<Text fontSize="medium" children="px" />}
        type="number"
      />
    </Box>
  );
}

export default AllCorners;
