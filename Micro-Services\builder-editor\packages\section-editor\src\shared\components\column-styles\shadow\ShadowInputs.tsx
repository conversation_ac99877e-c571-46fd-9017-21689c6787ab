import { Box, InputColor, InputField, Label, Stack, Text } from "@wuilt/quilt";
import { ShadowValues } from "@wuilt/section-preview";
import React from "react";
import { FormattedMessage } from "react-intl";

interface ShadowInputsProps {
  values: ShadowValues;
  onChange: (values: ShadowValues) => void;
}
const ShadowInputs: React.FC<ShadowInputsProps> = ({ values, onChange }) => {
  return (
    <div>
      <Stack direction="row" mb="15px">
        <Box>
          <Label>
            <FormattedMessage defaultMessage="Horizontal" id="NfU3/O" />
          </Label>
          <InputField
            prefix={<Text fontSize="medium" children="px" />}
            type="number"
            value={values?.horizontalOffset || 0}
            onChange={(horizontalOffset: any) => {
              onChange({ ...values, horizontalOffset });
            }}
          />
        </Box>
        <Box>
          <Label>
            <FormattedMessage defaultMessage="Vertical" id="cLrroF" />
          </Label>
          <InputField
            prefix={<Text fontSize="medium" children="px" />}
            type="number"
            value={values?.verticalOffset || 0}
            onChange={(verticalOffset: any) => {
              onChange({ ...values, verticalOffset });
            }}
          />
        </Box>
      </Stack>
      <Stack direction="row" mb="15px">
        <Box>
          <Label>
            <FormattedMessage defaultMessage="Blur" id="NdI3M5" />
          </Label>
          <InputField
            prefix={<Text fontSize="medium" children="px" />}
            type="number"
            value={values?.blur || 0}
            onChange={(blur: any) => {
              onChange({ ...values, blur });
            }}
          />
        </Box>
        <Box>
          <Label>
            <FormattedMessage defaultMessage="Spread" id="B7hDsY" />
          </Label>
          <InputField
            prefix={<Text fontSize="medium" children="px" />}
            type="number"
            value={values?.spread || 0}
            onChange={(spread: any) => {
              onChange({ ...values, spread });
            }}
          />
        </Box>
      </Stack>
      <Box>
        <Label>
          <FormattedMessage defaultMessage="Shadow color" id="YQ8gk0" />
        </Label>
        <InputColor
          value={values?.color || "#000000"}
          onChange={(color) => {
            onChange({
              ...values,
              color,
            });
          }}
        />
      </Box>
    </div>
  );
};

export default ShadowInputs;
