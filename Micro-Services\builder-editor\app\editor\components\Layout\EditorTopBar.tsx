'use client'

import {
  Box,
  Flex,
  HStack,
  Button,
  IconButton,
  Text,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Divider,
  useColorModeValue,
  Badge,
  Tooltip
} from '@chakra-ui/react'
import {
  ChevronDownIcon,
  ViewIcon,
  SettingsIcon,
  ExternalLinkIcon,
  CopyIcon,
  DownloadIcon,
  RepeatIcon,
  ArrowBackIcon,
  CheckIcon,
  WarningIcon,
  ArrowLeftIcon,
  ArrowRightIcon
} from '@chakra-ui/icons'
import { useEditorStore } from '@/lib/stores/editorStore'
import { useLanguage, useTranslation } from '@/lib/contexts/LanguageContext'
import { useState } from 'react'
import { useToast } from '@chakra-ui/react'
import { PageManager } from '../PageManager/PageManager'
import { LanguageSwitcher } from '../Language/LanguageSwitcher'
import { WebsiteSettingsModal } from '../WebsiteSettings/WebsiteSettingsModal'

export function EditorTopBar() {
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle')
  const [showWebsiteSettings, setShowWebsiteSettings] = useState(false)
  const [websiteSettings, setWebsiteSettings] = useState({
    colors: {
      primary: '#1f9aeb',
      secondary: '#6c757d',
      accent: '#28a745',
      background: '#ffffff',
      text: '#343a40'
    },
    fonts: {
      heading: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      body: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    },
    buttons: {
      style: 'rounded' as const,
      size: 'md' as const,
      variant: 'solid' as const
    },
    logo: {
      url: '',
      alt: '',
      width: '200px',
      height: '60px'
    },
    favicon: {
      url: ''
    }
  })
  const toast = useToast()
  const { t } = useTranslation()
  const { uiDirection } = useLanguage()

  const {
    currentPage,
    isPreviewMode,
    setPreviewMode,
    currentBreakpoint,
    setCurrentBreakpoint,
    undo,
    redo,
    canUndo,
    canRedo
  } = useEditorStore()

  // Old Builder exact colors
  const bgColor = 'white'
  const borderColor = '#dfe3e8'
  const textColor = '#343a40'

  const handlePreview = () => {
    setPreviewMode(!isPreviewMode)
  }

  const handlePublish = () => {
    // TODO: Implement publish functionality
    console.log('Publishing site...')
  }

  const handleSave = async () => {
    if (!currentPage || saveStatus === 'saving') return

    setSaveStatus('saving')

    try {
      const response = await fetch('/api/pages/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pageId: currentPage.id,
          pageData: currentPage,
          timestamp: new Date().toISOString()
        })
      })

      if (!response.ok) {
        throw new Error(`Save failed: ${response.statusText}`)
      }

      const result = await response.json()
      setSaveStatus('saved')

      toast({
        title: 'Page saved',
        description: 'Your changes have been saved successfully',
        status: 'success',
        duration: 2000,
        isClosable: true,
      })

      // Reset to idle after 2 seconds
      setTimeout(() => setSaveStatus('idle'), 2000)

    } catch (error) {
      console.error('Save failed:', error)
      setSaveStatus('error')

      toast({
        title: 'Save failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })

      // Reset to idle after 3 seconds
      setTimeout(() => setSaveStatus('idle'), 3000)
    }
  }

  return (
    <Box
      bg={bgColor}
      borderBottom="1px solid"
      borderColor={borderColor}
      px={0}
      py={0}
      zIndex={10}
      position="relative"
      boxShadow="0 1px 3px rgba(0,0,0,0.1)"
    >
      <Flex justify="space-between" align="center" h="50px">
        {/* Left Section - Logo, Pages, Languages */}
        <HStack spacing={0} flex="1">
          {/* Logo Button */}
          <Box
            px="16px"
            h="50px"
            display="flex"
            alignItems="center"
            borderRight="1px solid"
            borderColor={borderColor}
          >
            <Text fontSize="lg" fontWeight="bold" color="#1f9aeb">
              🏗️
            </Text>
          </Box>

          {/* Divider */}
          <Box w="1px" h="50px" bg={borderColor} />

          {/* Pages Button */}
          <Box
            px="16px"
            h="50px"
            display="flex"
            alignItems="center"
            borderRight="1px solid"
            borderColor={borderColor}
          >
            <PageManager />
          </Box>

          {/* Divider */}
          <Box w="1px" h="50px" bg={borderColor} />

          {/* Languages Button */}
          <Box
            px="16px"
            h="50px"
            display="flex"
            alignItems="center"
            borderRight="1px solid"
            borderColor={borderColor}
          >
            <HStack spacing={2}>
              <LanguageSwitcher type="ui" />
              <LanguageSwitcher type="content" />
            </HStack>
          </Box>

          {/* Divider */}
          <Box w="1px" h="50px" bg={borderColor} />
        </HStack>

        {/* Center Section - Device Controls (Old Builder Style) */}
        <HStack spacing={1} bg="#f8f9fa" borderRadius="4px" p="2px">
          <Button
            size="sm"
            variant="ghost"
            borderRadius="2px"
            px="12px"
            py="6px"
            fontSize="xs"
            fontWeight="600"
            bg={currentBreakpoint === 'desktop' ? '#1f9aeb' : 'transparent'}
            color={currentBreakpoint === 'desktop' ? 'white' : '#6c757d'}
            _hover={{
              bg: currentBreakpoint === 'desktop' ? '#177ab8' : '#e9ecef',
            }}
            onClick={() => setCurrentBreakpoint('desktop')}
            leftIcon={<Box w="14px" h="10px" bg="currentColor" borderRadius="1px" />}
          >
            Desktop
          </Button>
          <Button
            size="sm"
            variant="ghost"
            borderRadius="2px"
            px="12px"
            py="6px"
            fontSize="xs"
            fontWeight="600"
            bg={currentBreakpoint === 'tablet' ? '#1f9aeb' : 'transparent'}
            color={currentBreakpoint === 'tablet' ? 'white' : '#6c757d'}
            _hover={{
              bg: currentBreakpoint === 'tablet' ? '#177ab8' : '#e9ecef',
            }}
            onClick={() => setCurrentBreakpoint('tablet')}
            leftIcon={<Box w="10px" h="14px" bg="currentColor" borderRadius="1px" />}
          >
            Tablet
          </Button>
          <Button
            size="sm"
            variant="ghost"
            borderRadius="2px"
            px="12px"
            py="6px"
            fontSize="xs"
            fontWeight="600"
            bg={currentBreakpoint === 'mobile' ? '#1f9aeb' : 'transparent'}
            color={currentBreakpoint === 'mobile' ? 'white' : '#6c757d'}
            _hover={{
              bg: currentBreakpoint === 'mobile' ? '#177ab8' : '#e9ecef',
            }}
            onClick={() => setCurrentBreakpoint('mobile')}
            leftIcon={<Box w="6px" h="14px" bg="currentColor" borderRadius="1px" />}
          >
            Mobile
          </Button>
        </HStack>

        {/* Right Section - Actions (Old Builder Style) */}
        <HStack spacing={0} flex="1" justify="end">
          {/* Divider */}
          <Box w="1px" h="50px" bg={borderColor} />

          {/* Undo/Redo */}
          <Box
            px="12px"
            h="50px"
            display="flex"
            alignItems="center"
            borderRight="1px solid"
            borderColor={borderColor}
          >
            <HStack spacing={1}>
              <Tooltip label={t('editor.undo')}>
                <IconButton
                  aria-label={t('editor.undo')}
                  icon={uiDirection === 'rtl' ? <ArrowRightIcon /> : <ArrowLeftIcon />}
                  size="sm"
                  variant="ghost"
                  isDisabled={!canUndo}
                  onClick={undo}
                  borderRadius="4px"
                  color="#6c757d"
                  _hover={{ bg: '#f8f9fa', color: '#343a40' }}
                />
              </Tooltip>
              <Tooltip label={t('editor.redo')}>
                <IconButton
                  aria-label={t('editor.redo')}
                  icon={uiDirection === 'rtl' ? <ArrowLeftIcon /> : <ArrowRightIcon />}
                  size="sm"
                  variant="ghost"
                  isDisabled={!canRedo}
                  onClick={redo}
                  borderRadius="4px"
                  color="#6c757d"
                  _hover={{ bg: '#f8f9fa', color: '#343a40' }}
                />
              </Tooltip>
            </HStack>
          </Box>

          {/* Divider */}
          <Box w="1px" h="50px" bg={borderColor} />

          {/* Upgrade Button */}
          <Box
            px="16px"
            h="50px"
            display="flex"
            alignItems="center"
            borderRight="1px solid"
            borderColor={borderColor}
          >
            <Button
              size="sm"
              variant="outline"
              borderColor="#ffc107"
              color="#ffc107"
              bg="white"
              borderRadius="4px"
              fontWeight="600"
              fontSize="sm"
              px="12px"
              py="6px"
              _hover={{
                bg: '#fff3cd',
                borderColor: '#ffb300',
                color: '#ffb300',
              }}
            >
              Upgrade
            </Button>
          </Box>

          {/* Divider */}
          <Box w="1px" h="50px" bg={borderColor} />

          {/* Preview Button */}
          <Box
            px="16px"
            h="50px"
            display="flex"
            alignItems="center"
            borderRight="1px solid"
            borderColor={borderColor}
          >
            <Button
              leftIcon={<ViewIcon />}
              size="sm"
              variant="outline"
              borderColor={borderColor}
              color="#6c757d"
              bg="white"
              borderRadius="4px"
              fontWeight="600"
              fontSize="sm"
              px="12px"
              py="6px"
              onClick={handlePreview}
              _hover={{
                bg: '#f8f9fa',
                borderColor: '#1f9aeb',
                color: '#1f9aeb',
              }}
            >
              {isPreviewMode ? 'Edit' : t('editor.preview')}
            </Button>
          </Box>

          {/* Divider */}
          <Box w="1px" h="50px" bg={borderColor} />

          {/* Publish Button */}
          <Box
            px="16px"
            h="50px"
            display="flex"
            alignItems="center"
            borderRight="1px solid"
            borderColor={borderColor}
          >
            <Button
              leftIcon={<ExternalLinkIcon />}
              size="sm"
              variant="solid"
              bg="#28a745"
              color="white"
              borderRadius="4px"
              fontWeight="600"
              fontSize="sm"
              px="12px"
              py="6px"
              onClick={handlePublish}
              _hover={{
                bg: '#218838',
              }}
            >
              {t('editor.publish')}
            </Button>
          </Box>

          {/* Divider */}
          <Box w="1px" h="50px" bg={borderColor} />
        </HStack>
      </Flex>

      {/* Website Settings Modal */}
      <WebsiteSettingsModal
        isOpen={showWebsiteSettings}
        onClose={() => setShowWebsiteSettings(false)}
        settings={websiteSettings}
        onSave={setWebsiteSettings}
      />
    </Box>
  )
}
