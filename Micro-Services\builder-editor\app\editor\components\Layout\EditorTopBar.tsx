'use client'

import {
  Box,
  Flex,
  HStack,
  Button,
  IconButton,
  Text,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Divider,
  useColorModeValue,
  Badge,
  Tooltip
} from '@chakra-ui/react'
import {
  ChevronDownIcon,
  ViewIcon,
  SettingsIcon,
  ExternalLinkIcon,
  CopyIcon,
  DownloadIcon,
  RepeatIcon,
  ArrowBackIcon,
  CheckIcon,
  WarningIcon,
  ArrowLeftIcon,
  ArrowRightIcon
} from '@chakra-ui/icons'
import { useEditorStore } from '@/lib/stores/editorStore'
import { useLanguage, useTranslation } from '@/lib/contexts/LanguageContext'
import { useState } from 'react'
import { useToast } from '@chakra-ui/react'
import { PageManager } from '../PageManager/PageManager'
import { LanguageSwitcher } from '../Language/LanguageSwitcher'
import { WebsiteSettingsModal } from '../WebsiteSettings/WebsiteSettingsModal'

export function EditorTopBar() {
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle')
  const [showWebsiteSettings, setShowWebsiteSettings] = useState(false)
  const [websiteSettings, setWebsiteSettings] = useState({
    colors: {
      primary: '#2563eb',
      secondary: '#64748b',
      accent: '#0ea5e9',
      background: '#ffffff',
      text: '#1e293b'
    },
    fonts: {
      heading: 'Inter, sans-serif',
      body: 'Inter, sans-serif'
    },
    buttons: {
      style: 'rounded' as const,
      size: 'md' as const,
      variant: 'solid' as const
    },
    logo: {
      url: '',
      alt: '',
      width: '200px',
      height: '60px'
    },
    favicon: {
      url: ''
    }
  })
  const toast = useToast()
  const { t } = useTranslation()
  const { uiDirection } = useLanguage()

  const {
    currentPage,
    isPreviewMode,
    setPreviewMode,
    currentBreakpoint,
    setCurrentBreakpoint,
    undo,
    redo,
    canUndo,
    canRedo
  } = useEditorStore()

  // Use fixed colors to prevent hydration mismatch
  const bgColor = 'white'
  const borderColor = 'gray.200'
  const textColor = 'gray.700'

  const handlePreview = () => {
    setPreviewMode(!isPreviewMode)
  }

  const handlePublish = () => {
    // TODO: Implement publish functionality
    console.log('Publishing site...')
  }

  const handleSave = async () => {
    if (!currentPage || saveStatus === 'saving') return

    setSaveStatus('saving')

    try {
      const response = await fetch('/api/pages/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pageId: currentPage.id,
          pageData: currentPage,
          timestamp: new Date().toISOString()
        })
      })

      if (!response.ok) {
        throw new Error(`Save failed: ${response.statusText}`)
      }

      const result = await response.json()
      setSaveStatus('saved')

      toast({
        title: 'Page saved',
        description: 'Your changes have been saved successfully',
        status: 'success',
        duration: 2000,
        isClosable: true,
      })

      // Reset to idle after 2 seconds
      setTimeout(() => setSaveStatus('idle'), 2000)

    } catch (error) {
      console.error('Save failed:', error)
      setSaveStatus('error')

      toast({
        title: 'Save failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })

      // Reset to idle after 3 seconds
      setTimeout(() => setSaveStatus('idle'), 3000)
    }
  }

  return (
    <Box
      bg={bgColor}
      borderBottom="1px"
      borderColor={borderColor}
      px={4}
      py={2}
      zIndex={1000}
      position="relative"
      boxShadow="sm"
    >
      <Flex justify="space-between" align="center" h="56px">
        {/* Left Section - Logo and Site Info */}
        <HStack spacing={4}>
          {/* Back to Dashboard */}
          <Tooltip label="Back to Dashboard">
            <IconButton
              aria-label="Back to dashboard"
              icon={<ArrowBackIcon />}
              variant="ghost"
              size="sm"
              color={textColor}
            />
          </Tooltip>

          <Divider orientation="vertical" h="24px" />

          {/* Site Name and Page Manager */}
          <Box>
            <Text fontSize="sm" fontWeight="semibold" color={textColor}>
              Website Builder
            </Text>
            <PageManager />
          </Box>

          {/* Page Status */}
          <Badge colorScheme="green" variant="subtle" size="sm">
            Draft
          </Badge>
        </HStack>

        {/* Center Section - Device Preview Controls */}
        <HStack spacing={2}>
          <Text fontSize="sm" color={textColor} mr={2}>
            Device:
          </Text>
          
          {/* Desktop */}
          <Tooltip label="Desktop View">
            <Button
              size="sm"
              variant={currentBreakpoint === 'desktop' ? 'solid' : 'outline'}
              colorScheme={currentBreakpoint === 'desktop' ? 'blue' : 'gray'}
              onClick={() => setCurrentBreakpoint('desktop')}
              leftIcon={<Box w="16px" h="12px" bg="currentColor" borderRadius="2px" />}
            >
              Desktop
            </Button>
          </Tooltip>

          {/* Tablet */}
          <Button
            size="sm"
            variant={currentBreakpoint === 'tablet' ? 'solid' : 'outline'}
            colorScheme={currentBreakpoint === 'tablet' ? 'blue' : 'gray'}
            onClick={() => setCurrentBreakpoint('tablet')}
            leftIcon={<Box w="12px" h="16px" bg="currentColor" borderRadius="2px" />}
          >
            Tablet
          </Button>

          {/* Mobile */}
          <Button
            size="sm"
            variant={currentBreakpoint === 'mobile' ? 'solid' : 'outline'}
            colorScheme={currentBreakpoint === 'mobile' ? 'blue' : 'gray'}
            onClick={() => setCurrentBreakpoint('mobile')}
            leftIcon={<Box w="8px" h="16px" bg="currentColor" borderRadius="2px" />}
          >
            Mobile
          </Button>
        </HStack>

        {/* Right Section - Actions */}
        <HStack spacing={3}>
          {/* Language Switchers */}
          <HStack spacing={2}>
            <LanguageSwitcher type="ui" />
            <LanguageSwitcher type="content" />
          </HStack>

          <Divider orientation="vertical" h="24px" />

          {/* Undo/Redo */}
          <HStack spacing={1}>
            <Tooltip label={t('editor.undo')}>
              <IconButton
                aria-label={t('editor.undo')}
                icon={uiDirection === 'rtl' ? <ArrowRightIcon /> : <ArrowLeftIcon />}
                size="sm"
                variant="ghost"
                isDisabled={!canUndo}
                onClick={undo}
              />
            </Tooltip>
            <Tooltip label={t('editor.redo')}>
              <IconButton
                aria-label={t('editor.redo')}
                icon={uiDirection === 'rtl' ? <ArrowLeftIcon /> : <ArrowRightIcon />}
                size="sm"
                variant="ghost"
                isDisabled={!canRedo}
                onClick={redo}
              />
            </Tooltip>
          </HStack>

          <Divider orientation="vertical" h="24px" />

          {/* Preview Button */}
          <Button
            leftIcon={<ViewIcon />}
            size="sm"
            variant={isPreviewMode ? 'solid' : 'outline'}
            colorScheme={isPreviewMode ? 'blue' : 'gray'}
            onClick={handlePreview}
          >
            {isPreviewMode ? 'Edit' : t('editor.preview')}
          </Button>

          {/* Save Button */}
          <Button
            size="sm"
            variant="outline"
            onClick={handleSave}
            isLoading={saveStatus === 'saving'}
            loadingText="Saving"
            leftIcon={
              saveStatus === 'saved' ? <CheckIcon /> :
              saveStatus === 'error' ? <WarningIcon /> : undefined
            }
            colorScheme={
              saveStatus === 'saved' ? 'green' :
              saveStatus === 'error' ? 'red' : 'gray'
            }
          >
            {saveStatus === 'saved' ? 'Saved' :
             saveStatus === 'error' ? 'Error' : t('editor.save')}
          </Button>

          {/* Publish Button */}
          <Button
            leftIcon={<ExternalLinkIcon />}
            size="sm"
            colorScheme="green"
            onClick={handlePublish}
          >
            {t('editor.publish')}
          </Button>

          {/* More Actions Menu */}
          <Menu>
            <MenuButton
              as={IconButton}
              aria-label="More actions"
              icon={<SettingsIcon />}
              size="sm"
              variant="ghost"
            />
            <MenuList>
              <MenuItem icon={<CopyIcon />}>
                Duplicate Page
              </MenuItem>
              <MenuItem icon={<DownloadIcon />}>
                Export
              </MenuItem>
              <MenuItem
                icon={<SettingsIcon />}
                onClick={() => setShowWebsiteSettings(true)}
              >
                Website Settings
              </MenuItem>
            </MenuList>
          </Menu>
        </HStack>
      </Flex>

      {/* Website Settings Modal */}
      <WebsiteSettingsModal
        isOpen={showWebsiteSettings}
        onClose={() => setShowWebsiteSettings(false)}
        settings={websiteSettings}
        onSave={setWebsiteSettings}
      />
    </Box>
  )
}
