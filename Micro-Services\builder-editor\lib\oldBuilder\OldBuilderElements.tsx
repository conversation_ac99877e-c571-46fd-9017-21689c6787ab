'use client'

import React from 'react'

// Old Builder Element Components
// These will be replaced with actual imports from section-elements package

interface ButtonProps {
  value?: {
    text?: string
    href?: string
    target?: string
    destination?: string
  }
  type?: 'Primary' | 'Secondary'
}

interface TextProps {
  value?: string
  tag?: string
}

interface ImageProps {
  value?: string | { default?: string }
}

interface SocialLinksProps {
  value?: Record<string, string> | { default?: Record<string, string> }
  listClass?: string
  itemClass?: string
}

// Button Component matching Old Builder style
const Button: React.FC<ButtonProps> = ({ value, type = 'Primary' }) => {
  const isPrimary = type === 'Primary'
  
  const buttonStyle: React.CSSProperties = {
    padding: '12px 24px',
    borderRadius: '6px',
    border: isPrimary ? 'none' : '2px solid var(--theme-color-brand1, #007bff)',
    backgroundColor: isPrimary ? 'var(--theme-color-brand1, #007bff)' : 'transparent',
    color: isPrimary ? 'white' : 'var(--theme-color-brand1, #007bff)',
    cursor: 'pointer',
    fontWeight: '600',
    fontSize: '14px',
    textDecoration: 'none',
    display: 'inline-block',
    margin: '0 8px 8px 0',
    transition: 'all 0.3s ease',
    fontFamily: 'inherit'
  }

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault()
    if (value?.href && value.href !== '#') {
      if (value.target === 'new') {
        window.open(value.href, '_blank')
      } else {
        window.location.href = value.href
      }
    }
  }

  return (
    <button
      style={buttonStyle}
      onClick={handleClick}
      onMouseEnter={(e) => {
        if (isPrimary) {
          e.currentTarget.style.backgroundColor = 'var(--theme-color-secondary1, #0056b3)'
        } else {
          e.currentTarget.style.backgroundColor = 'var(--theme-color-brand1, #007bff)'
          e.currentTarget.style.color = 'white'
        }
      }}
      onMouseLeave={(e) => {
        if (isPrimary) {
          e.currentTarget.style.backgroundColor = 'var(--theme-color-brand1, #007bff)'
        } else {
          e.currentTarget.style.backgroundColor = 'transparent'
          e.currentTarget.style.color = 'var(--theme-color-brand1, #007bff)'
        }
      }}
    >
      {value?.text || 'Button'}
    </button>
  )
}

// Text Component matching Old Builder style
const Text: React.FC<TextProps> = ({ value, tag = 'p' }) => {
  const Tag = tag as keyof JSX.IntrinsicElements
  
  const getTextStyle = (tagName: string): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      margin: '0 0 16px 0',
      color: 'var(--text-color, inherit)',
      fontFamily: 'inherit'
    }

    switch (tagName) {
      case 'h1':
        return {
          ...baseStyle,
          fontSize: '2.5rem',
          fontWeight: '700',
          lineHeight: '1.2',
          marginBottom: '24px'
        }
      case 'h2':
        return {
          ...baseStyle,
          fontSize: '2rem',
          fontWeight: '600',
          lineHeight: '1.3',
          marginBottom: '20px'
        }
      case 'h3':
        return {
          ...baseStyle,
          fontSize: '1.5rem',
          fontWeight: '600',
          lineHeight: '1.4',
          marginBottom: '16px'
        }
      case 'p':
      default:
        return {
          ...baseStyle,
          fontSize: '1rem',
          lineHeight: '1.6',
          marginBottom: '16px'
        }
    }
  }

  return (
    <Tag
      style={getTextStyle(tag)}
      dangerouslySetInnerHTML={{ __html: value || 'Text content' }}
    />
  )
}

// Image Component matching Old Builder style
const Image: React.FC<ImageProps> = ({ value }) => {
  const imageSrc = typeof value === 'string' ? value : value?.default || '/placeholder-image.jpg'
  
  return (
    <img
      src={imageSrc}
      alt="Section image"
      style={{
        width: '100%',
        height: 'auto',
        borderRadius: '8px',
        objectFit: 'cover',
        display: 'block'
      }}
      onError={(e) => {
        e.currentTarget.src = '/placeholder-image.jpg'
      }}
    />
  )
}

// Social Links Component matching Old Builder style
const SocialLinks: React.FC<SocialLinksProps> = ({ value, listClass, itemClass }) => {
  const links = (typeof value === 'object' && value?.default) ? value.default : value || {}
  
  const socialIcons: Record<string, string> = {
    facebook: '📘',
    twitter: '🐦',
    linkedin: '💼',
    instagram: '📷',
    youtube: '📺',
    tiktok: '🎵',
    pinterest: '📌',
    snapchat: '👻'
  }

  const listStyle: React.CSSProperties = {
    listStyle: 'none',
    padding: 0,
    margin: 0,
    display: 'flex',
    alignItems: 'center'
  }

  const itemStyle: React.CSSProperties = {
    marginRight: '16px'
  }

  const linkStyle: React.CSSProperties = {
    textDecoration: 'none',
    fontSize: '24px',
    transition: 'transform 0.3s ease',
    display: 'inline-block'
  }

  const handleClick = (e: React.MouseEvent, url: string) => {
    e.preventDefault()
    if (url && url !== '#') {
      window.open(url, '_blank')
    }
  }

  return (
    <ul className={listClass} style={listStyle}>
      {Object.entries(links).map(([platform, url]) => (
        <li key={platform} className={itemClass} style={itemStyle}>
          <a
            href={url as string}
            style={linkStyle}
            onClick={(e) => handleClick(e, url as string)}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'scale(1.1)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'scale(1)'
            }}
          >
            {socialIcons[platform] || '🔗'}
          </a>
        </li>
      ))}
    </ul>
  )
}

// Video Component (placeholder)
const Video: React.FC<{ value?: any }> = ({ value }) => {
  return (
    <div
      style={{
        width: '100%',
        height: '300px',
        backgroundColor: '#f8f9fa',
        border: '2px dashed #dee2e6',
        borderRadius: '8px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#6c757d'
      }}
    >
      🎬 Video Component
    </div>
  )
}

// Form Component (placeholder)
const Form: React.FC<{ value?: any }> = ({ value }) => {
  return (
    <div
      style={{
        width: '100%',
        padding: '20px',
        backgroundColor: '#f8f9fa',
        border: '1px solid #dee2e6',
        borderRadius: '8px',
        color: '#6c757d'
      }}
    >
      📝 Form Component
    </div>
  )
}

// Map Component (placeholder)
const Map: React.FC<{ value?: any }> = ({ value }) => {
  return (
    <div
      style={{
        width: '100%',
        height: '300px',
        backgroundColor: '#f8f9fa',
        border: '2px dashed #dee2e6',
        borderRadius: '8px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#6c757d'
      }}
    >
      🗺️ Map Component
    </div>
  )
}

// Export all Old Builder Elements
export const OldBuilderElements = {
  Button,
  Text,
  Image,
  SocialLinks,
  Video,
  Form,
  Map
}

export default OldBuilderElements
