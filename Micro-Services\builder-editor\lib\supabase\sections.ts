import { createClient } from '@supabase/supabase-js'
import { Section } from '@/lib/stores/editorStore'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface DatabaseSection {
  id: string
  website_id: string
  name: string
  type: string
  position: number
  props: any
  style: any
  responsive: any
  old_builder_design?: {
    category: string
    designId: string
    name: string
    thumbnail?: string
  }
  created_at: string
  updated_at: string
}

export interface DatabaseWebsite {
  id: string
  user_id: string
  name: string
  domain?: string
  language: 'en' | 'ar'
  theme: any
  settings: any
  created_at: string
  updated_at: string
}

// Convert database section to editor section
export function databaseSectionToEditorSection(dbSection: DatabaseSection): Section {
  return {
    id: dbSection.id,
    type: dbSection.type as any,
    name: dbSection.name,
    elements: [], // Will be populated from props
    props: dbSection.props || {},
    style: dbSection.style || {},
    responsive: dbSection.responsive || {
      desktop: {},
      tablet: {},
      mobile: {}
    },
    oldBuilderDesign: dbSection.old_builder_design ? {
      category: dbSection.old_builder_design.category,
      designId: dbSection.old_builder_design.designId,
      name: dbSection.old_builder_design.name,
      thumbnail: dbSection.old_builder_design.thumbnail
    } : undefined
  }
}

// Convert editor section to database section
export function editorSectionToDatabaseSection(
  section: Section, 
  websiteId: string, 
  position: number
): Omit<DatabaseSection, 'created_at' | 'updated_at'> {
  return {
    id: section.id,
    website_id: websiteId,
    name: section.name,
    type: section.type,
    position,
    props: section.props,
    style: section.style,
    responsive: section.responsive,
    old_builder_design: section.oldBuilderDesign ? {
      category: section.oldBuilderDesign.category,
      designId: section.oldBuilderDesign.designId,
      name: section.oldBuilderDesign.name,
      thumbnail: section.oldBuilderDesign.thumbnail
    } : undefined
  }
}

// Save sections to database
export async function saveSections(websiteId: string, sections: Section[]): Promise<{ success: boolean; error?: string }> {
  try {
    // First, delete existing sections for this website
    const { error: deleteError } = await supabase
      .from('sections')
      .delete()
      .eq('website_id', websiteId)

    if (deleteError) {
      console.error('Error deleting existing sections:', deleteError)
      return { success: false, error: deleteError.message }
    }

    // Convert sections to database format
    const dbSections = sections.map((section, index) => 
      editorSectionToDatabaseSection(section, websiteId, index)
    )

    // Insert new sections
    const { error: insertError } = await supabase
      .from('sections')
      .insert(dbSections)

    if (insertError) {
      console.error('Error inserting sections:', insertError)
      return { success: false, error: insertError.message }
    }

    return { success: true }
  } catch (error) {
    console.error('Error saving sections:', error)
    return { success: false, error: 'Failed to save sections' }
  }
}

// Load sections from database
export async function loadSections(websiteId: string): Promise<{ sections: Section[]; error?: string }> {
  try {
    const { data, error } = await supabase
      .from('sections')
      .select('*')
      .eq('website_id', websiteId)
      .order('position', { ascending: true })

    if (error) {
      console.error('Error loading sections:', error)
      return { sections: [], error: error.message }
    }

    const sections = (data as DatabaseSection[]).map(databaseSectionToEditorSection)
    return { sections }
  } catch (error) {
    console.error('Error loading sections:', error)
    return { sections: [], error: 'Failed to load sections' }
  }
}

// Save a single section
export async function saveSection(websiteId: string, section: Section, position: number): Promise<{ success: boolean; error?: string }> {
  try {
    const dbSection = editorSectionToDatabaseSection(section, websiteId, position)

    const { error } = await supabase
      .from('sections')
      .upsert(dbSection)

    if (error) {
      console.error('Error saving section:', error)
      return { success: false, error: error.message }
    }

    return { success: true }
  } catch (error) {
    console.error('Error saving section:', error)
    return { success: false, error: 'Failed to save section' }
  }
}

// Delete a section
export async function deleteSection(sectionId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('sections')
      .delete()
      .eq('id', sectionId)

    if (error) {
      console.error('Error deleting section:', error)
      return { success: false, error: error.message }
    }

    return { success: true }
  } catch (error) {
    console.error('Error deleting section:', error)
    return { success: false, error: 'Failed to delete section' }
  }
}

// Create a new website
export async function createWebsite(
  userId: string, 
  name: string, 
  language: 'en' | 'ar' = 'en'
): Promise<{ website: DatabaseWebsite | null; error?: string }> {
  try {
    const websiteData = {
      user_id: userId,
      name,
      language,
      theme: {
        primaryColor: '#007bff',
        secondaryColor: '#6c757d',
        fontFamily: 'Inter, sans-serif'
      },
      settings: {
        rtl: language === 'ar',
        showHeader: true,
        showFooter: true
      }
    }

    const { data, error } = await supabase
      .from('websites')
      .insert(websiteData)
      .select()
      .single()

    if (error) {
      console.error('Error creating website:', error)
      return { website: null, error: error.message }
    }

    return { website: data as DatabaseWebsite }
  } catch (error) {
    console.error('Error creating website:', error)
    return { website: null, error: 'Failed to create website' }
  }
}

// Load user websites
export async function loadUserWebsites(userId: string): Promise<{ websites: DatabaseWebsite[]; error?: string }> {
  try {
    const { data, error } = await supabase
      .from('websites')
      .select('*')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false })

    if (error) {
      console.error('Error loading websites:', error)
      return { websites: [], error: error.message }
    }

    return { websites: data as DatabaseWebsite[] }
  } catch (error) {
    console.error('Error loading websites:', error)
    return { websites: [], error: 'Failed to load websites' }
  }
}

// Update website
export async function updateWebsite(
  websiteId: string, 
  updates: Partial<Omit<DatabaseWebsite, 'id' | 'user_id' | 'created_at' | 'updated_at'>>
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('websites')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', websiteId)

    if (error) {
      console.error('Error updating website:', error)
      return { success: false, error: error.message }
    }

    return { success: true }
  } catch (error) {
    console.error('Error updating website:', error)
    return { success: false, error: 'Failed to update website' }
  }
}
