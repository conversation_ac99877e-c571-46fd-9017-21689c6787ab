import React from "react";
import styled, { css } from "styled-components";
import { Box, Stack, Text } from "@chakra-ui/react";
interface IconProps {
  selected?: boolean;
  text?: string | JSX.Element;
  Icon?: ({ size }: { size?: string }) => JSX.Element;
  onClick?: React.MouseEventHandler<HTMLDivElement>;
}
function SingleIcon({ onClick, Icon, text, selected }: IconProps) {
  return (
    <Box cursor="pointer" onClick={onClick}>
      <IconContainer selected={selected}>
        <Icon />
      </IconContainer>
      <Stack mt="4px" gap="16px" align="center" justify="center">
        <Text color="GrayText" fontSize="12px" fontWeight="400">
          {text}
        </Text>
      </Stack>
    </Box>
  );
}

export default SingleIcon;

const IconContainer = styled.div<{ selected: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 44px;
  border: 1px solid #d0d5dd;
  /* Shadow/xs */

  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05);
  border-radius: 4px;

  ${({ selected }) =>
    selected
      ? css`
          border: 1px solid #0e9384;
        `
      : css`
          border: 1px solid #d0d5dd;
        `}
`;
