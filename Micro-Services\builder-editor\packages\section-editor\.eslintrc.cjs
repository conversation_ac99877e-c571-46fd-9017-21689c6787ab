module.exports = {
  parser: "@typescript-eslint/parser",
  env: { browser: true, es6: true, node: true },
  extends: [
    "eslint:recommended",
    "plugin:react/recommended",
    "plugin:import/recommended",
    "plugin:jsx-a11y/recommended",
    "plugin:@typescript-eslint/recommended",
    "prettier",
  ],
  parserOptions: {
    project: "./tsconfig.json",
    ecmaFeatures: { jsx: true },
    ecmaVersion: 18,
    sourceType: "module",
  },
  globals: { window: true, React: true },
  plugins: [
    "react",
    "react-hooks",
    "@typescript-eslint",
    "prettier",
    "eslint-plugin-formatjs",
  ],
  settings: {
    react: { version: "detect" },
    "import/resolver": {
      node: {
        paths: ["src"],
        extensions: [".js", ".jsx", ".ts", ".tsx"],
      },
    },
  },
  rules: {
    "react-hooks/rules-of-hooks": "error",
    "prettier/prettier": ["warn", { endOfLine: "auto" }],
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/no-non-null-assertion": "off",
    "@typescript-eslint/ban-ts-comment": "off",
    "@typescript-eslint/no-empty-function": "off",
    "react/prop-types": "off",
    "react/react-in-jsx-scope": "off",
    "react/no-children-prop": "off",
    "import/named": "off",
    "no-console": "warn",
    "@typescript-eslint/no-empty-interface": "off",
    "no-empty-pattern": "off",
    "import/no-cycle": "error",
    "@typescript-eslint/no-non-null-asserted-optional-chain": "off",
    "formatjs/enforce-id": [
      "warn",
      { idInterpolationPattern: "[sha512:contenthash:base64:6]" },
    ],
    "formatjs/enforce-placeholders": "error",
    "formatjs/enforce-default-message": "error",
    "formatjs/no-literal-string-in-jsx": "warn",
    "formatjs/no-multiple-whitespaces": "error",
    "@typescript-eslint/no-unused-vars": [
      "warn",
      {
        "args": "all",
        "argsIgnorePattern": "^_",
        "caughtErrors": "all",
        "caughtErrorsIgnorePattern": "^_",
        "destructuredArrayIgnorePattern": "^_",
        "varsIgnorePattern": "^_",
        "ignoreRestSiblings": true
      }
    ],
  },
};
