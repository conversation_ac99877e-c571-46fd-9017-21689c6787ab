import {
  Button,
  ButtonIcon,
  DropMenu,
  DuplicateIcon,
  GarbageIcon,
  ModalConfirm,
  MoreHorizIcon,
} from "@wuilt/quilt";
import React, { useState } from "react";
import { FormattedMessage } from "react-intl";
import trackEvent from "../../../shared/utils/trackEvent";
import { SectionElement, WuiltContext } from "@wuilt/section-preview";

interface ElementActionsProps {
  deleteElement: () => void;
  duplicateElement: () => void;
  element?: SectionElement;
  wuiltContext?: WuiltContext;
}

const ElementActions: React.FC<ElementActionsProps> = ({
  duplicateElement,
  deleteElement,
  element,
}) => {
  const [openConfirmModal, setOpenConfirmModal] = useState(false);

  return (
    <>
      <DropMenu
        applyHoverEffect
        activator={
          <ButtonIcon color="white" stopOpacity>
            <MoreHorizIcon />
          </ButtonIcon>
        }
      >
        <Button
          plain
          compact
          prefixIcon={<DuplicateIcon />}
          onClick={() => {
            duplicateElement();
            trackEvent("elementDuplicated", {
              "Element name": element?.type!,
            });
          }}
        >
          <FormattedMessage defaultMessage="Duplicate" id="4fHiNl" />
        </Button>

        <Button
          plain
          color="danger"
          compact
          onClick={() => setOpenConfirmModal(true)}
          prefixIcon={<GarbageIcon />}
        >
          <FormattedMessage defaultMessage="Delete" id="K3r6DQ" />
        </Button>
      </DropMenu>

      <ModalConfirm
        show={openConfirmModal}
        onClose={() => setOpenConfirmModal(false)}
        modalHeader={
          <FormattedMessage defaultMessage="Deleting Element" id="3Es1k2" />
        }
        modalBody={
          <p>
            <FormattedMessage
              defaultMessage="Are you sure you want to delete this element? This action cannot be undone."
              id="F0ZHKy"
            />
          </p>
        }
        cancelText={<FormattedMessage defaultMessage="Cancel" id="47FYwb" />}
        confirmText={<FormattedMessage defaultMessage="Delete" id="K3r6DQ" />}
        onConfirm={() => {
          deleteElement();
          trackEvent("elementDeleted", {
            "Element name": element?.type!,
          });
        }}
        loading={false}
      />
    </>
  );
};

export default ElementActions;
