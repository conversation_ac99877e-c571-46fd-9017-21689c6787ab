import React from "react";
import { ElementsViewer } from "@wuilt/section-preview";
import { ElementProps } from "../Element";
import { Box } from "@wuilt/quilt";

export interface AppStoreButtonsEditProps extends ElementProps {}
const AppStoreButtonsEdit: React.FC<AppStoreButtonsEditProps> = ({
  element,
  wuiltContext,
}) => {
  return (
    <Box id={element?.id} dataTest="AppStoreButtonsEdit" p="4px 0">
      <ElementsViewer.AppStoreButtons
        element={element}
        wuiltContext={wuiltContext}
      ></ElementsViewer.AppStoreButtons>
    </Box>
  );
};

export default AppStoreButtonsEdit;
