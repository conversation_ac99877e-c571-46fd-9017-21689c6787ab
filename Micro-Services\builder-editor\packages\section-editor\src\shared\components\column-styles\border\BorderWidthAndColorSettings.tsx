import { Stack } from "@wuilt/quilt";
import BorderColorInput from "./BorderColorInput";
import BorderWidthInput from "./BorderWidthInput";
import { BorderPosition } from "./helper";
import { Borders } from "@wuilt/section-preview";

interface BorderProps {
  borders: Borders | undefined;
  activeBorder: BorderPosition;
  onChange: (v: Borders) => void;
}

function BorderWidthAndColorSettings({
  borders,
  onChange,
  activeBorder,
}: BorderProps) {
  if (borders?.borderStyle?.[activeBorder] === "none") {
    return null;
  }
  return (
    <div>
      <Stack my="14px" align="center" width="100%" direction="row">
        <BorderWidthInput
          activeBorder={activeBorder}
          borders={borders}
          onChange={onChange}
        />
        <BorderColorInput
          activeBorder={activeBorder}
          borders={borders}
          onChange={onChange}
        />
      </Stack>
    </div>
  );
}

export default BorderWidthAndColorSettings;
