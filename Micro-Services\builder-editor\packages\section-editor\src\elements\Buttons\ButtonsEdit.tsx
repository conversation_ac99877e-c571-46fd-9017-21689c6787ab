import React from "react";
import {
  ElementsViewer,
  ButtonsSettings,
  SectionElement,
} from "@wuilt/section-preview";
import { ElementProps } from "../Element";
import { ButtonEdit } from "../Button";
import { updateButtonsSettingsMutation } from "../../shared/mutations/buttonsMutations";
import { Box } from "@chakra-ui/react";

export interface ButtonsEditProps extends ElementProps {}

const ButtonsEdit: React.FC<ButtonsEditProps> = ({
  element,
  wuiltContext,
  mutateElementApi,
  updateElementUi,
  text,
  updateTextApi,
}) => {
  const values = element?.settings as ButtonsSettings;

  const mutateButtonApi = (newButton: SectionElement, index: number) => {
    const newButtons = updateButtonsSettingsMutation(element, newButton, index);
    mutateElementApi(newButtons);
  };

  const updateButtonUi = (newButton: SectionElement, index: number) => {
    const newButtons = updateButtonsSettingsMutation(element, newButton, index);
    updateElementUi(newButtons);
  };

  return (
    <Box id={element?.id} data-test="ButtonsEdit" p="4px 0">
      <ElementsViewer.Buttons
        element={element}
        wuiltContext={wuiltContext}
        stopAction
      >
        {values?.buttons?.map((button, index) => {
          const buttonElement: SectionElement = {
            id: button?.id,
            settings: button,
            type: "Button",
          };
          return (
            <ButtonEdit
              key={button?.id}
              element={buttonElement}
              wuiltContext={wuiltContext}
              mutateElementApi={(newElement) => {
                mutateButtonApi(newElement, index);
              }}
              updateElementUi={(newElement) => {
                updateButtonUi(newElement, index);
              }}
              text={text}
              updateTextApi={updateTextApi}
            />
          );
        })}
      </ElementsViewer.Buttons>
    </Box>
  );
};

export { ButtonsEdit };
