import { Row } from "@wuilt/section-preview";

const GRID_CELLS_COUNT = 16;

export function calculateDefaultColumnSize(row: Row) {
  const columnSize = GRID_CELLS_COUNT / row?.columns?.length;
  return columnSize;
}

export function resizeColumnsFallbackGridTemplates(row: Row) {
  const defaultColumnSize = calculateDefaultColumnSize(row);
  const fallbackGridTemplates = Array(row?.columns?.length).fill(
    defaultColumnSize
  );
  return fallbackGridTemplates;
}

export function calculateGridTemplates(row: Row) {
  const defaultColumnSize = calculateDefaultColumnSize(row);
  const fallbackGridTemplates = resizeColumnsFallbackGridTemplates(row);
  const gridColumnTemplates = (
    row?.settings?.layout?.gridTemplates || fallbackGridTemplates
  )?.map(
    (i) =>
      `minmax(0, calc(100% * ${i || defaultColumnSize} / ${GRID_CELLS_COUNT}))`
  );
  return gridColumnTemplates;
}
