import React from "react";
import ColumnStyle from "../../Column/components/ColumnStyle";
import { Row, Settings } from "@wuilt/section-preview";

interface RowStyleProps {
  settings: Settings | undefined;
  updateSettings: (v: Row["settings"]) => void;
}

const RowStyle: React.FC<RowStyleProps> = ({ settings, updateSettings }) => {
  return <ColumnStyle settings={settings} updateSettings={updateSettings} />;
};

export default RowStyle;
