import { Column, FormSettings } from "@wuilt/section-preview";

export const getColumnForms = (column: Column) => {
  const columnForms = column?.elements?.filter(
    (element) => element?.type === "Form"
  );
  if (columnForms?.length) {
    const formsIds = columnForms?.map(
      (form) => (form?.settings as FormSettings)?.formId
    );
    return formsIds;
  }
  return;
};

export const getRowForms = (row) => {
  const rowFormsIds = row?.columns?.flatMap((column) => {
    return getColumnForms(column) || [];
  });
  if (rowFormsIds?.length) {
    return rowFormsIds.flat(1);
  }
  return;
};
