'use client'

import React, { useState, useEffect } from 'react'
import { Box, Spinner, Text, Alert, AlertIcon } from '@chakra-ui/react'
import { Section } from '@/lib/stores/editorStore'
import { OldBuilderElements } from '@/lib/oldBuilder/OldBuilderElements'
import { injectOldBuilderCSS } from '@/lib/oldBuilder/cssLoader'

interface OldBuilderSectionRendererProps {
  section: Section
  isSelected: boolean
  onSelect: () => void
}



// Create a mock Old Builder section component for now
function createMockOldBuilderSection(category: string, designId: string) {
  // This will be replaced with actual Old Builder section loading
  const MockSection = ({ Elements, classes, ...props }: any) => {
    const categoryColors: Record<string, string> = {
      hero: '#4299e1',
      features: '#48bb78',
      about: '#ed8936',
      services: '#9f7aea',
      team: '#f56565',
      testimonials: '#38b2ac',
      contact: '#ed64a6',
      gallery: '#667eea',
      pricing: '#f6ad55',
      'call-to-action': '#fc8181'
    }

    const bgColor = categoryColors[category] || '#718096'

    return (
      <div className="container" style={{ padding: '60px 20px', backgroundColor: '#f8f9fa' }}>
        <div className="row align-items-center">
          <div className="col-md-6">
            <Elements.Text value={`<h2>Professional ${category} Section</h2>`} tag="h2" />
            <Elements.Text value={`This is a ${category} section from the Old Builder design library. It includes professional styling and responsive layout.`} tag="p" />
            <Elements.Button value={{ text: 'Learn More', href: '#' }} type="Primary" />
            <Elements.Button value={{ text: 'Contact Us', href: '#' }} type="Secondary" />
          </div>
          <div className="col-md-6">
            <div style={{
              width: '100%',
              height: '300px',
              backgroundColor: bgColor,
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '18px',
              fontWeight: '600'
            }}>
              {category.charAt(0).toUpperCase() + category.slice(1)} {designId}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return {
    Component: MockSection,
    styles: {},
    meta: {
      title: { defaultValue: `${category} Section` },
      description: { defaultValue: `Professional ${category} section design` }
    }
  }
}

// Get default props from meta
function getDefaultProps(meta: any, language = 'default') {
  const props: any = {}
  
  Object.entries(meta || {}).forEach(([key, config]: [string, any]) => {
    if (config.defaultValue) {
      if (typeof config.defaultValue === 'object' && config.defaultValue[language]) {
        props[key] = config.defaultValue[language]
      } else if (typeof config.defaultValue === 'object' && config.defaultValue.default) {
        props[key] = config.defaultValue.default
      } else {
        props[key] = config.defaultValue
      }
    }
  })
  
  return props
}

export function OldBuilderSectionRenderer({ section, isSelected, onSelect }: OldBuilderSectionRendererProps) {
  const [sectionData, setSectionData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function loadSection() {
      if (!section.oldBuilderDesign) {
        setError('No Old Builder design data found')
        setLoading(false)
        return
      }

      const { category, designId } = section.oldBuilderDesign
      
      try {
        setLoading(true)
        setError(null)
        
        const data = createMockOldBuilderSection(category, designId)
        
        if (data) {
          setSectionData(data)
        } else {
          setError(`Failed to load section ${category}/${designId}`)
        }
      } catch (err) {
        console.error('Error loading Old Builder section:', err)
        setError('Failed to load section component')
      } finally {
        setLoading(false)
      }
    }

    loadSection()
  }, [section.oldBuilderDesign])

  if (loading) {
    return (
      <Box
        minH="200px"
        display="flex"
        alignItems="center"
        justifyContent="center"
        border={isSelected ? '2px solid #007bff' : '1px solid #e2e8f0'}
        borderRadius="md"
        bg="gray.50"
        onClick={onSelect}
        cursor="pointer"
      >
        <Spinner size="lg" color="blue.500" />
        <Text ml="12px" color="gray.600">Loading section...</Text>
      </Box>
    )
  }

  if (error || !sectionData) {
    return (
      <Box
        minH="200px"
        border={isSelected ? '2px solid #007bff' : '1px solid #e2e8f0'}
        borderRadius="md"
        onClick={onSelect}
        cursor="pointer"
        p="20px"
      >
        <Alert status="warning">
          <AlertIcon />
          <Box>
            <Text fontWeight="bold">Section Preview Unavailable</Text>
            <Text fontSize="sm">{error || 'Could not load section component'}</Text>
            <Text fontSize="xs" color="gray.600" mt="8px">
              Section: {section.name} ({section.oldBuilderDesign?.category}/{section.oldBuilderDesign?.designId})
            </Text>
          </Box>
        </Alert>
      </Box>
    )
  }

  // Inject CSS for Old Builder styles
  React.useEffect(() => {
    injectOldBuilderCSS()
  }, [])

  // Prepare props for the Old Builder component
  const defaultProps = getDefaultProps(sectionData.meta)
  const sectionProps = {
    ...defaultProps,
    ...section.props,
    Elements: OldBuilderElements,
    classes: sectionData.styles || {}
  }

  const SectionComponent = sectionData.Component

  return (
    <Box
      border={isSelected ? '2px solid #007bff' : 'none'}
      borderRadius={isSelected ? 'md' : 'none'}
      onClick={onSelect}
      cursor="pointer"
      position="relative"
      style={{
        // Apply CSS variables for theming
        '--theme-color-brand1': '#007bff',
        '--theme-color-secondary1': '#6c757d',
        '--text-color': '#333333'
      }}
    >
      {/* Render the actual Old Builder section */}
      <SectionComponent {...sectionProps} />
      
      {/* Selection overlay */}
      {isSelected && (
        <Box
          position="absolute"
          top="0"
          left="0"
          right="0"
          bottom="0"
          border="2px solid #007bff"
          borderRadius="md"
          pointerEvents="none"
          bg="rgba(0, 123, 255, 0.1)"
        />
      )}
    </Box>
  )
}
