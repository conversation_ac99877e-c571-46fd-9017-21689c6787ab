import React, { useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import ImageUpload from "../../../shared/components/ImageUpload";
import ImageClickAction from "./ImageClickAction";
import ImagePosition from "./ImagePosition";
import { Image, ImageSettings, SectionElement } from "@wuilt/section-preview";
import {
  Box,
  Divider,
  FormLabel,
  Stack,
  Switch,
  Tab,
  TabList,
  Tabs,
} from "@chakra-ui/react";
import InputField from "../../../components/InputField";
import ColorInput from "../../../components/ColorInput";
import { MonitorIcon, SmartphoneIcon } from "@wuilt/react-icons";

enum TabsTypes {
  "Desktop",
  "Mobile",
}

const getTabs = (desktopOnly: boolean) =>
  [
    {
      content: (
        <Stack direction="row" align="center" gap="4px">
          <MonitorIcon size="16px" />
          <span>
            <FormattedMessage defaultMessage="Desktop" id="tHSb8p" />
          </span>
        </Stack>
      ),
      id: TabsTypes.Desktop,
    },
    !desktopOnly && {
      content: (
        <Stack direction="row" align="center" gap="4px">
          <SmartphoneIcon size="16px" />
          <span>
            <FormattedMessage defaultMessage="Mobile" id="GWtmtu" />
          </span>
        </Stack>
      ),
      id: TabsTypes.Mobile,
    },
  ].filter(Boolean);

interface ImageSettingsTabsProps {
  element: SectionElement;
  pages?: any[];
  desktopOnly?: boolean;
  updateElementUi: (v: SectionElement) => void;
  onUploadImage: (cb: (src: string) => void) => void;
}

const ImageSettingsTabs: React.FC<ImageSettingsTabsProps> = ({
  element,
  pages,
  desktopOnly,
  updateElementUi,
  onUploadImage,
}) => {
  const intl = useIntl();
  const values = element?.settings as ImageSettings;
  const TABS = getTabs(desktopOnly);
  const [selectedTab, setSelectedTab] = useState(TABS[0]);
  const image =
    selectedTab?.id === TabsTypes?.Mobile ? values?.mobile : values?.desktop;
  const [hasOverlay, setHasOverlay] = useState(!!image?.overlay);

  const updateDesktop = (desktop: Image) => {
    updateElementUi({
      ...element,
      settings: { ...element?.settings, desktop },
    });
  };

  const updateMobile = (mobile: Image) => {
    updateElementUi({ ...element, settings: { ...element?.settings, mobile } });
  };

  const updateSettings = (image: Image) => {
    if (selectedTab?.id === TabsTypes.Desktop) {
      return updateDesktop(image);
    }
    updateMobile(image);
  };

  return (
    <Stack gap="16px">
      <Tabs onChange={(index) => setSelectedTab(TABS[index])}>
        <TabList>
          {TABS.map(({ content, id }) => {
            return (
              <Tab
                _selected={{ borderColor: "primary.500", color: "primary.500" }}
                _focusWithin={{ outline: "none" }}
                key={id}
              >
                {content}
              </Tab>
            );
          })}
        </TabList>
      </Tabs>

      <ImageUpload
        label={<FormattedMessage defaultMessage="Image" id="+0zv6g" />}
        value={image?.src}
        onUploadImage={onUploadImage}
        onChange={(src) => {
          updateSettings({ ...image, src });
        }}
      />

      <Divider />

      <Stack
        direction="row"
        align="center"
        onClick={(e) => e.stopPropagation()}
      >
        <Switch
          mb="0"
          size="md"
          isChecked={hasOverlay}
          onChange={(e: any) => {
            const value = e.target.checked;
            setHasOverlay(value);
            if (!value) {
              updateSettings({ ...image, overlay: "" });
            }
          }}
        />
        <FormLabel m="0" fontSize="16px" fontWeight="400">
          <FormattedMessage defaultMessage="Color overlay" id="kcl+X/" />
        </FormLabel>
      </Stack>
      {hasOverlay && (
        <Box>
          <FormLabel mb="4px" fontSize="16px" fontWeight="400">
            <FormattedMessage defaultMessage="Overlay color" id="etgaBX" />
          </FormLabel>
          <ColorInput
            color={image?.overlay || ""}
            onChange={(overlay) => updateSettings({ ...image, overlay })}
          />
        </Box>
      )}

      <Divider />

      <Box>
        <FormLabel mb="4px" fontSize="16px" fontWeight="400">
          <FormattedMessage
            defaultMessage="Image title (Alt text)"
            id="6HWL/o"
          />
        </FormLabel>
        <InputField
          placeholder={intl.formatMessage({
            defaultMessage: "Image ALT",
            id: "gUgJwk",
          })}
          value={image?.alt}
          onChange={(e) => updateSettings({ ...image, alt: e?.target?.value })}
        />
      </Box>

      <Divider />

      <ImageClickAction
        action={image?.action}
        pages={pages}
        updateAction={(action) => updateSettings({ ...image, action })}
      />

      <Divider />

      <ImagePosition
        value={image?.objectFit!}
        onChange={(objectFit) => {
          updateSettings({ ...image, objectFit });
        }}
      />
    </Stack>
  );
};

export default ImageSettingsTabs;
