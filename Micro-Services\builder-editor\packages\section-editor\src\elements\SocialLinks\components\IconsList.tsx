import React from "react";
import {
  SocialLinkSettings,
  SocialLinkName,
  SocialLinksIcons,
} from "@wuilt/section-preview";
import { useIntl } from "react-intl";
import { DragHandle, VerticalSort } from "../../../components/DragAndDrop";
import { TrashIcon } from "@wuilt/react-icons";
import {
  IconButton,
  InputGroup,
  InputLeftElement,
  Stack,
} from "@chakra-ui/react";
import InputField from "../../../components/InputField";

interface iconListProps {
  socialLinks?: SocialLinkSettings[];
  sortSocialLinksUi?: (arg: SocialLinkSettings[]) => void;
  deleteSocialLink?: (id: string) => void;
  updateSocialLinkAction: (socialLink: SocialLinkSettings) => void;
}
export const SocialLinksTypes: any = {
  [SocialLinkName.Behance]: <SocialLinksIcons.BehanceFilled />,
  [SocialLinkName.Discord]: <SocialLinksIcons.DiscordFilled />,
  [SocialLinkName.Dribbble]: <SocialLinksIcons.DribbbleFilled />,
  [SocialLinkName.Facebook]: <SocialLinksIcons.FacebookFilled />,
  [SocialLinkName.Messenger]: <SocialLinksIcons.FacebookMessengerFilled />,
  [SocialLinkName.Github]: <SocialLinksIcons.GithubFilled />,
  [SocialLinkName.Instagram]: <SocialLinksIcons.InstagramFilled />,
  [SocialLinkName.Linkedin]: <SocialLinksIcons.LinkedinFilled />,
  [SocialLinkName.Pinterest]: <SocialLinksIcons.PinterestFilled />,
  [SocialLinkName.Skype]: <SocialLinksIcons.SkypeFilled />,
  [SocialLinkName.Telegram]: <SocialLinksIcons.TelegramFilled />,
  [SocialLinkName.Tiktok]: <SocialLinksIcons.TiktokFilled />,
  [SocialLinkName.Twitter]: <SocialLinksIcons.TwitterFilled />,
  [SocialLinkName.Whatsapp]: <SocialLinksIcons.WhatsappFilled />,
  [SocialLinkName.Youtube]: <SocialLinksIcons.YoutubeFilled />,
  [SocialLinkName.Snapchat]: <SocialLinksIcons.SnapchatFilled />,
};
function IconsList({
  socialLinks,
  sortSocialLinksUi,
  deleteSocialLink,
  updateSocialLinkAction,
}: iconListProps) {
  const intl = useIntl();
  return (
    <VerticalSort
      useHandleOnly
      value={socialLinks!}
      onChange={(socialLinks) => {
        sortSocialLinksUi(socialLinks);
      }}
    >
      {({ item: socialLink }) => (
        <>
          <Stack
            direction="row"
            justify="space-between"
            pb="12px"
            className="dnd-item"
            gap="16px"
            align="center"
          >
            <Stack
              gap="16px"
              width="100%"
              direction="row"
              align="center"
              justify="center"
            >
              <DragHandle id={socialLink?.id} />
              <InputGroup>
                <InputLeftElement top="-2px">
                  {SocialLinksTypes[socialLink?.name!]}
                </InputLeftElement>
                <InputField
                  paddingInlineStart="35px"
                  type="text"
                  minHeight="36px"
                  maxHeight="36px"
                  borderRadius="4px"
                  placeholder={intl.formatMessage({
                    id: "omz+AY",
                    defaultMessage: "insert your link here",
                  })}
                  value={socialLink?.link}
                  onChange={(e) => {
                    updateSocialLinkAction({
                      ...socialLink,
                      link: e.target.value,
                    });
                  }}
                />
              </InputGroup>
            </Stack>
            <IconButton
              aria-label="Delete"
              size="xs"
              variant="plain"
              color={"error.600"}
              isDisabled={socialLinks?.length === 1}
              onClick={() => {
                deleteSocialLink(socialLink?.id);
              }}
            >
              <TrashIcon size="18px" />
            </IconButton>
          </Stack>
        </>
      )}
    </VerticalSort>
  );
}

export default IconsList;
