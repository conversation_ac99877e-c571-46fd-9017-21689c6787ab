import { useState } from "react";
import BorderPositionSettings from "./BorderPositionSettings";
import BorderStyleSettings from "./BorderStyleSettings";
import BorderWidthAndColorSettings from "./BorderWidthAndColorSettings";
import { BorderPosition } from "./helper";
import ResetBorderSettings from "./ResetBorderSettings";
import { Borders } from "@wuilt/section-preview";

interface BorderProps {
  borders: Borders | undefined;
  onChange: (v: Borders) => void;
}

function BorderTab({ borders, onChange }: BorderProps) {
  const [activeBorder, setActiveBorder] = useState(BorderPosition.all);
  return (
    <>
      <BorderPositionSettings
        activeBorder={activeBorder}
        setActiveBorder={setActiveBorder}
        borders={borders}
      />
      <ResetBorderSettings
        activeBorder={activeBorder}
        borders={borders}
        onChange={onChange}
      />
      <BorderStyleSettings
        borders={borders}
        activeBorder={activeBorder}
        onChange={onChange}
      />
      <BorderWidthAndColorSettings
        borders={borders}
        activeBorder={activeBorder}
        onChange={onChange}
      />
    </>
  );
}

export default BorderTab;
