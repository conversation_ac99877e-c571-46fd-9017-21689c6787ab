import { ButtonActionType, ButtonAction } from "@wuilt/section-preview";
import React from "react";
import { FormattedMessage, injectIntl, IntlShape } from "react-intl";
import getPageOption from "../../../shared/utils/getPageOption";
import { Box, Checkbox, Stack, Text } from "@chakra-ui/react";
import {
  EmailIcon,
  ExternalLinkIcon,
  NetworkDocumentIcon,
  PhoneIcon,
} from "@wuilt/react-icons";
import InputField from "../../../components/InputField";
import { Select } from "chakra-react-select";

const OPTIONS = [
  {
    label: <FormattedMessage defaultMessage="External link" id="OphrTn" />,
    value: ButtonActionType.External_Link,
    icon: <ExternalLinkIcon size="16px" />,
  },
  {
    label: <FormattedMessage defaultMessage="Open Page" id="aFuEjt" />,
    value: ButtonActionType.Internal_Link,
    icon: <NetworkDocumentIcon size="16px" />,
  },
  {
    label: <FormattedMessage defaultMessage="Email address" id="hJZwTS" />,
    value: ButtonActionType.Email,
    icon: <EmailIcon size="16px" />,
  },
  {
    label: <FormattedMessage defaultMessage="Phone no." id="1SphRN" />,
    value: ButtonActionType.Phone,
    icon: <PhoneIcon size="16px" />,
  },
];

interface ButtonActionSettingsProps {
  intl: IntlShape;
  action: ButtonAction | undefined;
  pages?: any[];
  updateAction: (v: ButtonAction) => void;
}

const ButtonActionSettings: React.FC<ButtonActionSettingsProps> = ({
  intl,
  action,
  pages,
  updateAction,
}) => {
  return (
    <Stack gap="16px">
      <Box>
        <Text fontSize="medium" mb="4px">
          <FormattedMessage defaultMessage="Button action" id="pRPwcS" />
        </Text>
        <Select
          useBasicStyles
          options={OPTIONS}
          isSearchable={false}
          value={createOption(action?.type)}
          menuPortalTarget={document.body}
          focusBorderColor="gray.300"
          styles={{
            menuPortal: (base) => ({ ...base, zIndex: 9999 }),
          }}
          chakraStyles={{
            control: (base) => ({ ...base, borderColor: "gray.300" }),
            option: (base) => ({
              ...base,
              _selected: { bg: "primary.400", color: "white" },
            }),
          }}
          formatOptionLabel={formatOptionLabel}
          onChange={(o) => {
            updateAction({
              ...action,
              type: o?.value,
              value: "",
              newTab: false,
            });
          }}
        />
      </Box>

      {action?.type === ButtonActionType.Internal_Link && (
        <Select
          useBasicStyles
          menuPortalTarget={document.body}
          focusBorderColor="gray.300"
          styles={{
            menuPortal: (base) => ({ ...base, zIndex: 9999 }),
          }}
          chakraStyles={{
            control: (base) => ({ ...base, borderColor: "gray.300" }),
            option: (base) => ({
              ...base,
              _selected: { bg: "primary.400", color: "white" },
            }),
          }}
          placeholder={
            <FormattedMessage defaultMessage="Select page" id="rvz2nM" />
          }
          options={pages?.map?.((i) => ({ value: i?.id, label: i?.name }))}
          value={getPageOption(action?.value, pages)}
          onChange={(o: any) => {
            updateAction({ ...action, value: o?.value ?? "" });
          }}
        />
      )}

      {action?.type === ButtonActionType.External_Link && (
        <Stack gap="16px">
          <InputField
            height="36px"
            placeholder={intl.formatMessage({
              defaultMessage: "ex. https://wuilt.com",
              id: "bBw2rk",
            })}
            value={action?.value}
            onChange={(e) => {
              updateAction({ ...action, value: e.target.value ?? "" });
            }}
          />
          <Checkbox
            mb="0"
            colorScheme="teal"
            isChecked={!!action?.newTab}
            onChange={(e) =>
              updateAction({ ...action, newTab: e.target.checked })
            }
          >
            <FormattedMessage
              defaultMessage="Open link in a new tab"
              id="Ue3cf2"
            />
          </Checkbox>
        </Stack>
      )}

      {action?.type === ButtonActionType.Email && (
        <Stack>
          <InputField
            placeholder={intl.formatMessage({
              defaultMessage: "ex. <EMAIL>",
              id: "/GUpEN",
            })}
            value={action?.value}
            onChange={(e) => {
              updateAction({ ...action, value: e.target.value ?? "" });
            }}
          />
          <Box>
            <Text fontSize="medium" mb="4px">
              <FormattedMessage
                defaultMessage="E-mail subject (optional)"
                id="bc70Hx"
              />
            </Text>
            <InputField
              height="36px"
              placeholder={intl.formatMessage({
                defaultMessage: "ex. Contact us",
                id: "pJQSpK",
              })}
              value={action?.emailSubject}
              onChange={(e) => {
                updateAction({ ...action, emailSubject: e.target.value ?? "" });
              }}
            />
          </Box>
        </Stack>
      )}

      {action?.type === ButtonActionType.Phone && (
        <InputField
          height="36px"
          placeholder={intl.formatMessage({
            defaultMessage: "ex. +20 123456789",
            id: "hpVUBV",
          })}
          value={action?.value}
          onChange={(e) => {
            updateAction({ ...action, value: e.target.value ?? "" });
          }}
        />
      )}
    </Stack>
  );
};

export default injectIntl(ButtonActionSettings);

/**
 * Helpers
 */

function createOption(type: ButtonActionType | undefined) {
  if (!type) return OPTIONS[0];
  return OPTIONS.find((i) => i.value === type);
}

function formatOptionLabel(option: (typeof OPTIONS)[0] | null) {
  return (
    <Stack direction="row" align="center" gap="4px">
      <span> {option?.icon} </span>
      <span> {option?.label} </span>
    </Stack>
  );
}
