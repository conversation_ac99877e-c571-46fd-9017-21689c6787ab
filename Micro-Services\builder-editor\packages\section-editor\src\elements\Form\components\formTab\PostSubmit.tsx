import React from "react";
import {
  ButtonActionType,
  FormPostSubmit,
  FormPostSubmitType,
  FormSettings,
  TextObject,
} from "@wuilt/section-preview";
import { FormattedMessage } from "react-intl";
import getPageOption from "../../../../shared/utils/getPageOption";
import {
  FormLabel,
  Text,
  VStack,
  Tabs,
  TabList,
  Tab,
  Button,
  HStack,
  Divider,
  FormControl,
  Switch,
} from "@chakra-ui/react";
import { ExternalLinkIcon, FileIcon } from "@wuilt/react-icons";
import InputField from "../../../../components/InputField";
import Select from "../../../../components/Select";

type RedirectOption = {
  label: React.ReactNode;
  value: ButtonActionType;
  icon: React.ReactNode;
};

const REDIRECT_OPTIONS = [
  {
    label: <FormattedMessage defaultMessage="External link" id="OphrTn" />,
    value: ButtonActionType.External_Link,
    icon: <ExternalLinkIcon size="20px" color="gray.500" />,
  },
  {
    label: <FormattedMessage defaultMessage="Open Page" id="aFuEjt" />,
    value: ButtonActionType.Internal_Link,
    icon: <FileIcon size="20px" color="gray.500" />,
  },
];

interface PostSubmitProps {
  text: TextObject;
  pages?: any[];
  formSettings: FormSettings;
  updateText?: (newTexts?: TextObject) => void;
  updateFormPostSubmit: (newFormPostSubmit: FormPostSubmit) => void;
}
const PostSubmit: React.FC<PostSubmitProps> = ({
  text,
  pages,
  formSettings,
  updateText,
  updateFormPostSubmit,
}) => {
  const activeTab = formSettings?.formPostSubmit?.submitType;

  return (
    <VStack width="full" gap="12px" padding="16px">
      <Tabs
        isFitted
        variant="unstyled"
        defaultIndex={activeTab === FormPostSubmitType.Message ? 0 : 1}
      >
        <TabList
          gap="8px"
          padding="4px"
          border="1px solid"
          borderRadius="8px"
          borderColor="gray.200"
          backgroundColor="gray.50"
        >
          <Tab padding="0px" _selected={{ border: "none" }}>
            <Button
              size="sm"
              width="full"
              borderRadius="6px"
              variant={
                activeTab === FormPostSubmitType.Message
                  ? "secondaryGray"
                  : "tertiaryGray"
              }
              onClick={() =>
                updateFormPostSubmit({
                  submitType: FormPostSubmitType.Message,
                })
              }
            >
              <Text
                variant="textSm"
                fontWeight="semibold"
                color={
                  activeTab === FormPostSubmitType.Message
                    ? "gray.700"
                    : "gray.500"
                }
              >
                <FormattedMessage defaultMessage="Message" id="T7Ry38" />
              </Text>
            </Button>
          </Tab>
          <Tab padding="0px" _selected={{ border: "none" }}>
            <Button
              size="sm"
              width="full"
              borderRadius="6px"
              variant={
                activeTab === FormPostSubmitType.Redirect
                  ? "secondaryGray"
                  : "tertiaryGray"
              }
              onClick={() =>
                updateFormPostSubmit({
                  submitType: FormPostSubmitType.Redirect,
                })
              }
            >
              <Text
                variant="textSm"
                fontWeight="semibold"
                color={
                  activeTab === FormPostSubmitType.Redirect
                    ? "gray.700"
                    : "gray.500"
                }
              >
                <FormattedMessage defaultMessage="Redirect" id="O7dlk1" />
              </Text>
            </Button>
          </Tab>
        </TabList>
      </Tabs>
      {activeTab === FormPostSubmitType.Message && (
        <InputField
          type="textarea"
          label={<FormattedMessage defaultMessage="Message" id="T7Ry38" />}
          placeholder={
            <FormattedMessage defaultMessage="Message" id="T7Ry38" />
          }
          hint={
            <FormattedMessage
              defaultMessage="This message displays after users submit a form."
              id="859u6T"
            />
          }
          value={text[formSettings?.formPostSubmit?.successMessage?.textId!]}
          onChange={(event) =>
            updateText?.({
              [formSettings?.formPostSubmit?.successMessage?.textId!]:
                event.target.value || "",
            })
          }
        />
      )}
      {activeTab === FormPostSubmitType.Redirect && (
        <>
          <VStack gap="6px" width="full" alignItems="start">
            <Text
              width="full"
              variant="textSm"
              fontWeight="medium"
              color="gray.700"
            >
              <FormLabel margin="0px">
                <FormattedMessage defaultMessage="Redirect to" id="Epr6B7" />
              </FormLabel>
            </Text>
            <Select
              value={createRedirectOption(
                formSettings?.formPostSubmit?.redirectType
              )}
              options={REDIRECT_OPTIONS}
              formatOptionLabel={formatRedirectOptionLabel}
              onChange={(option) =>
                updateFormPostSubmit({ redirectType: option?.value })
              }
            />
          </VStack>
          {formSettings?.formPostSubmit?.redirectType ===
            ButtonActionType.Internal_Link && (
            <Select
              value={getPageOption(
                formSettings?.formPostSubmit?.internalLink,
                pages
              )}
              placeholder={
                <FormattedMessage defaultMessage="Select page" id="rvz2nM" />
              }
              options={pages?.map?.((page) => ({
                value: page?.id,
                label: page?.name,
              }))}
              onChange={(option) => {
                updateFormPostSubmit({ internalLink: option?.value });
              }}
            />
          )}
          {formSettings?.formPostSubmit?.redirectType ===
            ButtonActionType.External_Link && (
            <>
              <InputField
                label={
                  <FormattedMessage defaultMessage="Link URL" id="1e9zwh" />
                }
                placeholder={
                  <FormattedMessage
                    defaultMessage="ex. https://example.com"
                    id="9fl8Fp"
                  />
                }
                hint={
                  <FormattedMessage
                    defaultMessage="Add a URL to redirect the user after they submit the form."
                    id="xgIb3m"
                  />
                }
                value={formSettings?.formPostSubmit?.externalLink}
                onChange={(e) => {
                  updateFormPostSubmit({ externalLink: e.target.value });
                }}
              />
              <Divider />
              <FormControl
                display="flex"
                alignItems="center"
                justifyContent="space-between"
              >
                <FormLabel htmlFor="new-tab-switch" margin="0px">
                  <Text variant="textSm" fontWeight="medium" color="gray.800">
                    <FormattedMessage
                      defaultMessage="Open link in a new tab"
                      id="Ue3cf2"
                    />
                  </Text>
                </FormLabel>
                <Switch
                  size="md"
                  id="new-tab-switch"
                  isChecked={formSettings?.formPostSubmit?.newTab}
                  onChange={(event) => {
                    updateFormPostSubmit({ newTab: event.target.checked });
                  }}
                />
              </FormControl>
            </>
          )}
        </>
      )}
    </VStack>
  );
};

/**
 * Helpers
 */

function createRedirectOption(type: ButtonActionType | undefined) {
  if (!type) return REDIRECT_OPTIONS[0];
  return REDIRECT_OPTIONS.find((i) => i.value === type);
}

function formatRedirectOptionLabel(option: RedirectOption) {
  return (
    <HStack gap="8px">
      {option.icon}
      <Text variant="textMd" fontWeight="medium" color="gray.800">
        {option?.label}
      </Text>
    </HStack>
  );
}

export default PostSubmit;
