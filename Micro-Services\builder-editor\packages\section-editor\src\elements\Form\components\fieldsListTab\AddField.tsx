import React, { Dispatch, SetStateAction } from "react";
import { FormattedMessage } from "react-intl";
import { nanoid } from "nanoid";
import { FormSettingsViews } from "../FormSettings";
import {
  FieldType,
  AutoCompleteType,
  FormField,
  TextObject,
  WuiltContext,
} from "@wuilt/section-preview";
import trackEvent from "../../../../shared/utils/trackEvent";
import UpgradeButton from "../../../../shared/components/UpgradeButton";
import {
  Button,
  Flex,
  SimpleGrid,
  Text,
  Tooltip,
  VStack,
} from "@chakra-ui/react";
import {
  AddIcon,
  CalendarOutlineIcon,
  CheckSquareIcon,
  ClockIcon,
  DropdownMenuIcon,
  EnvelopeIcon,
  Link01Icon,
  MobilePhoneIcon,
  RadioIcon,
  Type01Icon,
  TypeSquareIcon,
  UploadCloudIcon,
} from "@wuilt/react-icons";

export const FIELDS_TYPES = [
  {
    label: <FormattedMessage defaultMessage="Text" id="aA8bDw" />,
    icon: <Type01Icon size="20px" color="gray.500" />,
    value: FieldType.Text,
    defaultLabel: "Text",
    autoComplete: AutoCompleteType.Name,
  },
  {
    label: <FormattedMessage defaultMessage="E-mail" id="tkwCac" />,
    icon: <EnvelopeIcon size="20px" color="gray.500" />,
    value: FieldType.Email,
    defaultLabel: "Email",
    autoComplete: AutoCompleteType.Email,
  },
  {
    label: <FormattedMessage defaultMessage="Phone" id="O95R3Z" />,
    icon: <MobilePhoneIcon size="20px" color="gray.500" />,
    value: FieldType.Phone,
    defaultLabel: "Phone",
    autoComplete: AutoCompleteType.Phone,
  },
  {
    label: <FormattedMessage defaultMessage="Text area" id="9sozxz" />,
    icon: <TypeSquareIcon size="20px" color="gray.500" />,
    value: FieldType.TextArea,
    defaultLabel: "Message",
    autoComplete: "",
  },
  {
    label: <FormattedMessage defaultMessage="Link" id="JBWS0c" />,
    icon: <Link01Icon size="20px" color="gray.500" />,
    value: FieldType.Link,
    defaultLabel: "Link",
    autoComplete: AutoCompleteType.Url,
  },
  {
    label: <FormattedMessage defaultMessage="Select" id="kQAf2d" />,
    icon: <DropdownMenuIcon size="20px" color="gray.500" />,
    value: FieldType.Select,
    defaultLabel: "Select",
  },
  {
    label: <FormattedMessage defaultMessage="Checkbox" id="PRtqrC" />,
    icon: <CheckSquareIcon size="20px" color="gray.500" />,
    value: FieldType.Checkbox,
    defaultLabel: "Checkbox",
  },
  {
    label: <FormattedMessage defaultMessage="Radio" id="UkR6UT" />,
    icon: <RadioIcon size="20px" color="gray.500" />,
    value: FieldType.Radio,
    defaultLabel: "Radio",
  },
  {
    label: <FormattedMessage defaultMessage="Date" id="P7PLVj" />,
    icon: <CalendarOutlineIcon size="20px" color="gray.500" />,
    value: FieldType.Date,
    defaultLabel: "Date",
    autoComplete: "",
  },
  {
    label: <FormattedMessage defaultMessage="Time" id="ug01Mk" />,
    icon: <ClockIcon size="20px" color="gray.500" />,
    value: FieldType.Time,
    defaultLabel: "Time",
    autoComplete: "",
  },
  {
    label: <FormattedMessage defaultMessage="File upload" id="hEl6BL" />,
    icon: <UploadCloudIcon size="20px" color="gray.500" />,
    value: FieldType.File,
    defaultLabel: "File upload",
    defaultPlaceHolder: "No files selected",
    allowSpecificFileTypes: false,
  },
];

interface AddFieldProps {
  addField: (newField: FormField, newTexts: TextObject) => void;
  setView: Dispatch<SetStateAction<FormSettingsViews>>;
  wuiltContext: WuiltContext | undefined;
}

const AddField: React.FC<AddFieldProps> = ({
  addField,
  setView,
  wuiltContext,
}) => {
  const [hoveredFieldType, setHoveredFieldType] =
    React.useState<FieldType | null>(null);

  const availableFields = [
    FieldType.Text,
    FieldType.Email,
    FieldType.Phone,
    FieldType.TextArea,
  ];
  const { id, is_expired: isExpired, plan } = wuiltContext.props.site;
  const planId = plan?.id;
  const isPremiumOrFree = planId === 1 || planId === 15 || planId === 18;
  const isAvailableField = (fieldType) => {
    return (
      (isPremiumOrFree && availableFields.includes(fieldType?.value)) ||
      !isPremiumOrFree
    );
  };
  const handleAddField = (fieldType) => {
    if (isAvailableField(fieldType)) {
      const newLabelTextId = `Content:${nanoid()}`;
      const newPlaceholderTextId = `Content:${nanoid()}`;
      const newDescriptionTextId = `Content:${nanoid()}`;
      const newErrorMessageTextId = `Content:${nanoid()}`;
      const newTexts = {
        [newLabelTextId]: fieldType?.defaultLabel,
        [newPlaceholderTextId]: fieldType?.defaultPlaceHolder!,
        [newDescriptionTextId]: "",
        [newErrorMessageTextId]: "",
      };
      addField(
        {
          id: `Field:${nanoid()}`,
          type: fieldType?.value,
          settings: {
            label: { textId: newLabelTextId },
            description: { textId: newDescriptionTextId },
            autoComplete: fieldType?.autoComplete as any,
            allowedExtensions: "",
            placeholder: { textId: newPlaceholderTextId },
            errorMessage: { textId: newErrorMessageTextId },
            allowSpecificFileTypes: fieldType?.allowSpecificFileTypes,
          },
        },
        newTexts
      );
      setView(FormSettingsViews.Tabs);
      trackEvent("formFieldAdded", { "Field name": fieldType?.value });
    }
    return;
  };
  return (
    <VStack width="full" padding="16px" gap="8px">
      <SimpleGrid width="full" columns={2} columnGap="6px" rowGap="8px">
        {FIELDS_TYPES?.map((fieldType, index) => (
          <Tooltip
            hasArrow
            key={`add-field-${index}`}
            placement="top"
            backgroundColor="gray.900"
            label={
              <Text variant="textXs" fontWeight="semibold" color="white">
                <FormattedMessage defaultMessage="Add field" id="eSYQTy" />
              </Text>
            }
          >
            <Button
              key={index}
              width="full"
              padding="8px"
              boxShadow="none"
              borderRadius="6px"
              variant="secondaryGray"
              leftIcon={fieldType?.icon}
              _hover={{ backgroundColor: "gray.50" }}
              isDisabled={!isAvailableField(fieldType)}
              onClick={() => handleAddField(fieldType)}
              onMouseEnter={() => {
                setHoveredFieldType(fieldType?.value);
              }}
              onMouseLeave={() => {
                setHoveredFieldType(null);
              }}
            >
              <Flex
                width="full"
                alignItems="center"
                justifyContent="space-between"
              >
                <Text
                  width="full"
                  variant="textSm"
                  fontWeight="medium"
                  color="gray.700"
                >
                  {fieldType?.label}
                </Text>
                {hoveredFieldType === fieldType?.value && (
                  <AddIcon size="20px" color="teal.600" />
                )}
              </Flex>
            </Button>
          </Tooltip>
        ))}
      </SimpleGrid>
      {isPremiumOrFree && (
        <UpgradeButton
          isExpired={isExpired}
          siteId={id}
          content={
            <FormattedMessage
              defaultMessage="Upgrade to unlock all fields"
              id="WGrI7o"
            />
          }
        />
      )}
    </VStack>
  );
};

export default AddField;
