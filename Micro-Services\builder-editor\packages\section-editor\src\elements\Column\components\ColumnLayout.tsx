import React from "react";
import AlignmentInput from "../../../shared/components/AlignmentInput";
import GapInput from "../../../shared/components/GapInput";
import PaddingInput from "../../../shared/components/PaddingInput";
import { Column, Settings } from "@wuilt/section-preview";
import { Divider } from "@chakra-ui/react";

interface ColumnLayoutProps {
  rowGap: number;
  settings: Settings;
  isSiteRtl: boolean;
  updateSettings: (v: Column["settings"]) => void;
  updateRowGap: (gap: number) => void;
}

const ColumnLayout: React.FC<ColumnLayoutProps> = ({
  rowGap,
  settings,
  isSiteRtl,
  updateSettings,
  updateRowGap,
}) => {
  return (
    <>
      <GapInput value={rowGap ?? 0} onChange={updateRowGap} />

      <Divider my="16px" />

      <AlignmentInput
        isSiteRtl={isSiteRtl}
        value={settings?.layout?.alignment}
        onChange={(alignment) => {
          updateSettings({
            ...settings,
            layout: { ...settings?.layout, alignment },
          });
        }}
      />

      <Divider my="16px" />

      <PaddingInput
        value={settings?.layout?.padding}
        onChange={(padding) => {
          updateSettings({
            ...settings,
            layout: { ...settings?.layout, padding },
          });
        }}
      />
    </>
  );
};

export default ColumnLayout;
