import React from "react";
import {
  ComponentWithAs,
  FormControl,
  FormErrorMessage,
  FormHelperText,
  FormLabel,
  Input,
  InputProps,
  Text,
  Textarea,
  TextareaProps,
} from "@chakra-ui/react";
import { convertFormattedMessageToIntlString } from "../../shared/utils/convertToIntlString";
import { useIntl } from "react-intl";
import { DebounceInput, PropConstraints } from "react-debounce-input";

type FormInputType = React.HTMLInputTypeAttribute | "textarea" | "upload-area";

type InputElementType = (
  | ComponentWithAs<"textarea", TextareaProps>
  | ComponentWithAs<"input", InputProps>
) &
  (string | React.ComponentType<PropConstraints<HTMLInputElement>>);

interface InputFieldProps
  extends Omit<InputProps & TextareaProps, "placeholder"> {
  type?: FormInputType;
  label?: React.ReactNode;
  value?: number | string;
  hint?: React.ReactNode;
  error?: React.ReactNode;
  invalid?: boolean;
  placeholder?: React.ReactNode;
  debounce?: number;
}
const InputField: React.FC<InputFieldProps> = (props) => {
  const intl = useIntl();
  const {
    type,
    label,
    hint,
    placeholder,
    error,
    invalid,
    value,
    debounce,
    isDisabled,
    isRequired,
    isReadOnly,
    onBlur,
    onChange,
    ...inputElementProps
  } = props;
  const InputElement = (
    type === "textarea" ? Textarea : Input
  ) as InputElementType;

  return (
    <FormControl
      isInvalid={invalid}
      isDisabled={isDisabled}
      isRequired={isRequired}
      isReadOnly={isReadOnly}
    >
      {label && <FormLabel mb="6px">{label}</FormLabel>}
      <DebounceInput
        value={value}
        onBlur={onBlur}
        onChange={onChange}
        element={InputElement}
        placeholder={convertFormattedMessageToIntlString(placeholder, intl)}
        debounceTimeout={debounce || 0}
        minHeight={type === "textarea" ? "108px" : "44px"}
        resize={type === "textarea" ? "none" : undefined}
        {...inputElementProps}
      />
      {error && invalid ? (
        <FormErrorMessage>{error}</FormErrorMessage>
      ) : hint ? (
        <FormHelperText>
          <Text variant="textSm" color="gray.600">
            {hint}
          </Text>
        </FormHelperText>
      ) : null}
    </FormControl>
  );
};

export default InputField;
