import React, { useState } from 'react';
import {
  Box,
  IconButton,
  HStack,
  Tooltip,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverBody,
  VStack,
  Text,
  Switch,
  FormControl,
  FormLabel,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Button,
} from '@chakra-ui/react';
import {
  SettingsIcon,
  DeleteIcon,
  CopyIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ViewIcon,
  ViewOffIcon,
} from '@chakra-ui/icons';

interface SectionControlsProps {
  sectionData: any;
  onUpdate: (data: any) => void;
}

export const SectionControls: React.FC<SectionControlsProps> = ({
  sectionData,
  onUpdate,
}) => {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  const handleSettingChange = (path: string[], value: any) => {
    const updatedData = { ...sectionData };
    let current = updatedData;
    
    for (let i = 0; i < path.length - 1; i++) {
      if (!current[path[i]]) {
        current[path[i]] = {};
      }
      current = current[path[i]];
    }
    
    current[path[path.length - 1]] = value;
    onUpdate(updatedData);
  };

  const handleDuplicate = () => {
    // Implement section duplication
    console.log('Duplicate section');
  };

  const handleDelete = () => {
    // Implement section deletion
    console.log('Delete section');
  };

  const handleMoveUp = () => {
    // Implement move section up
    console.log('Move section up');
  };

  const handleMoveDown = () => {
    // Implement move section down
    console.log('Move section down');
  };

  const handleToggleVisibility = () => {
    handleSettingChange(['settings', 'isVisible'], !sectionData?.settings?.isVisible);
  };

  const settings = sectionData?.settings || {};
  const gridSettings = settings.grid || {};
  const styleSettings = settings.styles || {};
  const paddingSettings = styleSettings.padding || {};

  return (
    <Box
      position="absolute"
      top="-50px"
      right="0"
      zIndex={1001}
    >
      <HStack spacing={1}>
        {/* Move Up */}
        <Tooltip label="Move section up">
          <IconButton
            aria-label="Move up"
            icon={<ArrowUpIcon />}
            size="sm"
            variant="solid"
            bg="white"
            border="1px solid"
            borderColor="gray.200"
            onClick={handleMoveUp}
          />
        </Tooltip>

        {/* Move Down */}
        <Tooltip label="Move section down">
          <IconButton
            aria-label="Move down"
            icon={<ArrowDownIcon />}
            size="sm"
            variant="solid"
            bg="white"
            border="1px solid"
            borderColor="gray.200"
            onClick={handleMoveDown}
          />
        </Tooltip>

        {/* Toggle Visibility */}
        <Tooltip label={settings.isVisible ? "Hide section" : "Show section"}>
          <IconButton
            aria-label="Toggle visibility"
            icon={settings.isVisible ? <ViewIcon /> : <ViewOffIcon />}
            size="sm"
            variant="solid"
            bg="white"
            border="1px solid"
            borderColor="gray.200"
            onClick={handleToggleVisibility}
          />
        </Tooltip>

        {/* Duplicate */}
        <Tooltip label="Duplicate section">
          <IconButton
            aria-label="Duplicate"
            icon={<CopyIcon />}
            size="sm"
            variant="solid"
            bg="white"
            border="1px solid"
            borderColor="gray.200"
            onClick={handleDuplicate}
          />
        </Tooltip>

        {/* Settings */}
        <Popover
          isOpen={isSettingsOpen}
          onClose={() => setIsSettingsOpen(false)}
          placement="bottom-end"
        >
          <PopoverTrigger>
            <IconButton
              aria-label="Section settings"
              icon={<SettingsIcon />}
              size="sm"
              variant="solid"
              bg="white"
              border="1px solid"
              borderColor="gray.200"
              onClick={() => setIsSettingsOpen(!isSettingsOpen)}
            />
          </PopoverTrigger>
          
          <PopoverContent width="300px">
            <PopoverBody p={4}>
              <VStack spacing={4} align="stretch">
                <Text fontWeight="bold" fontSize="lg">
                  Section Settings
                </Text>

                {/* Grid Settings */}
                <Box>
                  <Text fontWeight="medium" mb={2}>Grid</Text>
                  
                  <FormControl display="flex" alignItems="center" mb={2}>
                    <FormLabel htmlFor="full-width" mb="0" fontSize="sm">
                      Full Width
                    </FormLabel>
                    <Switch
                      id="full-width"
                      isChecked={gridSettings.shouldFullWidth || false}
                      onChange={(e) => 
                        handleSettingChange(['settings', 'grid', 'shouldFullWidth'], e.target.checked)
                      }
                    />
                  </FormControl>

                  <FormControl display="flex" alignItems="center" mb={2}>
                    <FormLabel htmlFor="show-grid" mb="0" fontSize="sm">
                      Show Grid
                    </FormLabel>
                    <Switch
                      id="show-grid"
                      isChecked={gridSettings.shouldKeepGridVisible || false}
                      onChange={(e) => 
                        handleSettingChange(['settings', 'grid', 'shouldKeepGridVisible'], e.target.checked)
                      }
                    />
                  </FormControl>

                  <FormControl mb={2}>
                    <FormLabel fontSize="sm">Column Gap: {gridSettings.columnGap || 10}px</FormLabel>
                    <Slider
                      value={gridSettings.columnGap || 10}
                      min={0}
                      max={50}
                      step={5}
                      onChange={(value) => 
                        handleSettingChange(['settings', 'grid', 'columnGap'], value)
                      }
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <SliderThumb />
                    </Slider>
                  </FormControl>

                  <FormControl mb={2}>
                    <FormLabel fontSize="sm">Row Gap: {gridSettings.rowGap || 10}px</FormLabel>
                    <Slider
                      value={gridSettings.rowGap || 10}
                      min={0}
                      max={50}
                      step={5}
                      onChange={(value) => 
                        handleSettingChange(['settings', 'grid', 'rowGap'], value)
                      }
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <SliderThumb />
                    </Slider>
                  </FormControl>
                </Box>

                {/* Padding Settings */}
                <Box>
                  <Text fontWeight="medium" mb={2}>Padding</Text>
                  
                  <FormControl mb={2}>
                    <FormLabel fontSize="sm">Top: {paddingSettings.top || 0}px</FormLabel>
                    <Slider
                      value={paddingSettings.top || 0}
                      min={0}
                      max={200}
                      step={10}
                      onChange={(value) => 
                        handleSettingChange(['settings', 'styles', 'padding', 'top'], value)
                      }
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <SliderThumb />
                    </Slider>
                  </FormControl>

                  <FormControl mb={2}>
                    <FormLabel fontSize="sm">Bottom: {paddingSettings.bottom || 0}px</FormLabel>
                    <Slider
                      value={paddingSettings.bottom || 0}
                      min={0}
                      max={200}
                      step={10}
                      onChange={(value) => 
                        handleSettingChange(['settings', 'styles', 'padding', 'bottom'], value)
                      }
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <SliderThumb />
                    </Slider>
                  </FormControl>
                </Box>

                {/* Actions */}
                <Box pt={2} borderTop="1px solid" borderColor="gray.100">
                  <Button
                    size="sm"
                    colorScheme="red"
                    variant="outline"
                    width="100%"
                    onClick={handleDelete}
                  >
                    Delete Section
                  </Button>
                </Box>
              </VStack>
            </PopoverBody>
          </PopoverContent>
        </Popover>
      </HStack>
    </Box>
  );
};
