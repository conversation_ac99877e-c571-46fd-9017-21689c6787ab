import React, { useState } from "react";
import BorderRadiusInputs from "./BorderRadiusInputs";
import { BorderRadiusTabs } from "./helper";
import SelectedBorderRadius from "./SelectedBorderRadius";
import { BorderRadius } from "@wuilt/section-preview";
interface BorderRadiusProps {
  borderRadius: BorderRadius | undefined;
  onChange: (v: BorderRadius) => void;
}

function BorderRadiusTab({ borderRadius, onChange }: BorderRadiusProps) {
  const [activeBorderRadius, setActiveBorderRadius] = useState(
    BorderRadiusTabs.allCorners
  );

  return (
    <div>
      <SelectedBorderRadius
        activeBorderRadius={activeBorderRadius}
        setActiveBorderRadius={setActiveBorderRadius}
      />
      <BorderRadiusInputs
        borderRadius={borderRadius}
        onChange={onChange}
        activeBorderRadius={activeBorderRadius}
      />
    </div>
  );
}

export default BorderRadiusTab;
