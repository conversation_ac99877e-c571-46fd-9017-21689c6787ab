import {
  ButtonActionType,
  ButtonDesignSize,
  ButtonDesignType,
  ImageActionType,
  ImageObjectFit,
  AppStoreButtonActionType,
  AppStoreButtonStyle,
  FormPostSubmitType,
  FieldType,
  AutoCompleteType,
  Column,
  VideoSettings,
  Row,
  AppStoreButtonsSettings,
  ButtonsSettings,
  ButtonSettings,
  TextSettings,
  ImageSettings,
  FormSettings,
  WuiltContext,
  ElementType,
  SectionElement,
  BackgroundType,
  SocialLinksSettings,
  IconShape,
  TextObject,
  SocialLinkName,
  CounterSettings,
} from "@wuilt/section-preview";
import { nanoid } from "nanoid";
import { createFormApiMutation } from "../mutations/formMutations";
import { getDefaultFormTexts } from "./getTexts";

export function getDefaultColumn(): Column {
  return {
    id: `Column:${nanoid()}`,
    elements: [],
    settings: {
      background: [
        {
          id: `Background:${nanoid()}`,
          type: BackgroundType.Color,
          color: "#FFFFFF01",
          image: undefined,
          gradient: undefined,
        },
      ],
      layout: {
        alignment: { horizontal: "start", vertical: "start" },
        padding: { bottom: 16, left: 16, top: 16, right: 16 },
      },
    },
  };
}

export function getDefaultRow(): Row {
  return {
    id: `Row:${nanoid()}`,
    columns: [getDefaultColumn()],
    settings: {
      background: [
        {
          id: `Background:${nanoid()}`,
          type: BackgroundType.Color,
          color: "#FFFFFF01",
          image: undefined,
          gradient: undefined,
        },
      ],
      layout: {
        padding: { bottom: 16, left: 16, top: 16, right: 16 },
      },
    },
  };
}

function getDefaultText(extra: TextSettings = {}): {
  settings: TextSettings;
  texts?: TextObject;
} {
  const tag = extra?.tag ?? "p";
  const idAttribute =
    extra?.tag === "h2"
      ? "global-heading-2-styles"
      : extra?.tag === "p"
      ? "global-paragraph-1-styles"
      : "";
  const textId = `Content:${nanoid()}`;
  const texts = {
    [textId]: `<${tag} id="${idAttribute}">Text Content</${tag}>`,
  };
  const settings = {
    tag,
    textId,
  };
  return {
    settings,
    texts,
  };
}

function getDefaultVideo(): { settings: VideoSettings } {
  const settings = {
    url: "https://www.youtube.com/watch?v=LY1uok8FArw",
    autoplay: false,
    loop: false,
    muted: false,
    controls: true,
    width: "100%",
    height: "500px",
  };
  return {
    settings,
  };
}

export function getDefaultButton(): {
  settings: ButtonSettings;
  texts?: TextObject;
} {
  const textId = `Content:${nanoid()}`;
  const texts = {
    [textId]: "Button",
  };
  const settings = {
    id: `Button:${nanoid()}`,
    textId,
    action: { type: ButtonActionType.External_Link },
    design: {
      fill: true,
      borderRadius: 10,
      size: ButtonDesignSize.Large,
      type: ButtonDesignType.Primary,
    },
  };
  return {
    settings,
    texts,
  };
}

function getDefaultButtons(): {
  settings: ButtonsSettings;
  texts?: TextObject;
} {
  const primaryTextId = `Content:${nanoid()}`;
  const secondaryTextId = `Content:${nanoid()}`;
  const texts = {
    [primaryTextId]: "Button",
    [secondaryTextId]: "Button",
  };
  const settings = {
    buttons: [
      {
        id: `Button:${nanoid()}`,
        textId: primaryTextId,
        action: { type: ButtonActionType.External_Link },
        design: {
          borderRadius: 10,
          size: ButtonDesignSize.Large,
          type: ButtonDesignType.Primary,
        },
      },
      {
        id: `Button:${nanoid()}`,
        textId: secondaryTextId,
        action: { type: ButtonActionType.External_Link },
        design: {
          borderRadius: 10,
          size: ButtonDesignSize.Large,
          type: ButtonDesignType.Secondary,
        },
      },
    ],
  };
  return {
    settings,
    texts,
  };
}

function getDefaultImage(): { settings: ImageSettings } {
  const settings = {
    desktop: {
      action: {
        type: ImageActionType?.Do_Nothing,
      },
      objectFit: ImageObjectFit?.Cover,
    },
  };
  return {
    settings,
  };
}

export function getDefaultAppStoreButtons(): {
  settings: AppStoreButtonsSettings;
} {
  const settings = {
    appStoreButtons: [
      {
        id: `AppStoreButton:${nanoid()}`,
        action: { type: AppStoreButtonActionType.Apple },
      },
      {
        id: `AppStoreButton:${nanoid()}`,
        action: { type: AppStoreButtonActionType.Google },
      },
    ],
    design: {
      size: ButtonDesignSize.Large,
      style: AppStoreButtonStyle.Brand,
    },
  };
  return {
    settings,
  };
}
export function getDefaultAppStoreButton(): {
  settings: any;
} {
  const settings = {
    action: {
      type: AppStoreButtonActionType.Apple,
    },
    style: {
      size: ButtonDesignSize.Large,
      style: AppStoreButtonStyle.Brand,
    },
  };
  return {
    settings,
  };
}

export function getDefaultSocialLinks(): {
  settings: SocialLinksSettings;
} {
  const settings = {
    socialLinks: [
      {
        id: `SocialIcon:${nanoid()}`,
        name: SocialLinkName.Facebook,
      },
      {
        id: `SocialIcon:${nanoid()}`,
        name: SocialLinkName.Instagram,
      },
    ],
    iconStyle: {
      shape: IconShape.Filled,
      size: "20",
      color: "#1D2939",
    },
  };
  return {
    settings,
  };
}

export function getDefaultCounter(): { settings: CounterSettings } {
  const settings = {
    counterValue: {
      value: "90%",
      color: "#1D2939",
      size: 48,
    },
    LabelEnabled: true,
    counterLabel: {
      value: "Clients Served",
      color: "#1D2939",
      size: 16,
    },
    animationSpeed: 50,
  } as CounterSettings;
  return {
    settings,
  };
}

export async function getDefaultForm(
  _extra: FormSettings,
  wuiltContext: WuiltContext
): Promise<{ settings: FormSettings; texts?: TextObject }> {
  const { newTextIds, newTextObject } = getDefaultFormTexts();

  const settings: FormSettings = {
    formName: `First Form`,
    formPostSubmit: {
      submitType: FormPostSubmitType.Message,
      redirectType: ButtonActionType.External_Link,
      failureMessage: { textId: newTextIds.failureMessageTextId },
      successMessage: {
        textId: newTextIds.successMessageTextId,
      },
    },
    formButton: {
      id: `Button:${nanoid()}`,
      textId: newTextIds.formButtonTextId,
      action: { type: ButtonActionType.Submit },
      design: {
        borderRadius: 10,
        size: ButtonDesignSize.Large,
        type: ButtonDesignType.Primary,
      },
    },
    fields: [
      {
        id: `Field:${nanoid()}`,
        type: FieldType.Text,
        settings: {
          label: { textId: newTextIds.nameLabelTextId },
          placeholder: { textId: newTextIds.namePlaceholderTextId },
          description: { textId: newTextIds.nameDescriptionTextId },
          errorMessage: { textId: newTextIds.nameErrorMessageTextId },
          autoComplete: AutoCompleteType.Name,
        },
      },
      {
        id: `Field:${nanoid()}`,
        type: FieldType.Email,
        settings: {
          label: { textId: newTextIds.emailLabelTextId },
          placeholder: { textId: newTextIds.emailPlaceholderTextId },
          description: { textId: newTextIds.emailDescriptionTextId },
          errorMessage: { textId: newTextIds.emailErrorMessageTextId },
          autoComplete: AutoCompleteType.Email,
        },
      },
      {
        id: `Field:${nanoid()}`,
        type: FieldType.Phone,
        settings: {
          label: { textId: newTextIds.phoneLabelTextId },
          placeholder: { textId: newTextIds.phonePlaceholderTextId },
          description: { textId: newTextIds.phoneDescriptionTextId },
          errorMessage: { textId: newTextIds.phoneErrorMessageTextId },
          autoComplete: AutoCompleteType.Phone,
        },
      },
      {
        id: `Field:${nanoid()}`,
        type: FieldType.TextArea,
        settings: {
          label: { textId: newTextIds.messageLabelTextId },
          placeholder: { textId: newTextIds.messagePlaceholderTextId },
          description: { textId: newTextIds.messageDescriptionTextId },
          errorMessage: { textId: newTextIds.messageErrorMessageTextId },
          autoComplete: "" as any,
        },
      },
    ],
    notifications: {
      emails: [wuiltContext?.props?.userEmail!],
    },
  };
  const formId = await createFormApiMutation(wuiltContext, {
    settings,
    id: "will be ignored",
  });
  const formSettings = { formId, ...settings };
  return { settings: formSettings, texts: newTextObject };
}

const ElementsDefaultMapper = {
  Text: getDefaultText,
  Image: getDefaultImage,
  Button: getDefaultButton,
  Buttons: getDefaultButtons,
  AppStoreButtons: getDefaultAppStoreButtons,
  Form: getDefaultForm,
  Video: getDefaultVideo,
  SocialLinks: getDefaultSocialLinks,
  Counter: getDefaultCounter,
  AppStoreButton: getDefaultAppStoreButton,
};

export async function getDefaultElement(
  type: ElementType,
  extra: SectionElement["settings"] = {},
  wuiltContext: WuiltContext = {}
): Promise<any> {
  const { settings, texts }: { settings: any; texts?: TextObject } =
    await ElementsDefaultMapper[type](extra as any, wuiltContext);

  const newElement = {
    id: `${type}:${nanoid()}`,
    type,
    settings,
  };

  const newTexts = {
    ...texts,
  };
  return {
    newElement,
    newTexts,
  };
}
