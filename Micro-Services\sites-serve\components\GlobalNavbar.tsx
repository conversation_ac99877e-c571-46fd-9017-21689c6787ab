'use client';

import React from 'react';
import Section from './Section';

interface GlobalNavbarProps {
  sectionData: any;
  locale: string;
  pages: any[];
  animation?: {
    enabled: boolean;
    duration: number;
    easing: string;
  };
}

export default function GlobalNavbar({
  sectionData,
  locale,
  pages,
  animation,
}: GlobalNavbarProps) {
  return (
    <header style={{ position: 'sticky', top: 0, zIndex: 1000 }}>
      <Section
        id={sectionData.id}
        sectionData={sectionData}
        animation={animation}
        index={-1}
      />
    </header>
  );
}
