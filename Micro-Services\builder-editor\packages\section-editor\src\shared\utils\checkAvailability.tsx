import { UtilityPageType, WuiltContext } from "@wuilt/section-preview";

export const checkAvailability = (wuiltContext: WuiltContext) => {
  const planId = wuiltContext?.props?.site?.plan?.id;
  const activePage = wuiltContext?.props?.activePage;
  const isErrorPage = activePage?.type === UtilityPageType.NOT_FOUND_PAGE;
  const isMaintenancePage =
    activePage?.type === UtilityPageType.UNDER_MAINTENANCE_PAGE;
  const isPremiumOrFree = planId === 1 || planId === 15 || planId === 18;
  const isNotAvailable = (isMaintenancePage || isErrorPage) && isPremiumOrFree;

  return isNotAvailable;
};
