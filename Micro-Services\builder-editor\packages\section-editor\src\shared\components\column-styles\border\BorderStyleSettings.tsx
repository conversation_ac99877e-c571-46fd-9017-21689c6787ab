import {
  Label,
  BorderStyleDashedIcon,
  BorderStyleDottedIcon,
  BorderStyleSolidIcon,
  CloseIcon,
  utils,
} from "@wuilt/quilt";
import { FormattedMessage } from "react-intl";
import styled, { css } from "styled-components";
import { BorderPosition, updateBorderValue } from "./helper";
import { Borders } from "@wuilt/section-preview";

const BORDER_STYLES = [
  {
    id: 1,
    Icon: <CloseIcon />,
    styleValue: "none",
  },
  {
    id: 2,
    Icon: <BorderStyleSolidIcon />,
    styleValue: "solid",
  },
  {
    id: 3,
    Icon: <BorderStyleDashedIcon />,
    styleValue: "dashed",
  },
  {
    id: 4,
    Icon: <BorderStyleDottedIcon />,
    styleValue: "dotted",
  },
] as const;

interface BorderProps {
  borders: Borders | undefined;
  onChange: (v: Borders) => void;
  activeBorder: BorderPosition;
}

function BorderStyleSettings({ borders, onChange, activeBorder }: BorderProps) {
  return (
    <>
      <Label>
        <FormattedMessage defaultMessage="Style" id="7mL9QE" />
      </Label>
      <StyledRowLocal>
        {BORDER_STYLES.map(({ Icon, id, styleValue }) => {
          return (
            <StyledIconWrapper
              onClick={() => {
                onChange({
                  ...borders,
                  isAllSides: activeBorder === BorderPosition.all,
                  borderStyle: {
                    ...borders?.borderStyle,
                    ...updateBorderValue(activeBorder, styleValue),
                  },
                });
              }}
              isSelected={styleValue === borders?.borderStyle?.[activeBorder]}
              key={id}
            >
              {Icon}
            </StyledIconWrapper>
          );
        })}
      </StyledRowLocal>
    </>
  );
}

export default BorderStyleSettings;

const StyledRowLocal = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: solid 1px ${utils.color("ink", "lighter")};
  border-radius: 4px;
  height: 34px;
  width: 70%;
`;

const StyledIconWrapper = styled.div<{ isSelected: boolean }>`
  cursor: pointer;
  padding: 8px 0;
  width: calc(100% / 3);
  height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${utils.color("ink", "light")};

  ${({ isSelected }) =>
    isSelected &&
    css`
      background-color: ${utils.color("product", "normal")};
      color: ${utils.color("white", "normal")};
      fill: ${utils.color("white", "normal")};
    `}

  &:nth-child(2) {
    padding: 8px 0;
    ${({ theme }) =>
      `${
        theme.rtl
          ? "border-right: solid 1px #bac7d5;"
          : "border-left: solid 1px #bac7d5;"
      }`};
    border-top: none;
    border-bottom: none;
  }

  &:nth-child(3) {
    padding: 8px 0;
    border: solid 1px ${utils.color("ink", "lighter")};
    border-top: none;
    border-bottom: none;
  }
`;
