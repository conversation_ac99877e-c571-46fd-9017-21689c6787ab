// Enhanced section designs for Fluid Engine based on Old Builder's sections-designs package
// This provides fluid-engine compatible section templates with precise grid layouts

export interface FluidSectionDesign {
  id: string;
  category: string;
  name: string;
  description: string;
  thumbnail: string;
  elements: any[];
  settings: {
    grid: {
      breakpoints: {
        desktop: { columns: number; rows: number };
        tablet: { columns: number; rows: number };
        mobile: { columns: number; rows: number };
      };
      columnGap: number;
      rowGap: number;
      shouldFullWidth: boolean;
    };
    styles: {
      padding: { top: number; bottom: number };
      background: { color?: string; type?: string; image?: string };
    };
  };
}

// Hero section designs with fluid engine layouts
export const fluidHeroDesigns: FluidSectionDesign[] = [
  {
    id: 'hero-fluid-001',
    category: 'hero',
    name: 'Hero with Image Right',
    description: 'Professional landing page header with image on the right',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=Hero+Right',
    elements: [
      {
        id: 'hero-title',
        type: 'text',
        props: {
          content: 'Build Amazing Websites',
          fontSize: '48px',
          fontWeight: 'bold',
          color: '#1a202c',
          textAlign: 'left',
        },
        layout: {
          desktop: { offset: { top: 2, left: 1 }, size: { width: 10, height: 3 } },
          tablet: { offset: { top: 1, left: 1 }, size: { width: 10, height: 3 } },
          mobile: { offset: { top: 1, left: 0 }, size: { width: 12, height: 4 } },
        },
      },
      {
        id: 'hero-subtitle',
        type: 'text',
        props: {
          content: 'Create stunning websites with our powerful drag-and-drop builder. No coding required.',
          fontSize: '18px',
          color: '#4a5568',
          textAlign: 'left',
        },
        layout: {
          desktop: { offset: { top: 5, left: 1 }, size: { width: 10, height: 2 } },
          tablet: { offset: { top: 4, left: 1 }, size: { width: 10, height: 2 } },
          mobile: { offset: { top: 5, left: 0 }, size: { width: 12, height: 3 } },
        },
      },
      {
        id: 'hero-primary-cta',
        type: 'button',
        props: {
          text: 'Get Started',
          backgroundColor: '#3182ce',
          color: '#ffffff',
          borderRadius: '8px',
          padding: '16px 32px',
          fontSize: '18px',
          fontWeight: 'bold',
        },
        layout: {
          desktop: { offset: { top: 8, left: 1 }, size: { width: 4, height: 2 } },
          tablet: { offset: { top: 7, left: 1 }, size: { width: 4, height: 2 } },
          mobile: { offset: { top: 8, left: 0 }, size: { width: 6, height: 2 } },
        },
      },
      {
        id: 'hero-secondary-cta',
        type: 'button',
        props: {
          text: 'Learn More',
          backgroundColor: 'transparent',
          color: '#3182ce',
          borderRadius: '8px',
          padding: '16px 32px',
          fontSize: '18px',
          fontWeight: 'bold',
          border: '2px solid #3182ce',
        },
        layout: {
          desktop: { offset: { top: 8, left: 6 }, size: { width: 4, height: 2 } },
          tablet: { offset: { top: 7, left: 6 }, size: { width: 4, height: 2 } },
          mobile: { offset: { top: 8, left: 7 }, size: { width: 5, height: 2 } },
        },
      },
      {
        id: 'hero-social-links',
        type: 'social-links',
        props: {
          platforms: [
            { name: 'facebook', url: '#', icon: 'facebook' },
            { name: 'twitter', url: '#', icon: 'twitter' },
            { name: 'linkedin', url: '#', icon: 'linkedin' },
          ],
          layout: 'horizontal',
          size: '24px',
          spacing: '16px',
        },
        layout: {
          desktop: { offset: { top: 11, left: 1 }, size: { width: 6, height: 1 } },
          tablet: { offset: { top: 10, left: 1 }, size: { width: 6, height: 1 } },
          mobile: { offset: { top: 11, left: 0 }, size: { width: 8, height: 1 } },
        },
      },
      {
        id: 'hero-image',
        type: 'image',
        props: {
          src: '/api/placeholder-image?width=600&height=400&text=Hero+Image',
          alt: 'Hero Image',
          objectFit: 'cover',
          borderRadius: '12px',
        },
        layout: {
          desktop: { offset: { top: 1, left: 13 }, size: { width: 10, height: 10 } },
          tablet: { offset: { top: 12, left: 1 }, size: { width: 10, height: 8 } },
          mobile: { offset: { top: 13, left: 0 }, size: { width: 12, height: 8 } },
        },
      },
    ],
    settings: {
      grid: {
        breakpoints: {
          desktop: { columns: 24, rows: 14 },
          tablet: { columns: 12, rows: 22 },
          mobile: { columns: 12, rows: 24 },
        },
        columnGap: 20,
        rowGap: 10,
        shouldFullWidth: false,
      },
      styles: {
        padding: { top: 80, bottom: 80 },
        background: { color: '#ffffff' },
      },
    },
  },
  {
    id: 'hero-fluid-002',
    category: 'hero',
    name: 'Centered Hero with Background',
    description: 'Centered hero section with background image overlay',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=Centered+Hero',
    elements: [
      {
        id: 'hero-title-centered',
        type: 'text',
        props: {
          content: 'Your Success Starts Here',
          fontSize: '56px',
          fontWeight: 'bold',
          color: '#ffffff',
          textAlign: 'center',
        },
        layout: {
          desktop: { offset: { top: 3, left: 4 }, size: { width: 16, height: 3 } },
          tablet: { offset: { top: 2, left: 1 }, size: { width: 10, height: 4 } },
          mobile: { offset: { top: 2, left: 0 }, size: { width: 12, height: 5 } },
        },
      },
      {
        id: 'hero-subtitle-centered',
        type: 'text',
        props: {
          content: 'Join thousands of satisfied customers who have transformed their businesses with our platform.',
          fontSize: '20px',
          color: '#e2e8f0',
          textAlign: 'center',
        },
        layout: {
          desktop: { offset: { top: 7, left: 6 }, size: { width: 12, height: 2 } },
          tablet: { offset: { top: 7, left: 1 }, size: { width: 10, height: 3 } },
          mobile: { offset: { top: 8, left: 0 }, size: { width: 12, height: 4 } },
        },
      },
      {
        id: 'hero-cta-centered',
        type: 'button',
        props: {
          text: 'Start Free Trial',
          backgroundColor: '#ed8936',
          color: '#ffffff',
          borderRadius: '12px',
          padding: '20px 40px',
          fontSize: '20px',
          fontWeight: 'bold',
        },
        layout: {
          desktop: { offset: { top: 10, left: 10 }, size: { width: 4, height: 2 } },
          tablet: { offset: { top: 11, left: 4 }, size: { width: 4, height: 2 } },
          mobile: { offset: { top: 13, left: 3 }, size: { width: 6, height: 2 } },
        },
      },
    ],
    settings: {
      grid: {
        breakpoints: {
          desktop: { columns: 24, rows: 14 },
          tablet: { columns: 12, rows: 16 },
          mobile: { columns: 12, rows: 18 },
        },
        columnGap: 20,
        rowGap: 10,
        shouldFullWidth: true,
      },
      styles: {
        padding: { top: 120, bottom: 120 },
        background: {
          type: 'image',
          image: '/api/placeholder-image?width=1920&height=800&text=Background',
          color: 'rgba(0, 0, 0, 0.5)', // Overlay
        },
      },
    },
  },
];

// Features section designs with fluid engine layouts
export const fluidFeaturesDesigns: FluidSectionDesign[] = [
  {
    id: 'features-fluid-001',
    category: 'features',
    name: 'Features with Side Image',
    description: '3-column features grid with side image',
    thumbnail: '/api/placeholder-image?width=300&height=200&text=Features+Grid',
    elements: [
      {
        id: 'features-title',
        type: 'text',
        props: {
          content: 'Powerful Features',
          fontSize: '36px',
          fontWeight: 'bold',
          color: '#1a202c',
          textAlign: 'left',
        },
        layout: {
          desktop: { offset: { top: 1, left: 1 }, size: { width: 12, height: 2 } },
          tablet: { offset: { top: 1, left: 1 }, size: { width: 10, height: 2 } },
          mobile: { offset: { top: 1, left: 0 }, size: { width: 12, height: 3 } },
        },
      },
      // Feature 1
      {
        id: 'feature-1-icon',
        type: 'icon',
        props: {
          iconName: 'rocket',
          size: '48px',
          color: '#3182ce',
        },
        layout: {
          desktop: { offset: { top: 4, left: 1 }, size: { width: 2, height: 2 } },
          tablet: { offset: { top: 4, left: 1 }, size: { width: 2, height: 2 } },
          mobile: { offset: { top: 5, left: 0 }, size: { width: 2, height: 2 } },
        },
      },
      {
        id: 'feature-1-content',
        type: 'text',
        props: {
          content: '<h4>Lightning Fast</h4><p>Build websites in minutes, not hours with our optimized platform.</p>',
          fontSize: '16px',
          color: '#4a5568',
        },
        layout: {
          desktop: { offset: { top: 4, left: 4 }, size: { width: 8, height: 3 } },
          tablet: { offset: { top: 4, left: 4 }, size: { width: 6, height: 3 } },
          mobile: { offset: { top: 5, left: 3 }, size: { width: 9, height: 4 } },
        },
      },
      // Feature 2
      {
        id: 'feature-2-icon',
        type: 'icon',
        props: {
          iconName: 'mobile',
          size: '48px',
          color: '#3182ce',
        },
        layout: {
          desktop: { offset: { top: 8, left: 1 }, size: { width: 2, height: 2 } },
          tablet: { offset: { top: 8, left: 1 }, size: { width: 2, height: 2 } },
          mobile: { offset: { top: 10, left: 0 }, size: { width: 2, height: 2 } },
        },
      },
      {
        id: 'feature-2-content',
        type: 'text',
        props: {
          content: '<h4>Responsive Design</h4><p>Your websites will look perfect on all devices automatically.</p>',
          fontSize: '16px',
          color: '#4a5568',
        },
        layout: {
          desktop: { offset: { top: 8, left: 4 }, size: { width: 8, height: 3 } },
          tablet: { offset: { top: 8, left: 4 }, size: { width: 6, height: 3 } },
          mobile: { offset: { top: 10, left: 3 }, size: { width: 9, height: 4 } },
        },
      },
      // Side Image
      {
        id: 'features-image',
        type: 'image',
        props: {
          src: '/api/placeholder-image?width=400&height=500&text=Features',
          alt: 'Features Image',
          objectFit: 'cover',
          borderRadius: '8px',
        },
        layout: {
          desktop: { offset: { top: 1, left: 15 }, size: { width: 8, height: 12 } },
          tablet: { offset: { top: 12, left: 1 }, size: { width: 10, height: 8 } },
          mobile: { offset: { top: 15, left: 0 }, size: { width: 12, height: 8 } },
        },
      },
    ],
    settings: {
      grid: {
        breakpoints: {
          desktop: { columns: 24, rows: 14 },
          tablet: { columns: 12, rows: 22 },
          mobile: { columns: 12, rows: 25 },
        },
        columnGap: 20,
        rowGap: 10,
        shouldFullWidth: false,
      },
      styles: {
        padding: { top: 80, bottom: 80 },
        background: { color: '#f7fafc' },
      },
    },
  },
];

// All fluid section designs
export const fluidSectionDesigns = {
  hero: fluidHeroDesigns,
  features: fluidFeaturesDesigns,
};

// Helper function to get fluid section design
export function getFluidSectionDesign(category: string, designId: string): FluidSectionDesign | null {
  const categoryDesigns = fluidSectionDesigns[category as keyof typeof fluidSectionDesigns];
  if (!categoryDesigns) return null;
  
  return categoryDesigns.find(design => design.id === designId) || null;
}
