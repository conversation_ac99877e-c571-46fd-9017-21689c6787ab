import React from "react";
import {
  <PERSON>,
  Divider,
  FormLabel,
  InputGroup,
  InputRightElement,
  Stack,
  Switch,
  Text,
} from "@chakra-ui/react";
import NumberInput from "../../../components/NumberInput";
import { FormattedMessage } from "react-intl";
import trackEvent from "../../../shared/utils/trackEvent";
import { Layout, UpdateDataFunc } from "@wuilt/section-preview";

interface CustomSectionLayoutProps {
  layout: Layout | undefined;
  updateUi: UpdateDataFunc;
}
const PX = "Px";

const CustomSectionLayout: React.FC<CustomSectionLayoutProps> = ({
  layout,
  updateUi,
}) => {
  const updateLayout = (newLayout: Layout) => {
    updateUi((prev) => {
      return {
        ...prev,
        settings: { ...prev.settings, layout: newLayout },
      };
    });
  };

  return (
    <Stack gap="16px" align="start">
      <Stack gap="16px" direction="row" align="start">
        <Switch
          mb="0"
          size="md"
          isChecked={!!layout?.fullHeight}
          onChange={(e) => {
            const fullHeight = e.target.checked;
            updateLayout({ ...layout, fullHeight });
            fullHeight && trackEvent("sectionHeightStretched");
          }}
        />
        <Box>
          <Text color="black" fontWeight="700" fontSize="12px">
            <FormattedMessage
              defaultMessage="Stretch to full height"
              id="KZ9cfN"
            />
          </Text>
          <Text color="gray.500" fontSize="14px" fontWeight="400">
            <FormattedMessage
              defaultMessage="Make the section take the full height of the screen"
              id="tHhNf/"
            />
          </Text>
        </Box>
      </Stack>
      <Divider />
      <Box>
        <Text fontWeight="500" fontSize="12px" color="gray.500">
          <FormattedMessage defaultMessage="PADDING" id="k9TVDP" />
        </Text>
        <Stack gap="16px" direction="row">
          <Box>
            <FormLabel>
              <FormattedMessage defaultMessage="Top" id="X/9XdX" />
            </FormLabel>
            <InputGroup>
              <NumberInput
                value={layout?.padding?.top || 0}
                min={0}
                onChange={(_, value: any) => {
                  updateLayout({
                    ...layout,
                    padding: { ...layout?.padding!, top: value },
                  });
                }}
              />
              <InputRightElement
                marginInlineEnd={25}
                fontSize="14px"
                color="GrayText"
              >
                {PX}
              </InputRightElement>
            </InputGroup>
          </Box>
          <Box>
            <FormLabel>
              <FormattedMessage defaultMessage="Bottom" id="hFAjsX" />
            </FormLabel>
            <InputGroup>
              <NumberInput
                value={layout?.padding?.bottom || 0}
                min={0}
                onChange={(_, value: any) => {
                  updateLayout({
                    ...layout,
                    padding: { ...layout?.padding!, bottom: value },
                  });
                }}
              />
              <InputRightElement
                marginInlineEnd={25}
                fontSize="14px"
                color="GrayText"
              >
                {PX}
              </InputRightElement>
            </InputGroup>
          </Box>
        </Stack>
      </Box>
    </Stack>
  );
};

export default CustomSectionLayout;
