import { dataContext } from "../../components";

const EVENTS = {
  //Custom section
  sectionHeightStretched: "Custom section height stretched",
  sectionBackgroundAdded: "Custom section background added",
  sectionBackgroundDeleted: "Custom section background deleted",
  sectionVideoBackgroundEnabled: "Custom section video background enabled",
  sectionVideoBackgroundDisabled: "Custom section video background disabled",

  //Row
  rowAdded: "Custom section row added",
  rowDuplicated: "Custom section row duplicated",
  rowDeleted: "Custom section row deleted",
  rowBackgroundAdded: "Row background added",
  rowBackgroundDeleted: "Row background deleted",
  rowVideoBackgroundEnabled: "Row video background enabled",
  rowVideoBackgroundDisabled: "Row video background disabled",

  //Column
  columnAdded: "Custom section column added",
  columnDuplicated: "Custom section column duplicated",
  columnDeleted: "Custom section column deleted",
  columnBackgroundAdded: "Column background added",
  columnBackgroundDeleted: "Column background deleted",
  columnVideoBackgroundEnabled: "Column video background enabled",
  columnVideoBackgroundDisabled: "Column video background disabled",

  //Element
  elementAdded: "Custom section eLement added",
  elementDuplicated: "Custom section eLement duplicated",
  elementDeleted: "Custom section eLement deleted",

  //Form
  formFieldAdded: "Custom section form field added",
  formFieldDeleted: "Custom section form field deleted",
  formSubmissionEmailAdded:
    " Form submission email in custom section form element added",
  formSubmissionEmailDeleted:
    " Form submission email in custom section form element deleted",
  replyToEnabled: "Reply to button enabled",
  replyToDisabled: "Reply to button disabled",
};

export default function trackEvent(
  eventName: keyof typeof EVENTS,
  args?: { [key: string]: string | number }
) {
  dataContext?.props?.fireTrackEvent?.(EVENTS[eventName], args);
}
