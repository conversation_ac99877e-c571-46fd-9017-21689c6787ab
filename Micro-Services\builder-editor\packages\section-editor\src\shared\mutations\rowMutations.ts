import _cloneDeep from "lodash/cloneDeep";
import _isEqual from "lodash/isEqual";
import { getDefaultRow } from "../utils/getDefault";
import { resizeColumnsFallbackGridTemplates } from "../utils/resize-column-utils";
import { nanoid } from "nanoid";
import { duplicateColumnMutation } from "./columnMutations";
import {
  Column,
  CustomSection,
  Row,
  WuiltContext,
} from "@wuilt/section-preview";
import { getAllRowTexts } from "../utils/getTexts";
import { getRowForms } from "../utils/getForms";
import { deleteFormApiMutation } from "./formMutations";

export const addRowMutation = (
  section: CustomSection,
  rowIndex: number,
  location: "before" | "after" = "after"
) => {
  const isToAddBefore = location === "before";
  const newColumnIndex = isToAddBefore ? rowIndex : rowIndex + 1;
  const newColumn = getDefaultRow();
  const cloned = _cloneDeep(section);
  const rows = cloned?.rows;
  rows?.splice(newColumnIndex, 0, newColumn);
  cloned.rows = rows;
  return cloned;
};

export const updateRowGapMutation = (
  section: CustomSection,
  rowIndex: number,
  gap: number
) => {
  const cloned = _cloneDeep(section);
  cloned.rows[rowIndex].settings = {
    ...cloned.rows[rowIndex]?.settings,
    gap,
  };
  return cloned;
};

export const deleteRowMutation = (
  section: CustomSection,
  rowIndex: number,
  wuiltContext: WuiltContext
) => {
  const cloned = _cloneDeep(section);
  const rows = cloned?.rows;
  const row = rows[rowIndex];
  const text = cloned?.text;
  const deletedTexts = getAllRowTexts(row);
  deletedTexts?.forEach((textId: string) => delete text[textId]);
  const rowFormsIds = getRowForms(row);
  rowFormsIds && deleteFormApiMutation(rowFormsIds, wuiltContext);
  rows?.splice(rowIndex, 1);
  cloned.rows = rows;
  return cloned;
};

export const sortRowColumnsMutation = (
  section: CustomSection,
  rowIndex: number,
  sortedColumns: Column[]
) => {
  const cloned = _cloneDeep(section);
  cloned.rows[rowIndex].columns = sortedColumns;
  return cloned;
};

export const sortRowMutation = (section: CustomSection, sortedRows: Row[]) => {
  const cloned = _cloneDeep(section);
  cloned.rows = sortedRows;
  return cloned;
};

export const updateRowGridTemplatesMutation = (
  section: CustomSection,
  rowIndex: number,
  gridTemplates: number[]
) => {
  const oldGridTemplates =
    section?.rows[rowIndex]?.settings?.layout?.gridTemplates || [];
  if (_isEqual(oldGridTemplates, gridTemplates)) return section;
  const cloned = _cloneDeep(section);
  cloned.rows[rowIndex].settings = {
    ...cloned.rows[rowIndex].settings,
    layout: {
      ...cloned.rows[rowIndex].settings?.layout,
      gridTemplates,
    },
  };
  return cloned;
};

export const resetRowGridTemplatesMutation = (
  section: CustomSection,
  rowIndex: number
) => {
  const cloned = _cloneDeep(section);
  const defaultGridTemplates = resizeColumnsFallbackGridTemplates(
    cloned.rows[rowIndex]
  );
  cloned.rows[rowIndex].settings = {
    ...cloned.rows[rowIndex].settings,
    layout: {
      ...cloned.rows[rowIndex].settings?.layout,
      gridTemplates: defaultGridTemplates,
    },
  };
  return cloned;
};

export const updateRowMutation = (
  section: CustomSection,
  rowIndex: number,
  newSettings: Row["settings"]
) => {
  const cloned = _cloneDeep(section);
  cloned.rows[rowIndex].settings = newSettings;
  return cloned;
};

export const duplicateRowMutation = async (
  section: CustomSection,
  rowIndex: number,
  wuiltContext: WuiltContext = {}
) => {
  const cloned = _cloneDeep(section);
  const rows = cloned?.rows;
  const newRow = { ...rows[rowIndex], id: `Row:${nanoid()}` };
  const newColumns = await Promise.all(
    newRow?.columns?.map((e, columnIndex) => {
      return duplicateColumnMutation(
        section,
        rowIndex,
        columnIndex,
        true,
        wuiltContext
      );
    })
  );

  newRow.columns = newColumns?.map((e) => e?.newColumn);
  const newTextsArray = newColumns?.reduce(
    (acc, curr) => ({
      ...acc,
      ...curr?.newColumnTexts,
    }),
    {}
  );

  const newRowTexts = Object.assign({}, newTextsArray);

  cloned.text = {
    ...cloned.text,
    ...newRowTexts,
  };
  rows?.splice(rowIndex + 1, 0, newRow);
  cloned.rows = rows;
  return cloned;
};

export const DuplicateNewColumn = (
  section: CustomSection,
  rowIndex: number,
  columnIndex: number
) => {
  const cloned = _cloneDeep(section);
  const columns = cloned?.rows[rowIndex]?.columns;
  const oldColumns = columns[columnIndex];
  const newCols = { ...oldColumns, id: `Column:${nanoid()}` };
  return newCols;
};
