import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export async function getSiteData(subdomain: string) {
  try {
    // First, try to get site by custom domain
    let { data: site, error } = await supabase
      .from('websites')
      .select(`
        *,
        pages (
          id,
          title,
          slug,
          meta,
          sections,
          is_published
        )
      `)
      .eq('custom_domain', subdomain)
      .eq('is_published', true)
      .single();

    // If not found by custom domain, try by subdomain
    if (error || !site) {
      ({ data: site, error } = await supabase
        .from('websites')
        .select(`
          *,
          pages (
            id,
            title,
            slug,
            meta,
            sections,
            is_published
          )
        `)
        .eq('subdomain', subdomain)
        .eq('is_published', true)
        .single());
    }

    if (error || !site) {
      console.error('Site not found:', error);
      return null;
    }

    // Filter only published pages
    site.pages = site.pages?.filter((page: any) => page.is_published) || [];

    return site;
  } catch (error) {
    console.error('Error fetching site data:', error);
    return null;
  }
}

export async function getPageData(siteId: string, pageSlug: string) {
  try {
    const { data: page, error } = await supabase
      .from('pages')
      .select('*')
      .eq('website_id', siteId)
      .eq('slug', pageSlug)
      .eq('is_published', true)
      .single();

    if (error || !page) {
      console.error('Page not found:', error);
      return null;
    }

    return page;
  } catch (error) {
    console.error('Error fetching page data:', error);
    return null;
  }
}

export async function getWebsiteSettings(websiteId: string) {
  try {
    const { data: settings, error } = await supabase
      .from('website_settings')
      .select('*')
      .eq('website_id', websiteId)
      .single();

    if (error) {
      console.error('Error fetching website settings:', error);
      return null;
    }

    return settings;
  } catch (error) {
    console.error('Error fetching website settings:', error);
    return null;
  }
}

export async function submitForm(formData: any, websiteId: string) {
  try {
    const { data, error } = await supabase
      .from('form_submissions')
      .insert({
        website_id: websiteId,
        form_data: formData,
        submitted_at: new Date().toISOString(),
      });

    if (error) {
      console.error('Error submitting form:', error);
      return { success: false, error: error.message };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error submitting form:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}
