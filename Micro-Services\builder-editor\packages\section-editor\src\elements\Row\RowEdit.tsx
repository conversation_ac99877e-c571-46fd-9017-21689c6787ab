import React from "react";
import {
  ElementsViewer,
  Row,
  UpdateDataFunc,
  WuiltContext,
} from "@wuilt/section-preview";
import {
  addRowMutation,
  deleteRowMutation,
  duplicateRowMutation,
} from "../../shared/mutations";
import RowMenu, { StyledRowMenu } from "./components/RowMenu";
import styled, { css } from "styled-components";
import { checkAvailability } from "../../shared/utils/checkAvailability";

export interface RowEditProps {
  row: Row;
  isSingleRow: boolean;
  rowIndex: number;
  isSorting?: boolean;
  disableAdding: boolean;
  isSingleColumn: boolean;
  isLastRow: boolean;
  wuiltContext: WuiltContext;
  children: React.ReactNode;
  updateAndMutate: UpdateDataFunc;
  updateUi: UpdateDataFunc;
  mutateApi: UpdateDataFunc;
  onUploadImage: (cb: (src: string) => void) => void;
}

const RowEdit: React.FC<RowEditProps> = ({
  row,
  rowIndex,
  disableAdding,
  wuiltContext,
  isSorting,
  children,
  isLastRow,
  updateAndMutate,
  updateUi,
  mutateApi,
  isSingleRow,
  isSingleColumn,
  onUploadImage,
}) => {
  const isNotAvailable = checkAvailability(wuiltContext);
  const addRow = (location?: "before" | "after") => {
    if (disableAdding) return;
    updateAndMutate((prev) => {
      return addRowMutation(prev, rowIndex, location);
    });
  };

  const deleteRow = () => {
    if (isSingleRow) return;
    updateAndMutate((prev) => {
      return deleteRowMutation(prev, rowIndex, wuiltContext);
    });
  };

  const duplicateRow = () => {
    if (disableAdding) return;
    updateAndMutate((prev) => {
      return duplicateRowMutation(prev, rowIndex, wuiltContext);
    });
  };

  return (
    <RowContainer
      isSorting={!!isSorting}
      data-test="RowEdit"
      id={row?.id}
      isNotAvailable={isNotAvailable}
    >
      <ElementsViewer.Row row={row} isLastRow={isLastRow}>
        {children}
      </ElementsViewer.Row>
      <RowMenu
        row={row}
        rowIndex={rowIndex}
        updateUi={updateUi}
        onClosePopup={() => mutateApi()}
        duplicateRow={duplicateRow}
        deleteRow={deleteRow}
        onUploadImage={onUploadImage}
        disableAdding={disableAdding}
        addRow={addRow}
        disableDeleting={isSingleRow && isSingleColumn}
      />
    </RowContainer>
  );
};

export { RowEdit };

const RowContainer = styled.div<{
  isSorting: boolean;
  isNotAvailable: boolean;
}>`
  position: relative;
  ${StyledRowMenu} {
    display: flex;
    visibility: hidden;
  }

  &:hover {
    .CustomSection_RowView {
      outline: 1px dashed #2e90fa;
      z-index: 1;
    }
    ${StyledRowMenu} {
      z-index: 1;
      display: flex;
      visibility: visible;
    }
  }

  ${({ isSorting }) =>
    isSorting &&
    css`
      background-color: white;
      height: 100px;
      overflow: hidden;
    `}
  ${({ isNotAvailable }) =>
    isNotAvailable &&
    css`
      pointer-events: none;
    `}
`;
