import {
  Box,
  Divider,
  Heading,
  ImageIcon,
  Stack,
  Text,
  PlayCircleIcon,
} from "@wuilt/quilt";
import React, { ReactNode } from "react";
import { FormattedMessage } from "react-intl";
import AppStoreButtonsIcon from "../../../shared/icons/AppStoreButtonsIcon";
import { getDefaultElement } from "../../../shared/utils/getDefault";
import trackEvent from "../../../shared/utils/trackEvent";
import {
  ElementType,
  SectionElement,
  WuiltContext,
  TextObject,
  SocialLinksIcons,
} from "@wuilt/section-preview";
import CounterIcon from "../../../shared/icons/CounterIcon";
import styled from "styled-components";
import { Popup } from "../../../components/Popup";

type ElementSelectView = {
  type: ElementType;
  getValue: (wuiltContext?: WuiltContext) => Promise<{
    newElement: SectionElement;
    newTexts: TextObject;
  }>;
  title: ReactNode;
  view: ReactNode;
};
const ELEMENTS: ElementSelectView[] = [
  {
    type: "Text",
    getValue: () => getDefaultElement("Text", { tag: "p" }),
    title: <FormattedMessage defaultMessage="Text" id="aA8bDw" />,
    view: (
      <Stack justify="center" height="100%">
        <Text align="center" fontWeight="semiBold" color="info">
          <FormattedMessage defaultMessage="Text Content" id="4s8bsp" />
        </Text>
      </Stack>
    ),
  },
  {
    type: "Text",
    getValue: () => getDefaultElement("Text", { tag: "h2" }),
    title: <FormattedMessage defaultMessage="Heading" id="jT6/da" />,
    view: (
      <Stack justify="center" spacing="tight" height="100%" px="4px">
        <Heading color="info" fontWeight="semiBold">
          <FormattedMessage defaultMessage="Heading" id="jT6/da" />
        </Heading>
        <Divider height="6px" borderRadius="20px" />
        <Divider height="6px" borderRadius="20px" width="50%" />
      </Stack>
    ),
  },
  {
    type: "Buttons",
    getValue: () => getDefaultElement("Buttons"),
    title: <FormattedMessage defaultMessage="Buttons" id="E2rh1p" />,
    view: (
      <Stack
        direction="row"
        align="center"
        justify="center"
        spacing="tight"
        height="100%"
        px="4px"
      >
        <Stack
          justify="center"
          border="2px solid"
          borderColor="disabled"
          borderRadius="8px"
          width="50px"
          height="30px"
          p="4px"
        >
          <Divider height="6px" borderRadius="10px" />
        </Stack>
        <Stack
          justify="center"
          border="2px solid"
          borderColor="disabled"
          borderRadius="8px"
          width="50px"
          height="30px"
          bg="info"
          p="4px"
        >
          <Divider height="6px" borderRadius="10px" />
        </Stack>
      </Stack>
    ),
  },
  {
    type: "Image",
    getValue: () => getDefaultElement("Image"),
    title: <FormattedMessage defaultMessage="Image" id="+0zv6g" />,
    view: (
      <Stack justify="center" align="center" height="100%">
        <Stack
          justify="center"
          align="center"
          border="1px solid"
          borderColor="disabled"
          borderRadius="8px"
          width="60px"
          height="60px"
        >
          <ImageIcon size="xxl" color="disabled" />
        </Stack>
      </Stack>
    ),
  },
  {
    type: "AppStoreButtons",
    getValue: () => getDefaultElement("AppStoreButtons"),
    title: <FormattedMessage defaultMessage="App store buttons" id="kP3kdg" />,
    view: (
      <Stack justify="center" align="center" height="100%">
        <AppStoreButtonsIcon />
      </Stack>
    ),
  },
  {
    type: "SocialLinks",
    getValue: () => getDefaultElement("SocialLinks"),
    title: <FormattedMessage defaultMessage="Social Links" id="3bLmoU" />,
    view: (
      <Stack
        justify="center"
        align="center"
        color="info"
        direction="row"
        height="100%"
      >
        <SocialLinksIcons.FacebookFilled />
        <SocialLinksIcons.InstagramFilled />
        <SocialLinksIcons.YoutubeFilled />
      </Stack>
    ),
  },
  {
    type: "Form",
    getValue: (wuiltContext?: WuiltContext) =>
      getDefaultElement("Form", {}, wuiltContext),
    title: <FormattedMessage defaultMessage="Form" id="baRFiF" />,
    view: (
      <Stack p="6px" spacing="tight" height="100%">
        <Divider height="4px" borderRadius="21px" width="60%" />
        <Stack
          justify="center"
          border="1px solid"
          borderColor="disabled"
          borderRadius="2px"
          width="100%"
          height="16px"
        ></Stack>
        <Divider height="4px" borderRadius="21px" width="40%" />
        <Stack
          justify="center"
          border="1px solid"
          borderColor="disabled"
          borderRadius="2px"
          width="100%"
          height="16px"
        ></Stack>
        <Stack
          justify="center"
          border="2px solid"
          borderColor="disabled"
          borderRadius="4px"
          width="64px"
          height="20px"
          bg="info"
          p="6px 10px"
        >
          <Divider height="4px" borderRadius="10px" />
        </Stack>
      </Stack>
    ),
  },
  {
    type: "Video",
    getValue: () => getDefaultElement("Video"),
    title: <FormattedMessage defaultMessage="Video" id="kBJUtE" />,
    view: (
      <Stack justify="center" align="center" height="100%">
        <Stack
          justify="center"
          align="center"
          border="1px solid"
          borderColor="disabled"
          borderRadius="8px"
          width="60px"
          height="60px"
        >
          <PlayCircleIcon color="info" size="lg" />
        </Stack>
      </Stack>
    ),
  },
  {
    type: "Counter",
    getValue: () => getDefaultElement("Counter"),
    title: <FormattedMessage defaultMessage="Counter" id="WOjlOt" />,
    view: (
      <Stack justify="center" align="center" height="100%">
        <CounterIcon />
      </Stack>
    ),
  },
];

interface AddElementPopupProps {
  wuiltContext?: WuiltContext;
  addElement: (newElement: SectionElement, newTexts: TextObject) => void;
  closePopup: () => void;
}

const AddElementPopup: React.FC<AddElementPopupProps> = ({
  wuiltContext,
  addElement,
  closePopup,
}) => {
  return (
    <>
      <Popup.Header>
        <FormattedMessage defaultMessage="Add Element" id="Y/H8Vd" />
      </Popup.Header>
      <PopupBodyWrapper>
        <Popup.Body>
          <Box display="grid" gridTemplateColumns="auto auto">
            {ELEMENTS.map((element, i) => (
              <Box key={i} mb="10px">
                <Box
                  width="130px"
                  height="80px"
                  borderRadius="4px"
                  border="3px solid"
                  borderColor="disabled"
                  onHover={{ borderColor: "primary" }}
                  onClick={async () => {
                    const addedElement = await element?.getValue(wuiltContext);
                    const { newElement, newTexts } = addedElement;
                    addElement(newElement, newTexts);
                    trackEvent("elementAdded", {
                      "Element name": element?.type,
                    });
                    closePopup();
                  }}
                  cursor="pointer"
                  mb="4px"
                >
                  {element.view}
                </Box>
                <Heading fontSize="sm" align="center">
                  {element.title}
                </Heading>
              </Box>
            ))}
          </Box>
        </Popup.Body>
      </PopupBodyWrapper>
    </>
  );
};

export default AddElementPopup;

const PopupBodyWrapper = styled.div`
  & > div {
    &::-webkit-scrollbar {
      width: 6px;
      border-radius: 9px;
    }
    &::-webkit-scrollbar-thumb {
      background: #7f868e77;
      border-radius: 9px;
    }
  }
`;
