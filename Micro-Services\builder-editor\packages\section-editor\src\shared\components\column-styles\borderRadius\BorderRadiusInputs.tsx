import { BorderRadius } from "@wuilt/section-preview";
import AllCorners from "./AllCorners";
import { BorderRadiusTabs } from "./helper";
import PerCorners from "./PerCorners";

interface SelectedBorderRadiusProps {
  activeBorderRadius: string;
  onChange: (v: BorderRadius) => void;
  borderRadius: BorderRadius | undefined;
}

function BorderRadiusInputs({
  activeBorderRadius,
  borderRadius,
  onChange,
}: SelectedBorderRadiusProps) {
  return (
    <div>
      {activeBorderRadius === BorderRadiusTabs.allCorners && (
        <AllCorners borderRadius={borderRadius} onChange={onChange} />
      )}
      {activeBorderRadius === BorderRadiusTabs.perCorner && (
        <PerCorners borderRadius={borderRadius} onChange={onChange} />
      )}
    </div>
  );
}

export default BorderRadiusInputs;
