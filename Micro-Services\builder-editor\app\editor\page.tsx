'use client'

import { useState } from 'react'
import { Box, Flex, useColorModeValue, Spinner, Text } from '@chakra-ui/react'
import { EditorLayout } from './components/Layout/EditorLayout'
import { ClientOnly } from '../../components/ClientOnly'

export default function EditorPage() {
  return (
    <ClientOnly
      fallback={
        <Flex
          height="100vh"
          align="center"
          justify="center"
          bg="#283340"
          direction="column"
          gap={4}
        >
          <Spinner size="xl" color="blue.500" />
          <Text color="white" fontSize="lg">
            Loading Editor...
          </Text>
        </Flex>
      }
    >
      <EditorLayout />
    </ClientOnly>
  )
}
