'use client'

import { useState } from 'react'
import { Box, Flex, useColorModeValue, Spinner, Text } from '@chakra-ui/react'
import { EditorLayout } from './components/Layout/EditorLayout'
import { ClientOnly } from '../../components/ClientOnly'
import { WebsiteManager } from './components/WebsiteManager'
import { useEditorStore } from '@/lib/stores/editorStore'

export default function EditorPage() {
  const [selectedWebsiteId, setSelectedWebsiteId] = useState<string | null>(null)
  const { currentWebsiteId } = useEditorStore()

  // Mock user ID - in production, this would come from authentication
  const userId = 'user_123'

  const handleWebsiteSelected = (websiteId: string) => {
    setSelectedWebsiteId(websiteId)
  }

  return (
    <ClientOnly
      fallback={
        <Flex
          height="100vh"
          align="center"
          justify="center"
          bg="#283340"
          direction="column"
          gap={4}
        >
          <Spinner size="xl" color="blue.500" />
          <Text color="white" fontSize="lg">
            Loading Editor...
          </Text>
        </Flex>
      }
    >
      {/* Show website manager if no website is selected */}
      {!selectedWebsiteId && !currentWebsiteId ? (
        <Box minH="100vh" bg="gray.50">
          <WebsiteManager
            userId={userId}
            onWebsiteSelected={handleWebsiteSelected}
          />
        </Box>
      ) : (
        <EditorLayout />
      )}
    </ClientOnly>
  )
}
