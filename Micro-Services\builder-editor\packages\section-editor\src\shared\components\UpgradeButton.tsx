import React from "react";
import { But<PERSON>, Stack } from "@wuilt/quilt";
import styled from "styled-components";

const UpgradeButton = ({ siteId, isExpired, content }) => {
  const navigateOutsideIframe = () => {
    // Check if the code is running within an iframe
    if (window.parent === window) return;
    window.parent.location.href = `/upgrade/${siteId}/${
      isExpired === true ? "expired" : "trial"
    }`;
  };
  return (
    <Stack align="center">
      <StyledButton
        onClick={() => navigateOutsideIframe()}
        to={
          window.parent === window
            ? `/upgrade/${siteId}/${isExpired === true ? "expired" : "trial"}`
            : ""
        }
        compact
        size="small"
        plain
      >
        {content}
      </StyledButton>
    </Stack>
  );
};

export default UpgradeButton;

/* styles */
const StyledButton = styled(Button)`
  background: linear-gradient(64deg, #ff2c77 0%, #6723a5 57.73%) !important;
  -webkit-text-fill-color: transparent !important;
  -webkit-background-clip: text !important;
  div {
    font-size: 14px;
    font-weight: 600;
  }
`;
