import React from "react";
import { useSortable } from "@dnd-kit/sortable";

import { DragHandleIcon } from "@wuilt/react-icons";
import { ColorProps, IconButton, IconButtonProps } from "@chakra-ui/react";

export interface IconProps {
  width?: string;
  height?: string;
  color?: ColorProps["color"];
  customColor?: string;
  className?: string;
  children?: React.ReactNode;
  viewBox?: string;
  dataTest?: any;
  ariaHidden?: boolean;
  reverseOnRtl?: boolean;
  ariaLabel?: string;
  title?: React.ReactNode;
  svgString?: string | null;
  rotate?: number;
}

export interface DragHandleProps extends IconProps {
  DragIcon?: React.ReactNode;
  id?: string;
  buttonIconProps?: IconButtonProps;
  hide?: boolean;
}

export const DragHandle = ({
  id,
  hide = false,
  buttonIconProps,
  DragIcon = <DragHandleIcon color="gray.500" size="16px" />,
  ...iconProps
}: DragHandleProps) => {
  const { attributes, listeners, isDragging } = useSortable({ id: id as any });
  const draggingProps = !id ? {} : { ...attributes, ...listeners };
  const DragIconComponent = React.cloneElement(
    DragIcon as React.ReactElement,
    iconProps
  );

  if (hide) return null;
  return (
    <div {...draggingProps}>
      <IconButton
        size="16px"
        variant="linkGray"
        cursor={isDragging ? "grabbing" : "grab"}
        dataTest="button-drag-drop"
        {...buttonIconProps}
      >
        {DragIconComponent}
      </IconButton>
    </div>
  );
};
