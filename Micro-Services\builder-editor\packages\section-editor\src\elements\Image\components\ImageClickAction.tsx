import { Box, Checkbox, <PERSON>Label, Stack } from "@chakra-ui/react";
import { ImageActionType, ImageAction } from "@wuilt/section-preview";
import React from "react";
import { FormattedMessage, injectIntl, IntlShape } from "react-intl";
import InputField from "../../../components/InputField";
import {
  ExpandIcon,
  ExternalLinkIcon,
  NetworkDocumentIcon,
} from "@wuilt/react-icons";
import Select from "../../../components/Select";

const OPTIONS = [
  {
    label: <FormattedMessage defaultMessage="Do nothing" id="EQvhPD" />,
    value: ImageActionType.Do_Nothing,
  },
  {
    label: <FormattedMessage defaultMessage="Scale up image" id="wXNRHo" />,
    value: ImageActionType.Scale_Up,
    icon: <ExpandIcon size="16px" />,
  },
  {
    label: <FormattedMessage defaultMessage="Open Page" id="aFuEjt" />,
    value: ImageActionType.Internal_Link,
    icon: <NetworkDocumentIcon size="16px" />,
  },
  {
    label: <FormattedMessage defaultMessage="External link" id="OphrTn" />,
    value: ImageActionType.External_Link,
    icon: <ExternalLinkIcon size="16px" />,
  },
];

interface ImageClickActionProps {
  intl: IntlShape;
  action: ImageAction | undefined;
  pages?: any[];
  updateAction: (v: ImageAction) => void;
}

const ImageClickAction: React.FC<ImageClickActionProps> = ({
  intl,
  action,
  pages,
  updateAction,
}) => {
  return (
    <Stack gap="16px">
      <Box>
        <FormLabel mb="4px" fontSize="16px" fontWeight="400">
          <FormattedMessage defaultMessage="Click action" id="cTs+Ce" />
        </FormLabel>
        <Select
          options={OPTIONS}
          isSearchable={false}
          value={createOption(action?.type)}
          formatOptionLabel={formatOptionLabel}
          onChange={(o) => {
            updateAction({
              ...action,
              type: o?.value,
              value: "",
              newTab: false,
            });
          }}
        />
      </Box>

      {action?.type === ImageActionType.Internal_Link && (
        <Select
          placeholder={
            <FormattedMessage defaultMessage="Select page" id="rvz2nM" />
          }
          options={pages?.map?.((i) => ({ value: i?.id, label: i?.name }))}
          value={getPageOption(action?.value, pages)}
          onChange={(o: any) => {
            updateAction({ ...action, value: o?.value ?? "" });
          }}
        />
      )}

      {action?.type === ImageActionType.External_Link && (
        <Stack gap="16px">
          <InputField
            placeholder={intl.formatMessage({
              defaultMessage: "ex. https://wuilt.com",
              id: "bBw2rk",
            })}
            value={action?.value}
            onChange={(e) => {
              updateAction({ ...action, value: e.target.value ?? "" });
            }}
          />
          <Box onClick={(e) => e.stopPropagation()}>
            <Checkbox
              mb="0"
              colorScheme="teal"
              isChecked={!!action?.newTab}
              onChange={(e) =>
                updateAction({ ...action, newTab: e.target.checked })
              }
            >
              <FormattedMessage
                defaultMessage="Open link in a new tab"
                id="Ue3cf2"
              />
            </Checkbox>
          </Box>
        </Stack>
      )}
    </Stack>
  );
};

export default injectIntl(ImageClickAction);

/**
 * Helpers
 */

function createOption(type: ImageActionType | undefined) {
  if (!type) return OPTIONS[0];
  return OPTIONS.find((i) => i.value === type);
}

function formatOptionLabel(option: (typeof OPTIONS)[0] | null) {
  return (
    <Stack direction="row" align="center" gap="4px">
      <span> {option?.icon} </span>
      <span> {option?.label} </span>
    </Stack>
  );
}

function getPageOption(id: string | undefined, pages: any[] = []) {
  const page = pages?.find?.((i) => i?.id === id);
  if (!page?.id && !pages?.[0]?.id) return null;
  return {
    value: page?.id || pages?.[0]?.id,
    label: page?.name || pages?.[0]?.name,
  };
}
