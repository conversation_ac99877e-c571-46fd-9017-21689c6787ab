import { <PERSON>, <PERSON>ack, <PERSON><PERSON>abel, FormControl, Switch } from "@chakra-ui/react";
import {
  AppStoreButtonSettings,
  AppStoreButtonActionType,
} from "@wuilt/section-preview";
import React from "react";
import { FormattedMessage, injectIntl, IntlShape } from "react-intl";
import InputField from "../../../components/InputField";
import Select from "../../../components/Select";

interface AppStoreButtonDetailsProps {
  button?: AppStoreButtonSettings;
  intl: IntlShape;
  updateAppStoreButtonAction: (arg: AppStoreButtonSettings) => void;
}

const APPSTOREBUTTONSOPTIONS = [
  {
    label: <FormattedMessage defaultMessage="Apple App Store" id="R0hki2" />,
    value: AppStoreButtonActionType.Apple,
  },
  {
    label: <FormattedMessage defaultMessage="Google Play Store" id="xUJMg2" />,
    value: AppStoreButtonActionType.Google,
  },
  {
    label: (
      <FormattedMessage defaultMessage="Samsung Galaxy Store" id="FYlJzX" />
    ),
    value: AppStoreButtonActionType.Samsung,
  },
  {
    label: <FormattedMessage defaultMessage="Huawei App Gallery" id="4YxAWu" />,
    value: AppStoreButtonActionType.Huawei,
  },
];
const AppStoreButtonDetails: React.FC<AppStoreButtonDetailsProps> = ({
  button,
  intl,
  updateAppStoreButtonAction,
}) => {
  return (
    <Stack gap="16px">
      <Box>
        <FormLabel mb="4px" fontSize="16px" fontWeight="400">
          <FormattedMessage defaultMessage="Button type" id="wfhgra" />
        </FormLabel>
        <Select
          options={APPSTOREBUTTONSOPTIONS}
          isSearchable={false}
          isClearable={false}
          value={createOption(button?.action?.type)}
          onChange={(option) => {
            updateAppStoreButtonAction({
              ...button,
              action: {
                ...button?.action,
                type: option?.value,
              },
            });
          }}
        />
      </Box>
      <Box>
        <FormLabel mb="4px" fontSize="16px" fontWeight="400">
          <FormattedMessage defaultMessage="Link to app page" id="8HnwyF" />
        </FormLabel>
        <InputField
          placeholder={intl.formatMessage({
            defaultMessage: "https://",
            id: "Vthr0a",
          })}
          value={button?.action?.value}
          onChange={(e) => {
            updateAppStoreButtonAction({
              ...button,
              action: {
                ...button?.action,
                value: e.target.value,
              },
            });
          }}
        />
      </Box>
      <Stack
        onClick={(e) => e.stopPropagation()}
        justifyContent={"space-between"}
        direction={"row"}
        alignItems={"center"}
      >
        <FormattedMessage defaultMessage="Open in a new tab" id="z4qMx5" />
        <Switch
          onChange={(e) =>
            updateAppStoreButtonAction({
              ...button,
              action: {
                ...button?.action,
                newTab: e.target.checked,
              },
            })
          }
          checked={!!button?.action?.newTab}
          id="email-alerts"
        />
      </Stack>
    </Stack>
  );
};

export default injectIntl(AppStoreButtonDetails);

function createOption(type: AppStoreButtonActionType | undefined) {
  if (!type) return APPSTOREBUTTONSOPTIONS[0];
  return APPSTOREBUTTONSOPTIONS.find((option) => option.value === type);
}
