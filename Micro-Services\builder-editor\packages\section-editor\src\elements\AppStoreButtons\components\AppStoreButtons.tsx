import React, { Dispatch, SetStateAction } from "react";
import { FormattedMessage } from "react-intl";
import { nanoid } from "nanoid";
import { AppStoreButtonsSettingsViews } from "./AppStoreButtonsSettings";
import {
  AppStoreButtonSettings,
  AppStoreButtonActionType,
} from "@wuilt/section-preview";
import { Divider, Stack, Text, Button } from "@chakra-ui/react";
import { AddIcon, ChevronRightIcon } from "@wuilt/react-icons";
import { useTheme } from "styled-components";
import SortableAppStoreButtons from "./SortableAppStoreButtons";

const ButtonsTypes: any = {
  Apple: <FormattedMessage defaultMessage="Apple App Store" id="R0hki2" />,
  Google: <FormattedMessage defaultMessage="Google Play Store" id="xUJMg2" />,
  Samsung: (
    <FormattedMessage defaultMessage="Samsung Galaxy Store" id="FYlJzX" />
  ),
  <PERSON>awei: <FormattedMessage defaultMessage="Huawei App Gallery" id="4YxAWu" />,
};

interface AppStoreButtonsProps {
  appStoreButtons?: AppStoreButtonSettings[];
  setView: Dispatch<SetStateAction<AppStoreButtonsSettingsViews>>;
  setActiveButton: Dispatch<SetStateAction<AppStoreButtonSettings>>;
  addAppStoreButton: (arg: AppStoreButtonSettings) => void;
  sortAppStoreButtonsUi: (arg: AppStoreButtonSettings[]) => void;
  deleteAppStoreButton: (arg: string) => void;
}
const AppStoreButtons: React.FC<AppStoreButtonsProps> = ({
  appStoreButtons,
  setActiveButton,
  setView,
  addAppStoreButton,
  sortAppStoreButtonsUi,
  deleteAppStoreButton,
}) => {
  function setActiveButtonHandler(button: AppStoreButtonSettings) {
    setView(AppStoreButtonsSettingsViews.Settings);
    setActiveButton(button);
  }
  return (
    <Stack gap="16px">
      <SortableAppStoreButtons
        appStoreButtons={appStoreButtons}
        sortAppStoreButtonsUi={sortAppStoreButtonsUi}
        deleteAppStoreButton={deleteAppStoreButton}
        setActiveButtonHandler={setActiveButtonHandler}
      />
      <Button
        variant="plain"
        color="primary.600"
        size="sm"
        justifyContent={"start"}
        paddingInline={0}
        fontSize={"14px"}
        leftIcon={<AddIcon size="20px" />}
        onClick={() =>
          addAppStoreButton({
            id: `${nanoid()}`,
            action: {
              type: AppStoreButtonActionType.Apple,
            },
          })
        }
      >
        <FormattedMessage defaultMessage="Add App Store button" id="+tg44F" />
      </Button>
    </Stack>
  );
};

export default AppStoreButtons;
