import {
  SectionElement,
  TextObject,
  TextSettings,
} from "@wuilt/section-preview";
import { nanoid } from "nanoid";

export function duplicateTextMutation(
  element: SectionElement,
  textObject: TextObject
) {
  const type = element?.type;
  const textSettings = element?.settings as TextSettings;
  const newTextId = `Content:${nanoid()}`;
  const newElementSettings = {
    id: `${type}:${nanoid()}`,
    type,
    settings: { ...textSettings, textId: newTextId },
  };
  const newTextIds = {
    ...textObject,
    [newTextId]: textObject[textSettings?.textId!],
  };
  return {
    newElementSettings,
    newTextIds,
  };
}
