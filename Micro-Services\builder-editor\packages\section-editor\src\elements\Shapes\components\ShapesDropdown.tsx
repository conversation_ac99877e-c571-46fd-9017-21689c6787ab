import { Box } from "@chakra-ui/react";
import shapesMapper from "../shapesMapper";
import { CSSProperties, useState } from "react";
import styled from "styled-components";

export default function ShapesDropdown({
  onSelect,
  val,
}: {
  onSelect: (val: string) => void;
  val: string;
}) {
  const [toggle, setToggle] = useState(false);
  const style: CSSProperties = {
    fill: "#333",
  };
  const selectShape = (item: string) => {
    onSelect(item);
    setToggle(false);
  };
  const shapes = shapesMapper(style);
  const shapesList = [];
  for (const item in shapes) {
    shapesList.push(
      <Box onClick={() => selectShape(item)} className="shape">
        {shapes[item]}
      </Box>
    );
  }
  return (
    <div className="shapes-dropdown">
      <Box className="selected-value" onClick={() => setToggle(true)}>
        <ShapeIcon>{shapes[val]}</ShapeIcon>
      </Box>
      {toggle && <ShapeSelection>{shapesList}</ShapeSelection>}
    </div>
  );
}

const ShapeIcon = styled.div`
  background: rgb(242, 242, 242);
  display: flex;
  justify-content: center;
  padding: 25px 0;
  cursor: pointer;

  &:hover {
    background: rgb(218, 216, 216);
  }

  svg {
    width: 47px;
    height: 47px;
  }
`;

const ShapeSelection = styled.div`
  display: flex;
  position: absolute;
  top: 60px;
  left: 10px;
  border: 1px solid #ccc;
  border-radius: 12px;
  flex-wrap: wrap;
  width: calc(100% - 20px);
  height: 367px;
  overflow: auto;
  -webkit-box-pack: justify;
  justify-content: center;
  background: rgb(255, 255, 255);
  row-gap: 15px;
  padding: 20px;
  .shape {
    width: 30%;
    display: flex;
    justify-content: center;
    padding: 20px 0;
    svg {
      width: 47px;
      height: 47px;
    }
    &:hover {
      background: rgb(218, 216, 216);
      cursor: pointer;
    }
  }
`;
