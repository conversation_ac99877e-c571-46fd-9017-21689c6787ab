import AppStoreButtonsEdit from "./AppStoreButtons/AppStoreButtonsEdit";
import { ButtonEdit } from "./Button";
import { ShapesEdit } from "./Shapes";
import { ButtonsEdit } from "./Buttons";
import { ColumnEdit } from "./Column";
import { ElementEdit } from "./Element";
import FormEdit from "./Form/FormEdit";
import ImageEdit from "./Image/ImageEdit";
import { RowEdit } from "./Row";
import { SectionEdit } from "./Section";
import { TextEdit } from "./Text";
import VideoEdit from "./Video/VideoEdit";
import SocialLinksEdit from "./SocialLinks/SocialLinksEdit";
import CounterEdit from "./Counter/CounterEdit";

import CounterSettings from "./Counter/components/CounterSetting";
import ButtonSettings from "./Button/components/ButtonSettings";
import VideoSettings from "./Video/components/VideoSettings";
import FormSettings from "./Form/components/FormSettings";
import ImageSettings from "./Image/components/ImageSettings";
import SocialLinksSettings from "./SocialLinks/components/SocialLinksSettings";
import AppStoreButtonSettings from "./AppStoreButtons/components/AppStoreButtonsSettings";
import ShapesSettings from "./Shapes/components/ShapesSettings";

export const ElementsEditor = {
  Section: SectionEdit,
  Column: ColumnEdit,
  Row: RowEdit,
  Element: ElementEdit as any,
  Text: TextEdit as any,
  Buttons: ButtonsEdit,
  Button: ButtonEdit,
  Image: ImageEdit as any,
  AppStoreButtons: AppStoreButtonsEdit,
  Form: FormEdit,
  Video: VideoEdit,
  SocialLinks: SocialLinksEdit,
  Counter: CounterEdit,
  Shapes: ShapesEdit,
};

export const ElementsSettings = {
  Button: ButtonSettings,
  Video: VideoSettings,
  Form: FormSettings,
  Image: ImageSettings,
  SocialLinks: SocialLinksSettings,
  AppStoreButtons: AppStoreButtonSettings,
  Counter: CounterSettings,
  Shapes: ShapesSettings,
};
