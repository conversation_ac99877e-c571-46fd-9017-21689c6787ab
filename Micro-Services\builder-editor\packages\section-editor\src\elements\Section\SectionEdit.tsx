import React from "react";
import { CustomSection, ElementsViewer } from "@wuilt/section-preview";

export interface SectionEditProps {
  settings: CustomSection["settings"];
  children: React.ReactNode;
}

const SectionEdit: React.FC<SectionEditProps> = ({ settings, children }) => {
  return (
    <ElementsViewer.Section settings={settings}>
      {children}
    </ElementsViewer.Section>
  );
};

export { SectionEdit };
