'use client'

import { useState } from 'react'
import {
  Box,
  VStack,
  IconButton,
  Tooltip,
  useColorModeValue,
  Flex,
  Text,
  Button,
  HStack,
  Divider,
  SimpleGrid,
  Image,
  Badge
} from '@chakra-ui/react'
import {
  SettingsIcon,
  ChatIcon,
  QuestionIcon,
  BellIcon,
  DownloadIcon,
  EditIcon
} from '@chakra-ui/icons'
import { motion, AnimatePresence } from 'framer-motion'
import { useTranslation } from '@/lib/contexts/LanguageContext'
import { sectionDesigns } from '@/lib/sections/sectionDesigns'

interface OldBuilderSidebarProps {
  onAddSection?: (sectionId: string, category: string) => void
}

// Sidebar button component matching Old Builder
function SidebarButton({ 
  icon, 
  label, 
  isActive = false, 
  onClick 
}: { 
  icon: React.ReactNode
  label: string
  isActive?: boolean
  onClick?: () => void 
}) {
  return (
    <Tooltip label={label} placement="right">
      <IconButton
        aria-label={label}
        icon={icon}
        size="md"
        variant="ghost"
        bg={isActive ? '#e6f7ff' : 'transparent'}
        color={isActive ? '#0E9384' : '#667085'}
        borderRadius="8px"
        _hover={{
          bg: '#f0f0f0',
          color: '#333'
        }}
        onClick={onClick}
      />
    </Tooltip>
  )
}

// Expandable panel content
function StylesPanel({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  const { t } = useTranslation()
  
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ x: -300, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          exit={{ x: -300, opacity: 0 }}
          transition={{ duration: 0.2, ease: 'easeOut' }}
          style={{
            position: 'fixed',
            top: '50px',
            left: '55px',
            bottom: '0',
            width: '280px',
            background: 'white',
            borderRight: '1px solid #dfe3e8',
            boxShadow: '2px 0 8px rgba(0,0,0,0.1)',
            zIndex: 999,
          }}
        >
          <VStack spacing={0} align="stretch" h="100%">
            {/* Header */}
            <Box
              p="16px"
              borderBottom="1px solid #dfe3e8"
              bg="#f8f9fa"
            >
              <HStack justify="space-between">
                <Text fontWeight="600" fontSize="sm" color="#343a40">
                  Site Styles
                </Text>
                <IconButton
                  aria-label="Close"
                  icon={<Box>×</Box>}
                  size="sm"
                  variant="ghost"
                  onClick={onClose}
                />
              </HStack>
              <Text fontSize="xs" color="#6c757d" mt="4px">
                Manage every style of your website here
              </Text>
            </Box>

            {/* Content */}
            <Box flex="1" p="16px" overflowY="auto">
              {/* Current Theme */}
              <Box mb="24px">
                <Text fontWeight="600" fontSize="sm" color="#343a40" mb="12px">
                  CURRENT THEME
                </Text>
                
                <Box
                  p="12px"
                  border="1px solid #dfe3e8"
                  borderRadius="8px"
                  bg="#f8f9fa"
                >
                  <Text fontWeight="600" fontSize="sm" color="#343a40" mb="4px">
                    Heading
                  </Text>
                  <Text fontSize="xs" color="#6c757d" mb="12px">
                    This is your paragraph
                  </Text>
                  <Text fontSize="xs" color="#6c757d">
                    General Formatting
                  </Text>
                  
                  {/* Color swatches */}
                  <HStack spacing="8px" mt="12px">
                    <Box w="24px" h="24px" bg="#8B4513" borderRadius="4px" />
                    <Box w="24px" h="24px" bg="#000000" borderRadius="4px" />
                    <Box w="24px" h="24px" bg="#DAA520" borderRadius="4px" />
                  </HStack>
                  
                  <Button
                    size="sm"
                    bg="#8B4513"
                    color="white"
                    borderRadius="4px"
                    mt="12px"
                    fontSize="xs"
                    fontWeight="600"
                    _hover={{ bg: '#7A3F12' }}
                  >
                    Primary
                  </Button>
                </Box>
              </Box>

              {/* Customize Site Theme */}
              <Box mb="24px">
                <Text fontWeight="600" fontSize="sm" color="#343a40" mb="12px">
                  CUSTOMIZE SITE THEME
                </Text>
                
                <VStack spacing="8px" align="stretch">
                  <Button
                    variant="outline"
                    size="sm"
                    justifyContent="flex-start"
                    borderColor="#dfe3e8"
                    color="#343a40"
                    _hover={{ bg: '#f8f9fa' }}
                  >
                    <HStack>
                      <Box w="16px" h="16px" bg="#1f9aeb" borderRadius="2px" />
                      <Text fontSize="sm">Colors</Text>
                    </HStack>
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    justifyContent="flex-start"
                    borderColor="#dfe3e8"
                    color="#343a40"
                    _hover={{ bg: '#f8f9fa' }}
                  >
                    <HStack>
                      <Text fontSize="sm" fontWeight="600">T</Text>
                      <Text fontSize="sm">Typography</Text>
                    </HStack>
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    justifyContent="flex-start"
                    borderColor="#dfe3e8"
                    color="#343a40"
                    _hover={{ bg: '#f8f9fa' }}
                  >
                    <HStack>
                      <Box w="16px" h="16px" border="2px solid #343a40" borderRadius="2px" />
                      <Text fontSize="sm">Buttons</Text>
                    </HStack>
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    justifyContent="flex-start"
                    borderColor="#dfe3e8"
                    color="#343a40"
                    _hover={{ bg: '#f8f9fa' }}
                  >
                    <HStack>
                      <Box w="16px" h="16px" bg="#f0f0f0" borderRadius="2px" />
                      <Text fontSize="sm">Form fields</Text>
                    </HStack>
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    justifyContent="flex-start"
                    borderColor="#dfe3e8"
                    color="#343a40"
                    _hover={{ bg: '#f8f9fa' }}
                  >
                    <HStack>
                      <Box>🎬</Box>
                      <Text fontSize="sm">Animations</Text>
                    </HStack>
                  </Button>
                </VStack>
              </Box>

              {/* Reset to default */}
              <Button
                variant="link"
                size="sm"
                color="#6c757d"
                fontSize="xs"
                textDecoration="underline"
              >
                🔄 Reset to default
              </Button>
            </Box>
          </VStack>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export function OldBuilderSidebar({ onAddSection }: OldBuilderSidebarProps) {
  const [activePanel, setActivePanel] = useState<string | null>(null)
  
  const handlePanelToggle = (panelName: string) => {
    setActivePanel(activePanel === panelName ? null : panelName)
  }

  return (
    <>
      {/* Fixed 55px Sidebar */}
      <Box
        w="55px"
        bg="white"
        borderRight="1px solid #dfe3e8"
        boxShadow="xl"
        p="6px"
        display="flex"
        flexDirection="column"
        position="relative"
        zIndex={10}
        h="100%"
      >
        {/* Top tools */}
        <VStack spacing="4px" flex="1">
          <SidebarButton
            icon={<EditIcon />}
            label="Styles"
            isActive={activePanel === 'styles'}
            onClick={() => handlePanelToggle('styles')}
          />
          <SidebarButton
            icon={<Box>📁</Box>}
            label="Media Center"
            isActive={activePanel === 'media'}
            onClick={() => handlePanelToggle('media')}
          />
        </VStack>

        {/* Bottom tools */}
        <VStack spacing="4px" flex="1" justify="flex-end">
          <SidebarButton
            icon={<DownloadIcon />}
            label="Backups"
            isActive={activePanel === 'backups'}
            onClick={() => handlePanelToggle('backups')}
          />
          <SidebarButton
            icon={<SettingsIcon />}
            label="Settings"
            isActive={activePanel === 'settings'}
            onClick={() => handlePanelToggle('settings')}
          />
          <SidebarButton
            icon={<BellIcon />}
            label="Notifications"
            isActive={activePanel === 'notifications'}
            onClick={() => handlePanelToggle('notifications')}
          />
          <SidebarButton
            icon={<QuestionIcon />}
            label="Help & Support"
            isActive={activePanel === 'help'}
            onClick={() => handlePanelToggle('help')}
          />
        </VStack>
      </Box>

      {/* Expandable Panels */}
      <StylesPanel 
        isOpen={activePanel === 'styles'} 
        onClose={() => setActivePanel(null)} 
      />
      
      {/* Backdrop for panels */}
      {activePanel && (
        <Box
          position="fixed"
          top="50px"
          left="0"
          right="0"
          bottom="0"
          bg="rgba(0, 0, 0, 0.3)"
          zIndex={998}
          onClick={() => setActivePanel(null)}
        />
      )}
    </>
  )
}
