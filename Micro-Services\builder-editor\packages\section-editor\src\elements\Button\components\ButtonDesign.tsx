import {
  Text,
  <PERSON>ton,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Stack<PERSON>rops,
} from "@chakra-ui/react";
import {
  ButtonDesignType,
  ButtonDesign,
  TextObject,
  WuiltContext,
} from "@wuilt/section-preview";
import React from "react";
import { FormattedMessage } from "react-intl";
import InputField from "../../../components/InputField";
import Select from "../../../components/Select";

type ButtonDesignTypeOption = {
  label: React.ReactNode;
  value: ButtonDesignType;
};

const BUTTON_DESIGN_TYPE_OPTIONS: ButtonDesignTypeOption[] = [
  {
    label: <FormattedMessage defaultMessage="Primary" id="t2Wr8I" />,
    value: ButtonDesignType.Primary,
  },
  {
    label: <FormattedMessage defaultMessage="Secondary" id="6FQuCT" />,
    value: ButtonDesignType.Secondary,
  },
  {
    label: <FormattedMessage defaultMessage="Tertiary" id="vgAm5Z" />,
    value: ButtonDesignType.Link,
  },
];

interface ButtonDesignSettingsProps {
  text?: TextObject;
  design: ButtonDesign | undefined;
  updateText?: (newTexts?: TextObject) => void;
  updateDesign: (v: ButtonDesign) => void;
  buttonTextId?: string;
  wuiltContext?: WuiltContext;
  enableContentEdit?: boolean;
  wrapperStackProps?: StackProps;
}

const ButtonDesignSettings: React.FC<ButtonDesignSettingsProps> = ({
  text,
  design,
  updateText,
  updateDesign,
  buttonTextId,
  enableContentEdit,
  wrapperStackProps = {},
}) => {
  return (
    <VStack gap="16px" {...wrapperStackProps}>
      {enableContentEdit && (
        <>
          <InputField
            label={
              <FormattedMessage defaultMessage="Button text" id="BlGveL" />
            }
            value={text?.[buttonTextId!]}
            onChange={(e) =>
              updateText?.({
                [buttonTextId!]: e.target.value,
              })
            }
          />
          <Divider />
        </>
      )}
      <VStack gap="6px" width="full" alignItems="start">
        <Text
          width="full"
          variant="textSm"
          fontWeight="medium"
          color="gray.700"
        >
          <FormattedMessage defaultMessage="Button style" id="kYSEdK" />
        </Text>
        <Select
          value={createOption(design?.type)}
          options={BUTTON_DESIGN_TYPE_OPTIONS}
          formatOptionLabel={formatOptionLabel}
          onChange={(buttonTypeOption) => {
            updateDesign({ ...design, type: buttonTypeOption?.value });
          }}
        />
      </VStack>
    </VStack>
  );
};

/**
 * Chakra react select helpers
 */

function createOption(type: ButtonDesignType | undefined) {
  if (!type) return BUTTON_DESIGN_TYPE_OPTIONS[0];
  return BUTTON_DESIGN_TYPE_OPTIONS.find((option) => option.value === type);
}

function formatOptionLabel(option: ButtonDesignTypeOption) {
  return (
    <HStack gap="8px">
      <Button
        padding="8px 12px !important"
        className={`button-view-${
          option.value === ButtonDesignType.Link
            ? "tertiary"
            : option.value.toLocaleLowerCase()
        }`}
      >
        <FormattedMessage defaultMessage="Button" id="KP63fg" />
      </Button>
      <Divider orientation="vertical" height="36px" color="gray.200" />
      <Text variant="textMd" fontWeight="medium" color="gray.800">
        {option?.label}
      </Text>
    </HStack>
  );
}

export default ButtonDesignSettings;
