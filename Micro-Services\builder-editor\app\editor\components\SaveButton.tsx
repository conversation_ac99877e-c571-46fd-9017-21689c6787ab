'use client'

import React from 'react'
import { Button, useToast, HStack, Text } from '@chakra-ui/react'
import { CheckIcon, WarningIcon } from '@chakra-ui/icons'
import { useEditorStore } from '@/lib/stores/editorStore'

export function SaveButton() {
  const { saveToSupabase, isSaving, currentWebsiteId } = useEditorStore()
  const toast = useToast()

  const handleSave = async () => {
    if (!currentWebsiteId) {
      toast({
        title: 'No website selected',
        description: 'Please select a website first',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      })
      return
    }

    const result = await saveToSupabase()
    
    if (result.success) {
      toast({
        title: 'Saved successfully',
        description: 'Your website has been saved to the cloud',
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
    } else {
      toast({
        title: 'Save failed',
        description: result.error || 'Failed to save website',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    }
  }

  return (
    <Button
      leftIcon={isSaving ? undefined : <CheckIcon />}
      colorScheme="green"
      size="sm"
      onClick={handleSave}
      isLoading={isSaving}
      loadingText="Saving..."
      isDisabled={!currentWebsiteId}
    >
      {currentWebsiteId ? 'Save Website' : 'No Website Selected'}
    </Button>
  )
}

export function AutoSaveIndicator() {
  const { isSaving, currentWebsiteId } = useEditorStore()

  if (!currentWebsiteId) {
    return (
      <HStack spacing="8px" color="orange.500">
        <WarningIcon />
        <Text fontSize="sm">No website selected</Text>
      </HStack>
    )
  }

  if (isSaving) {
    return (
      <HStack spacing="8px" color="blue.500">
        <Text fontSize="sm">Saving...</Text>
      </HStack>
    )
  }

  return (
    <HStack spacing="8px" color="green.500">
      <CheckIcon />
      <Text fontSize="sm">Saved</Text>
    </HStack>
  )
}
