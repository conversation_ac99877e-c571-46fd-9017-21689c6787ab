import {
  BorderRadiusAllCornersIcon,
  BorderRadiusPerCornerIcon,
  utils,
} from "@wuilt/quilt";
import React from "react";
import { FormattedMessage } from "react-intl";
import styled, { css } from "styled-components";
import { BorderRadiusTabs } from "./helper";

interface SelectedBorderRadiusProps {
  setActiveBorderRadius: React.Dispatch<React.SetStateAction<BorderRadiusTabs>>;
  activeBorderRadius: string;
}
const BORDER_RADIUS_TABS = [
  {
    icon: <BorderRadiusAllCornersIcon size="lg" />,
    type: BorderRadiusTabs.allCorners,
    content: <FormattedMessage defaultMessage="All corners" id="M1KyH9" />,
  },
  {
    icon: <BorderRadiusPerCornerIcon size="lg" />,
    type: BorderRadiusTabs.perCorner,
    content: <FormattedMessage defaultMessage="Per corner" id="Km2O/u" />,
  },
];

function SelectedBorderRadius({
  setActiveBorderRadius,
  activeBorderRadius,
}: SelectedBorderRadiusProps) {
  return (
    <div>
      <StyledRowLocal>
        {BORDER_RADIUS_TABS.map(({ type, content, icon }) => {
          return (
            <StyledIconWrapper
              onClick={() => {
                setActiveBorderRadius(type);
              }}
              key={type}
              isSelected={activeBorderRadius === type}
            >
              {icon}
              {content}
            </StyledIconWrapper>
          );
        })}
      </StyledRowLocal>
    </div>
  );
}

export default SelectedBorderRadius;

const StyledRowLocal = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: solid 1px ${utils.color("ink", "lighter")};
  border-radius: 4px;
  height: 34px;
  width: 100%;
`;

const StyledIconWrapper = styled.div<{ isSelected: boolean }>`
  cursor: pointer;
  padding: 8px 0;
  width: calc(100% / 2);
  height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  color: ${utils.color("ink", "light")};
  ${({ isSelected }) =>
    isSelected &&
    css`
      background-color: ${utils.color("product", "normal")};
      color: ${utils.color("white", "normal")};
      fill: ${utils.color("white", "normal")};
    `}
  &:nth-child(2) {
    padding: 8px 0;
    ${({ theme }) =>
      `${
        theme.rtl
          ? "border-right: solid 1px #bac7d5;"
          : "border-left: solid 1px #bac7d5;"
      }`};
    border-top: none;
    border-bottom: none;
  }
`;
