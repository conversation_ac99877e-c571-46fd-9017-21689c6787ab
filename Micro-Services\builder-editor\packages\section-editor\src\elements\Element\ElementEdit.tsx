import React, { useState } from "react";
import AddElementButton from "./components/AddElementButton";
import styled, { css } from "styled-components";
import ElementSettingsMenu, {
  StyledSettingsButtons,
} from "./components/ElementSettingsMenu";
import {
  addElementMutation,
  deleteElementMutation,
  duplicateElementMutation,
  updateElementMutation,
} from "../../shared/mutations";
import {
  ElementType,
  SectionElement,
  UpdateDataFunc,
  WuiltContext,
  Column,
  TextObject,
} from "@wuilt/section-preview";
import { checkAvailability } from "../../shared/utils/checkAvailability";

const ElementsWithoutBackgroundHover: ElementType[] = ["Form"];

export type ElementProps = {
  element: SectionElement;
  wuiltContext?: WuiltContext;
  pages?: any[];
  updateElementUi: (newElement: SectionElement, newTexts?: TextObject) => void;
  mutateElementApi: (newElement: SectionElement) => void;
  onUploadImage?: (cb: (src: string) => void) => void;
  mutateApi?: any;
  text: TextObject;
  updateTextApi?: (newTexts?: TextObject) => void;
  updateTextUi?: (newTexts?: TextObject) => void;
};

interface ElementEditProps {
  element: SectionElement;
  column: Column;
  wuiltContext: WuiltContext;
  Component: (props: ElementProps) => JSX.Element;
  rowIndex: number;
  columnIndex: number;
  elementIndex: number;
  pages: any[];
  isSorting?: boolean;
  updateUi: UpdateDataFunc;
  mutateApi: UpdateDataFunc;
  updateAndMutate: UpdateDataFunc;
  onUploadImage: (cb: (src: string) => void) => void;
  text: TextObject;
  updateTextApi?: (newTexts?: TextObject) => void;
  updateTextUi?: (newTexts?: TextObject) => void;
}

const ElementEdit: React.FC<ElementEditProps> = ({
  element,
  wuiltContext,
  Component,
  rowIndex,
  columnIndex,
  elementIndex,
  pages,
  isSorting,
  updateUi,
  mutateApi,
  updateAndMutate,
  onUploadImage,
  text,
  updateTextApi,
  updateTextUi,
}) => {
  const [openSettingsPopup, setOpenSettingsPopup] = useState(false);
  const isNotAvailable = checkAvailability(wuiltContext);

  const addElement = (newElement: SectionElement, newTexts: TextObject) => {
    const newElementIndex = elementIndex + 1;
    updateAndMutate((prev) => {
      return addElementMutation(
        prev,
        rowIndex,
        columnIndex,
        newElement,
        newElementIndex,
        newTexts
      );
    });
  };

  const duplicateElement = () => {
    updateAndMutate((prev) => {
      return duplicateElementMutation(
        prev,
        rowIndex,
        columnIndex,
        elementIndex,
        false,
        wuiltContext
      );
    });
  };

  const updateElementUi = (
    newElement: SectionElement,
    newTexts?: TextObject
  ) => {
    updateUi((prev) => {
      return updateElementMutation(
        prev,
        rowIndex,
        columnIndex,
        elementIndex,
        newElement,
        newTexts
      );
    });
  };

  const mutateElementApi = (newElement: SectionElement) => {
    mutateApi((prev) => {
      return updateElementMutation(
        prev,
        rowIndex,
        columnIndex,
        elementIndex,
        newElement
      );
    });
  };

  const deleteElement = () => {
    updateAndMutate((prev) => {
      return deleteElementMutation(
        prev,
        rowIndex,
        columnIndex,
        elementIndex,
        wuiltContext
      );
    });
  };

  const handleToggleSetting = () => {
    setOpenSettingsPopup((prev) => !prev);
  };

  return (
    <StyledElementEdit
      data-test="ElementEdit"
      className="dnd-item"
      isSorting={!!isSorting}
      isNotAvailable={isNotAvailable}
    >
      <StyledHoverBox
        noBackgroundHoverEffect={ElementsWithoutBackgroundHover.includes(
          element.type!
        )}
      >
        <ElementSettingsMenu
          element={element}
          deleteElement={deleteElement}
          duplicateElement={duplicateElement}
          updateElementUi={updateElementUi}
          mutateElementApi={mutateElementApi}
          onClosePopup={() => mutateApi()}
          onUploadImage={onUploadImage}
          pages={pages}
          appDirection={wuiltContext?.props?.appDirection!}
          text={text}
          updateTextApi={updateTextApi}
          updateTextUi={updateTextUi}
          openSettingsPopup={openSettingsPopup}
          setOpenSettingsPopup={setOpenSettingsPopup}
          wuiltContext={wuiltContext}
        />
        <StyledToggleSettings onMouseDown={handleToggleSetting} />
        <Component
          element={element}
          pages={pages}
          wuiltContext={wuiltContext}
          updateElementUi={updateElementUi}
          mutateElementApi={mutateElementApi}
          onUploadImage={onUploadImage}
          mutateApi={mutateApi}
          text={text}
          updateTextApi={updateTextApi}
          updateTextUi={updateTextUi}
        />
      </StyledHoverBox>
      <AddElementButton addElement={addElement} wuiltContext={wuiltContext} />
    </StyledElementEdit>
  );
};

export { ElementEdit };

/**
 * Styles
 */

const StyledToggleSettings = styled.div`
  inset: 0;
  z-index: 1;
  position: absolute;
  cursor: pointer;
`;

const StyledHoverBox = styled.div<{ noBackgroundHoverEffect: boolean }>`
  position: relative;

  ${StyledSettingsButtons} {
    display: none;
  }

  &:hover {
    outline: solid 1px #0e9384;
    outline-offset: 4px;
    ${({ noBackgroundHoverEffect }) =>
      !noBackgroundHoverEffect && "background-color: #0000001f;"}
    ${StyledSettingsButtons} {
      display: flex;
    }
  }
`;

const StyledElementEdit = styled.div<{
  isSorting: boolean;
  isNotAvailable: boolean;
}>`
  position: relative;
  margin-bottom: 20px;
  ${({ isSorting }) =>
    isSorting &&
    css`
      background-color: white;
      height: 60px;
      overflow: hidden;
    `}
  ${({ isNotAvailable }) =>
    isNotAvailable &&
    css`
      pointer-events: none;
    `}
`;
