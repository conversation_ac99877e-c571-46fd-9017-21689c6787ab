import React from "react";
import { FormattedMessage } from "react-intl";
import { CounterValueAndLabel } from "@wuilt/section-preview";
import { Stack, Text, Box } from "@chakra-ui/react";
import ColorInput from "../../../components/ColorInput";
import InputField from "../../../components/InputField";
import NumberInput from "../../../components/NumberInput";

interface CounterLabelAndValueSettingsProps {
  valueLabel?: boolean;
  counterValueAndLabelSettings: CounterValueAndLabel;
  updateCounterValueAndLabel: (counterValue: CounterValueAndLabel) => void;
}
const CounterLabelAndValueSettings: React.FC<
  CounterLabelAndValueSettingsProps
> = ({
  valueLabel = true,
  counterValueAndLabelSettings,
  updateCounterValueAndLabel,
}) => {
  return (
    <>
      <Stack direction="column" mb="12px" gap="0">
        {valueLabel && (
          <Text
            as="label"
            htmlFor="value"
            fontSize="12px"
            fontWeight="500"
            color="gray.700"
            mb="6px"
          >
            <FormattedMessage defaultMessage="Value" id="GufXy5" />
          </Text>
        )}
        <InputField
          type="text"
          value={counterValueAndLabelSettings.value}
          onChange={(e) => {
            updateCounterValueAndLabel({
              ...counterValueAndLabelSettings,
              value: e.target.value,
            });
          }}
        />
      </Stack>
      <Stack direction="row" gap="8px">
        <Stack direction="column" gap="0">
          <Text
            as="label"
            htmlFor="color"
            fontSize="12px"
            fontWeight="500"
            color="gray.700"
            mb="6px"
          >
            <FormattedMessage defaultMessage="Color" id="uMhpKe" />
          </Text>
          <ColorInput
            color={counterValueAndLabelSettings.color}
            onChange={(color) => {
              updateCounterValueAndLabel({
                ...counterValueAndLabelSettings,
                color: color,
              });
            }}
          />
        </Stack>
        <Stack direction="column" className="flex flex-col" gap="0">
          <Text
            as="label"
            htmlFor="size"
            fontSize="12px"
            fontWeight="500"
            color="gray.700"
            mb="6px"
          >
            <FormattedMessage defaultMessage="Font size" id="kc7szF" />
          </Text>
          <Box w="78px">
            <NumberInput
              min={8}
              max={96}
              value={counterValueAndLabelSettings.size}
              onChange={(_, value) => {
                updateCounterValueAndLabel({
                  ...counterValueAndLabelSettings,
                  size: value,
                });
              }}
            />
          </Box>
        </Stack>
      </Stack>
    </>
  );
};

export default CounterLabelAndValueSettings;
