import React from "react";
import {
  migrateCustomSectionData,
  UpdateDataArgs,
  CustomSection,
  WuiltContext,
  Page,
  UpdateDataFunc,
  Column,
  Row,
  SectionElement,
  TextObject,
} from "@wuilt/section-preview";
import { SortableContext, SortableItem } from "@wuilt/quilt";
import { ElementsEditor } from "../elements";
import {
  sortColumnElementsMutation,
  sortRowColumnsMutation,
  sortRowMutation,
} from "../shared/mutations";
import { resizeColumnsFallbackGridTemplates } from "../shared/utils/resize-column-utils";
import { addMetaData } from "../shared/utils/addMetaData";

async function getUpdatedData(
  callback: UpdateDataArgs | undefined,
  data: CustomSection
) {
  return typeof callback === "undefined"
    ? data
    : typeof callback === "function"
    ? await callback(data)
    : callback;
}

export interface CustomSectionRendererProps {
  data: CustomSection;
  wuiltContext: WuiltContext;
  activePage: Page;
  sectionId: string;
  updateUi: (args: CustomSection) => void;
  mutateApi: (args: CustomSection) => void;
  onUploadImage: (cb: (src: string) => void) => void;
}

export let dataContext: WuiltContext;

const CustomSectionRenderer: React.FC<CustomSectionRendererProps> = ({
  data: dataProp,
  wuiltContext,
  activePage,
  sectionId,
  updateUi: updateUiProp,
  mutateApi: mutateApiProp,
  onUploadImage: onUploadImageProp,
}) => {
  const { Section, Row, Column, Element } = ElementsEditor;

  dataContext = {
    ...wuiltContext,
    props: { ...wuiltContext?.props, activePage, sectionId },
  };

  const data = migrateCustomSectionData(dataProp);

  const updateUi: UpdateDataFunc = async (callback) => {
    const updatedData = await getUpdatedData(callback, data);
    updateUiProp?.(updatedData);
  };

  const mutateApi: UpdateDataFunc = async (callback) => {
    const updatedData = await getUpdatedData(callback, data);
    const dataWithMeta = addMetaData(updatedData);
    mutateApiProp?.(dataWithMeta);
  };

  const updateAndMutate: UpdateDataFunc = async (callback) => {
    const updatedData = await getUpdatedData(callback, data);
    mutateApi?.(updatedData);
  };

  const updateTextUi = (newText: TextObject) => {
    updateUi?.({
      ...data,
      text: { ...data?.text, ...newText },
    });
  };

  const updateTextApi = (newText: TextObject) => {
    mutateApi?.({
      ...data,
      text: { ...data?.text, ...newText },
    });
  };

  const onUploadImage = (cb: (src: string) => void) => {
    onUploadImageProp?.(cb);
  };

  const onSortRowColumns = (sortedColumns: Column[], rowIndex: number) => {
    mutateApi((prev) => sortRowColumnsMutation(prev, rowIndex, sortedColumns));
  };
  const onSortRow = (sortedRows: Row[]) => {
    mutateApi((prev) => sortRowMutation(prev, sortedRows));
  };

  const onSortColumnElements = (
    sortedElements: SectionElement[],
    rowIndex: number,
    columnIndx: number
  ) => {
    mutateApi((prev) =>
      sortColumnElementsMutation(prev, rowIndex, columnIndx, sortedElements)
    );
  };

  return (
    <Section settings={data.settings}>
      <SortableContext
        value={data?.rows}
        onChange={(sortedColumns) => onSortRow(sortedColumns)}
      >
        {data.rows?.map((row, i) => (
          <SortableItem
            key={row?.id}
            id={row?.id}
            useHandleOnly
            index={i}
            item={undefined}
          >
            {({ isSorting }) => (
              <Row
                updateUi={updateUi}
                wuiltContext={dataContext}
                mutateApi={mutateApi}
                onUploadImage={onUploadImage}
                isSorting={isSorting}
                key={row.id}
                row={row}
                rowIndex={i}
                isSingleRow={data?.rows?.length === 1}
                disableAdding={data?.rows?.length >= 3}
                updateAndMutate={updateAndMutate}
                isLastRow={i === data?.rows?.length - 1}
                isSingleColumn={row?.columns?.length === 1}
              >
                <SortableContext
                  horizontal
                  value={row?.columns}
                  onChange={(sortedColumns) =>
                    onSortRowColumns(sortedColumns, i)
                  }
                >
                  {row?.columns?.map((column, j) => (
                    <SortableItem
                      key={column?.id}
                      id={column?.id}
                      useHandleOnly
                      index={j}
                      item={undefined}
                    >
                      <Column
                        rowGap={row?.settings?.gap || 0}
                        rowIndex={i}
                        columnIndex={j}
                        column={column}
                        updateUi={updateUi}
                        mutateApi={mutateApi}
                        updateAndMutate={updateAndMutate}
                        onUploadImage={onUploadImage}
                        disableAdding={row?.columns?.length >= 8}
                        isSingleRow={data?.rows?.length === 1}
                        isSingleColumn={row?.columns?.length === 1}
                        isLastColumn={row?.columns?.length === j + 1}
                        wuiltContext={dataContext}
                        rowGridTemplates={
                          row?.settings?.layout?.gridTemplates ||
                          resizeColumnsFallbackGridTemplates(row)
                        }
                        rowFullWidth={!!row?.settings?.layout?.fullWidth}
                      >
                        <SortableContext
                          value={column?.elements}
                          onChange={(sortedElements) =>
                            onSortColumnElements(sortedElements, i, j)
                          }
                        >
                          {column?.elements?.map((element, k) => (
                            <SortableItem
                              key={element?.id}
                              index={k}
                              item={undefined}
                              id={element?.id}
                              useHandleOnly
                            >
                              {({ isSorting }) => (
                                <Element
                                  key={element?.id}
                                  isSorting={isSorting}
                                  Component={
                                    ElementsEditor[element.type!] as any
                                  }
                                  wuiltContext={dataContext}
                                  element={element}
                                  column={column}
                                  rowIndex={i}
                                  columnIndex={j}
                                  elementIndex={k}
                                  pages={wuiltContext?.props?.pages}
                                  updateUi={updateUi}
                                  mutateApi={mutateApi}
                                  updateAndMutate={updateAndMutate}
                                  onUploadImage={onUploadImage}
                                  text={data?.text}
                                  updateTextApi={updateTextApi}
                                  updateTextUi={updateTextUi}
                                />
                              )}
                            </SortableItem>
                          ))}
                        </SortableContext>
                      </Column>
                    </SortableItem>
                  ))}
                </SortableContext>
              </Row>
            )}
          </SortableItem>
        ))}
      </SortableContext>
    </Section>
  );
};

export { CustomSectionRenderer };
