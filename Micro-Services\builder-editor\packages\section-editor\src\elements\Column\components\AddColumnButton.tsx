import React from "react";
import { FormattedMessage } from "react-intl";
import trackEvent from "../../../shared/utils/trackEvent";
import { IconButton, Tooltip } from "@chakra-ui/react";
import { AddColumnIcon } from "@wuilt/react-icons";

interface AddColumnButtonProps {
  addColumnBefore: boolean;
  disableAdding: boolean;
  addColumn: (location?: "after" | "before") => void;
}

const AddColumnButton: React.FC<AddColumnButtonProps> = ({
  addColumnBefore,
  disableAdding,
  addColumn,
}) => {
  if (disableAdding) return null;

  return (
    <Tooltip
      hasArrow
      label={<FormattedMessage defaultMessage="Add Column" id="jUjjIq" />}
      placement="bottom"
    >
      <IconButton
        aria-label="Add Column"
        isRound
        variant="plain"
        background="white"
        shadow="sm"
        minW="35px"
        height="35px"
        _hover={{ background: "gray.100" }}
        onClick={() => {
          addColumn(addColumnBefore ? "before" : "after");
          trackEvent("columnAdded");
        }}
      >
        <AddColumnIcon size="16px" />
      </IconButton>
    </Tooltip>
  );
};

export default AddColumnButton;
