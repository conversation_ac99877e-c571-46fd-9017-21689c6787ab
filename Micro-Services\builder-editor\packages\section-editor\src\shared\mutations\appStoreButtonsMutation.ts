import {
  AppStoreButtonsSettings,
  SectionElement,
} from "@wuilt/section-preview";
import { nanoid } from "nanoid";

export function duplicateAppStoreButtonsMutation(element: SectionElement) {
  const type = element?.type;
  const appStoreButtonsSettings = element?.settings as AppStoreButtonsSettings;

  return {
    type,
    id: `Buttons:${nanoid()}`,
    settings: {
      ...appStoreButtonsSettings,
      appStoreButtons: appStoreButtonsSettings?.appStoreButtons?.map((b) => ({
        ...b,
        id: `AppStoreButton:${nanoid()}`,
      })),
    },
  };
}
