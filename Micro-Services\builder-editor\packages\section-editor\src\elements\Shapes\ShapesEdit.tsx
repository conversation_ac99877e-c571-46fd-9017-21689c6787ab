import { CSSProperties } from "react";
import shapesMapper from "./shapesMapper";

export function ShapesEdit({
  element,
  wuiltContext,
  mutateElementApi,
  updateElementUi,
}: any) {
  const style: CSSProperties = {
    fill: element.fill,
    filter: `drop-shadow(${element.shadow.x}px ${element.shadow.y}px ${element.shadow.blur}px ${element.shadow.color} ) blur(${element.blur}px)`,
    stroke: element.stroke,
    strokeWidth: element.strokeWidth,
  };

  const shape = shapesMapper(style, element.shape);
  return <div className="shapes-edit">{shape}</div>;
}
