import { Stack, Box, Label, InputField, Text } from "@wuilt/quilt";
import { Padding } from "@wuilt/section-preview";
import React from "react";
import { FormattedMessage } from "react-intl";

interface PaddingInputProps {
  value: Padding | undefined;
  onChange: (v: Padding) => void;
}

const PaddingInput: React.FC<PaddingInputProps> = ({
  value: padding = { top: 0, right: 0, bottom: 0, left: 0 },
  onChange,
}) => {
  return (
    <Box>
      <Text fontWeight="medium">
        <FormattedMessage defaultMessage="PADDING" id="k9TVDP" />
      </Text>
      <Stack direction="row">
        <Box>
          <Label>
            <FormattedMessage defaultMessage="Horizontal" id="NfU3/O" />
          </Label>
          <InputField
            prefix={<Text fontSize="medium" children="px" />}
            type="number"
            minValue={0}
            value={padding?.left}
            onChange={(value: any) => {
              onChange({ ...padding, left: value, right: value });
            }}
          />
        </Box>
        <Box>
          <Label>
            <FormattedMessage defaultMessage="Vertical" id="cLrroF" />
          </Label>
          <InputField
            prefix={<Text fontSize="medium" children="px" />}
            type="number"
            minValue={0}
            value={padding?.top}
            onChange={(value: any) => {
              onChange({ ...padding, top: value, bottom: value });
            }}
          />
        </Box>
      </Stack>
    </Box>
  );
};

export default PaddingInput;
