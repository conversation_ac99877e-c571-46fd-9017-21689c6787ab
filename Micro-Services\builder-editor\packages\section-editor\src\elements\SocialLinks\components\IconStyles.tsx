import React from "react";
import { FormattedMessage } from "react-intl";
import { IconShape, IconStyle, SocialLinksIcons } from "@wuilt/section-preview";
import styled from "styled-components";
import SingleIcon from "./SingleIcon";
import {
  Box,
  FormLabel,
  InputGroup,
  InputLeftElement,
  Stack,
} from "@chakra-ui/react";
import ColorInput from "../../../components/ColorInput";
import NumberInput from "../../../components/NumberInput";

const PX = "px";

const SocialLinkShape = [
  {
    Icon: SocialLinksIcons.FacebookFilled,
    label: <FormattedMessage defaultMessage="Filled" id="yrzvie" />,
    shape: IconShape.Filled,
    id: 1,
  },
  {
    Icon: SocialLinksIcons.FacebookOutlined,
    label: <FormattedMessage defaultMessage="Outlined" id="cAuxCa" />,
    shape: IconShape.Outlined,
    id: 2,
  },
  {
    Icon: SocialLinksIcons.FacebookColored,
    label: <FormattedMessage defaultMessage="Colored" id="Eb3SdH" />,
    shape: IconShape.Colored,
    id: 3,
  },
];

interface IconStylesProps {
  iconStyle?: IconStyle;
  updateSocialLinkStyle?: (iconStyle: IconStyle) => void;
}

function IconStyles({ iconStyle, updateSocialLinkStyle }: IconStylesProps) {
  return (
    <Stack gap="16px">
      <Box>
        <FormLabel fontSize="16px" fontWeight="400" mb="4px">
          <FormattedMessage defaultMessage="Icon shape" id="q148ud" />
        </FormLabel>
        <IconShapeContainer>
          {SocialLinkShape.map(({ Icon, label, shape, id }) => (
            <SingleIcon
              selected={iconStyle?.shape === shape}
              key={id}
              Icon={Icon}
              text={label}
              onClick={() => {
                updateSocialLinkStyle({ ...iconStyle, shape: shape });
              }}
            />
          ))}
        </IconShapeContainer>
      </Box>
      <Stack gap="16px" align="center" direction="row">
        <Box width="100%">
          <FormLabel fontSize="16px" fontWeight="400" mb="4px">
            <FormattedMessage defaultMessage="Size" id="agOXPD" />
          </FormLabel>
          <InputGroup>
            <InputLeftElement color="GrayText">{PX}</InputLeftElement>
            <NumberInput
              width={iconStyle?.shape === IconShape.Colored ? "162px" : "100%"}
              value={iconStyle?.size}
              min={20}
              inputFieldProps={{
                paddingInlineStart: "35px",
              }}
              onChange={(value) => {
                if (value < "20") return;
                updateSocialLinkStyle({ ...iconStyle, size: value });
              }}
            />
          </InputGroup>
        </Box>
        {iconStyle?.shape !== IconShape.Colored && (
          <Box width="100%">
            <FormLabel fontSize="16px" fontWeight="400" mb="4px">
              <FormattedMessage defaultMessage="Icon color" id="CyU8R7" />
            </FormLabel>
            <ColorInput
              color={iconStyle?.color || "#1D2939"}
              onChange={(color) => {
                updateSocialLinkStyle({ ...iconStyle, color: color });
              }}
            />
          </Box>
        )}
      </Stack>
    </Stack>
  );
}

export default IconStyles;
const IconShapeContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
`;
