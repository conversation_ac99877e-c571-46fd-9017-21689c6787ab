'use client'

import { useState } from 'react'
import {
  Box,
  VStack,
  HStack,
  Button,
  Text,
  IconButton,
  Flex,
  useColorModeValue,
  Divider,
  Badge,
  Image,
  SimpleGrid,
  Tooltip
} from '@chakra-ui/react'
import {
  CloseIcon,
  AddIcon,
  StarIcon,
  ChevronRightIcon
} from '@chakra-ui/icons'
import { motion, AnimatePresence } from 'framer-motion'
import { useEditorStore } from '@/lib/stores/editorStore'
import { useTranslation } from '@/lib/contexts/LanguageContext'
import { sectionDesigns } from '@/lib/sections/sectionDesigns'

interface AddSectionFloatingSidebarProps {
  isOpen: boolean
  onClose: () => void
  afterSectionId?: string
}

// Section categories matching Old Builder
const SECTION_CATEGORIES = [
  { id: 'hero', label: 'Hero', icon: '🎯' },
  { id: 'features', label: 'Features', icon: '⭐' },
  { id: 'about', label: 'About', icon: '📖' },
  { id: 'services', label: 'Services', icon: '🔧' },
  { id: 'team', label: 'Team', icon: '👥' },
  { id: 'testimonials', label: 'Testimonials', icon: '💬' },
  { id: 'contact', label: 'Contact', icon: '📞' },
  { id: 'gallery', label: 'Gallery', icon: '🖼️' },
  { id: 'pricing', label: 'Pricing', icon: '💰' },
  { id: 'cta', label: 'Call to Action', icon: '🚀' },
  { id: 'content', label: 'Content', icon: '📝' },
  { id: 'logos', label: 'Logos', icon: '🏢' },
]

export function AddSectionFloatingSidebar({ isOpen, onClose, afterSectionId }: AddSectionFloatingSidebarProps) {
  const [activeCategory, setActiveCategory] = useState('hero')
  const [hoveredDesign, setHoveredDesign] = useState<string | null>(null)
  const { addSection } = useEditorStore()
  const { t } = useTranslation()

  // Old Builder exact colors
  const bgColor = '#f9fafb'
  const sidebarBg = 'white'
  const borderColor = '#dfe3e8'
  const primaryColor = '#1f9aeb'
  const textColor = '#343a40'
  const lightGreen = '#d4edda'
  const lightBlue = '#e3f2fd'

  const handleAddCustomSection = () => {
    const customSection = {
      id: `custom-${Date.now()}`,
      type: 'custom' as const,
      name: 'Custom Section',
      elements: [],
      style: {
        padding: '60px 20px',
        backgroundColor: '#ffffff',
        minHeight: '200px'
      },
      responsive: {
        desktop: {},
        tablet: {},
        mobile: {}
      }
    }
    
    addSection(customSection, afterSectionId)
    onClose()
  }

  const handleAddSection = (designId: string, category: string) => {
    const designs = sectionDesigns[category as keyof typeof sectionDesigns] || []
    const design = designs.find(d => d.id === designId)
    
    if (design) {
      const newSection = {
        id: `section-${Date.now()}`,
        type: category as any,
        name: design.name,
        elements: [],
        style: design.defaultStyle || {},
        responsive: {
          desktop: {},
          tablet: {},
          mobile: {}
        }
      }
      
      addSection(newSection, afterSectionId)
      onClose()
    }
  }

  const getCategoryDesigns = (categoryId: string) => {
    return sectionDesigns[categoryId as keyof typeof sectionDesigns] || []
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <Box
            position="fixed"
            top="50px"
            left="0"
            right="0"
            bottom="0"
            bg="rgba(0, 0, 0, 0.3)"
            zIndex={999}
            onClick={onClose}
          />

          {/* Floating Sidebar */}
          <motion.div
            initial={{ x: -550 }}
            animate={{ x: 55 }}
            exit={{ x: -550 }}
            transition={{ duration: 0.2, ease: 'easeOut' }}
            style={{
              position: 'fixed',
              top: '50px',
              bottom: '0',
              left: '0',
              width: '530px',
              zIndex: 1000,
              background: bgColor,
              boxShadow: '3px 0px 0px rgba(0,0,0,0.05), 1px 0px 0px rgba(0,0,0,0.25)',
            }}
          >
            <VStack spacing={0} h="100%" align="stretch">
              {/* Header */}
              <HStack
                justify="space-between"
                align="center"
                bg="#e9ecef"
                px="15px"
                py="10px"
                borderBottom={`1px solid ${borderColor}`}
              >
                <Text color={textColor} fontSize="sm" fontWeight="600">
                  {t('editor.addSection')}
                </Text>
                <IconButton
                  aria-label="Close"
                  icon={<CloseIcon />}
                  size="sm"
                  variant="ghost"
                  onClick={onClose}
                />
              </HStack>

              <Flex h="100%">
                {/* Categories Sidebar */}
                <Box
                  w="200px"
                  bg={sidebarBg}
                  borderRight={`1px solid ${borderColor}`}
                  boxShadow="sm"
                >
                  <VStack spacing={0} align="stretch" p="10px">
                    {/* Custom Section Button */}
                    <Button
                      leftIcon={<StarIcon />}
                      size="sm"
                      bg={primaryColor}
                      color="white"
                      borderRadius="6px"
                      fontWeight="600"
                      mb="10px"
                      onClick={handleAddCustomSection}
                      _hover={{
                        bg: '#177ab8',
                      }}
                    >
                      Custom Section
                    </Button>

                    <Divider mb="10px" />

                    {/* Category List */}
                    {SECTION_CATEGORIES.map((category) => (
                      <Button
                        key={category.id}
                        variant="ghost"
                        size="sm"
                        justifyContent="space-between"
                        bg={activeCategory === category.id ? lightGreen : 'transparent'}
                        borderColor={activeCategory === category.id ? '#28a745' : 'transparent'}
                        border="1px solid"
                        borderRadius="6px"
                        color={activeCategory === category.id ? '#28a745' : '#6c757d'}
                        fontWeight="600"
                        fontSize="sm"
                        px="10px"
                        py="5px"
                        mb="2px"
                        onClick={() => setActiveCategory(category.id)}
                        _hover={{
                          bg: activeCategory !== category.id ? '#f8f9fa' : lightGreen,
                          borderColor: activeCategory !== category.id ? borderColor : '#28a745',
                        }}
                      >
                        <HStack spacing={2}>
                          <Text>{category.icon}</Text>
                          <Text>{category.label}</Text>
                        </HStack>
                        {activeCategory === category.id && (
                          <ChevronRightIcon />
                        )}
                      </Button>
                    ))}
                  </VStack>
                </Box>

                {/* Designs Preview Area */}
                <Box
                  flex="1"
                  bg={bgColor}
                  p="15px"
                  overflowY="auto"
                >
                  <SimpleGrid columns={2} spacing={3}>
                    {getCategoryDesigns(activeCategory).map((design) => (
                      <Box
                        key={design.id}
                        bg="white"
                        borderRadius="8px"
                        border="2px solid"
                        borderColor={hoveredDesign === design.id ? primaryColor : borderColor}
                        overflow="hidden"
                        cursor="pointer"
                        transition="all 0.2s"
                        _hover={{
                          borderColor: primaryColor,
                          boxShadow: '0 4px 12px rgba(31, 154, 235, 0.15)',
                        }}
                        onClick={() => handleAddSection(design.id, activeCategory)}
                        onMouseEnter={() => setHoveredDesign(design.id)}
                        onMouseLeave={() => setHoveredDesign(null)}
                      >
                        <Box
                          h="120px"
                          bg="gray.100"
                          display="flex"
                          alignItems="center"
                          justifyContent="center"
                          position="relative"
                        >
                          {design.thumbnail ? (
                            <Image
                              src={design.thumbnail}
                              alt={design.name}
                              w="100%"
                              h="100%"
                              objectFit="cover"
                            />
                          ) : (
                            <Text fontSize="2xl" color="gray.400">
                              {SECTION_CATEGORIES.find(c => c.id === activeCategory)?.icon}
                            </Text>
                          )}
                          
                          {hoveredDesign === design.id && (
                            <Box
                              position="absolute"
                              top="0"
                              left="0"
                              right="0"
                              bottom="0"
                              bg="rgba(31, 154, 235, 0.9)"
                              display="flex"
                              alignItems="center"
                              justifyContent="center"
                            >
                              <AddIcon color="white" fontSize="xl" />
                            </Box>
                          )}
                        </Box>
                        
                        <Box p="12px">
                          <Text
                            fontSize="sm"
                            fontWeight="600"
                            color={textColor}
                            mb="4px"
                          >
                            {design.name}
                          </Text>
                          <Text
                            fontSize="xs"
                            color="#6c757d"
                            noOfLines={2}
                          >
                            {design.description}
                          </Text>
                        </Box>
                      </Box>
                    ))}
                  </SimpleGrid>
                </Box>
              </Flex>
            </VStack>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}
