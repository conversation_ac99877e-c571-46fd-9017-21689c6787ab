import React from "react";
import { FormattedMessage } from "react-intl";
import BorderTab from "../../../shared/components/column-styles/border/BorderTab";
import BorderRadiusTab from "../../../shared/components/column-styles/borderRadius/BorderRadiusTab";
import ShadowTab from "../../../shared/components/column-styles/shadow/ShadowTab";
import { Settings } from "@wuilt/section-preview";
import {
  Divider,
  Heading,
  Accordion,
  AccordionItem,
  AccordionButton,
  Box,
  AccordionIcon,
  AccordionPanel,
} from "@chakra-ui/react";
interface ColumnStyleProps {
  settings: Settings | undefined;
  updateSettings: (v: Settings) => void;
}

function ColumnStyle({ settings, updateSettings }: ColumnStyleProps) {
  const StylesTabs = [
    {
      id: 1,
      header: <FormattedMessage defaultMessage="Border" id="sGaEsd" />,
      body: (
        <BorderTab
          borders={settings?.layout?.borders}
          onChange={(borders) => {
            updateSettings({
              ...settings,
              layout: { ...settings?.layout, borders },
            });
          }}
        />
      ),
    },
    {
      id: 2,
      header: <FormattedMessage defaultMessage="Border radius" id="METN89" />,
      body: (
        <BorderRadiusTab
          borderRadius={settings?.layout?.borderRadius}
          onChange={(borderRadius) => {
            updateSettings({
              ...settings,
              layout: { ...settings?.layout, borderRadius },
            });
          }}
        />
      ),
    },
    {
      id: 3,
      header: <FormattedMessage defaultMessage="Shadow" id="u4jNiO" />,
      body: (
        <ShadowTab
          shadow={settings?.layout?.shadow}
          updateShadow={(shadow) => {
            updateSettings({
              ...settings,
              layout: { ...settings?.layout, shadow },
            });
          }}
        />
      ),
    },
  ];

  return (
    <div>
      <Accordion allowToggle>
        {StylesTabs.map(({ header, body, id }) => {
          return (
            <AccordionItem key={id}>
              <AccordionButton
                _expanded={{ color: "primary.500", bg: "gray.100" }}
                bg="white"
              >
                <Box flex="1" textAlign="left">
                  <Heading color="inherit" fontSize="16px" fontWeight="700">
                    {header}
                  </Heading>
                </Box>
                <AccordionIcon />
              </AccordionButton>
              <AccordionPanel background="gray.100">
                <Divider borderBottomColor="gray.300" mb="12px" />
                {body}
              </AccordionPanel>
            </AccordionItem>
          );
        })}
      </Accordion>
    </div>
  );
}

export default ColumnStyle;
