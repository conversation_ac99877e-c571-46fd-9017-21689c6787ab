import {
  InputField,
  BorderRadiusTopLeftIcon,
  BorderRadiusTopRightIcon,
  Box,
  BorderRadiusBottomLeftIcon,
  BorderRadiusBottomRightIcon,
} from "@wuilt/quilt";
import { BorderRadius } from "@wuilt/section-preview";
import styled from "styled-components";
interface SelectedBorderRadiusProps {
  borderRadius: BorderRadius | undefined;
  onChange: (v: BorderRadius) => void;
}

function PerCorner({ borderRadius, onChange }: SelectedBorderRadiusProps) {
  const borderInputs = [
    {
      id: 1,
      borderRadiusValue: borderRadius?.borderTopLeftRadius,
      prefix: <BorderRadiusTopLeftIcon />,
      prefixBorder: true,
      suffix: false,
      suffixBorder: false,
      updateBorder: (value: any) => {
        onChange({
          ...borderRadius,
          borderTopLeftRadius: value,
        });
      },
    },
    {
      id: 2,
      borderRadiusValue: borderRadius?.borderTopRightRadius,
      prefix: false,
      prefixBorder: false,
      suffix: <BorderRadiusTopRightIcon />,
      suffixBorder: true,
      updateBorder: (value: any) => {
        onChange({
          ...borderRadius,
          borderTopRightRadius: value,
        });
      },
    },
    {
      id: 3,
      borderRadiusValue: borderRadius?.borderBottomLeftRadius,
      prefix: <BorderRadiusBottomLeftIcon />,
      prefixBorder: true,
      suffix: false,
      suffixBorder: false,
      updateBorder: (value: any) => {
        onChange({
          ...borderRadius,
          borderBottomLeftRadius: value,
        });
      },
    },
    {
      id: 4,
      borderRadiusValue: borderRadius?.borderBottomRightRadius,
      prefix: false,
      prefixBorder: true,
      suffix: <BorderRadiusBottomRightIcon />,
      suffixBorder: true,
      updateBorder: (value: any) => {
        onChange({
          ...borderRadius,
          borderBottomRightRadius: value,
        });
      },
    },
  ];

  return (
    <Grid>
      {borderInputs.map(
        ({
          id,
          prefix,
          prefixBorder,
          suffix,
          suffixBorder,
          updateBorder,
          borderRadiusValue,
        }) => {
          return (
            <Box key={id}>
              <InputField
                width="100%"
                prefixBorder={prefixBorder}
                hideArrows
                prefix={prefix}
                suffix={suffix}
                suffixBorder={suffixBorder}
                type="number"
                value={borderRadiusValue || 1}
                minValue={1}
                onChange={(value) => {
                  updateBorder(value);
                }}
              />
            </Box>
          );
        }
      )}
    </Grid>
  );
}

export default PerCorner;

const Grid = styled.div`
  margin-top: 15px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
`;
