// CSS Loader for Old Builder SCSS files
// This converts SCSS variables and mixins to CSS custom properties

export function convertScssToCSS(scssContent: string): string {
  // Basic SCSS to CSS conversion
  let css = scssContent
  
  // Convert SCSS variables to CSS custom properties
  css = css.replace(/\$([a-zA-Z0-9_-]+):\s*([^;]+);/g, '--$1: $2;')
  
  // Convert variable usage
  css = css.replace(/\$([a-zA-Z0-9_-]+)/g, 'var(--$1)')
  
  // Remove SCSS imports (we'll handle these separately)
  css = css.replace(/@import\s+["'][^"']+["'];?/g, '')
  
  // Convert basic mixins (simplified)
  css = css.replace(/@include\s+([a-zA-Z0-9_-]+)\([^)]*\);?/g, '/* mixin: $1 */')
  
  // Remove SCSS comments
  css = css.replace(/\/\/[^\n\r]*/g, '')
  
  // Convert nested selectors (basic support)
  css = convertNestedSelectors(css)
  
  return css
}

function convertNestedSelectors(css: string): string {
  // This is a simplified nested selector converter
  // In a real implementation, you'd want a proper SCSS parser
  
  const lines = css.split('\n')
  const result: string[] = []
  const selectorStack: string[] = []
  let currentIndent = 0
  
  for (const line of lines) {
    const trimmed = line.trim()
    const indent = line.length - line.trimStart().length
    
    if (trimmed.includes('{')) {
      // Opening brace - new selector
      const selector = trimmed.replace('{', '').trim()
      
      if (indent > currentIndent) {
        // Nested selector
        selectorStack.push(selector)
      } else {
        // Same level or going back
        while (selectorStack.length > indent / 2) {
          selectorStack.pop()
        }
        selectorStack.push(selector)
      }
      
      result.push(`${selectorStack.join(' ')} {`)
      currentIndent = indent
    } else if (trimmed === '}') {
      result.push('}')
      selectorStack.pop()
    } else if (trimmed) {
      result.push(line)
    }
  }
  
  return result.join('\n')
}

// Common Old Builder CSS variables
export const oldBuilderCSSVariables = `
  :root {
    --theme-color-brand1: #007bff;
    --theme-color-secondary1: #6c757d;
    --theme-color-success: #28a745;
    --theme-color-danger: #dc3545;
    --theme-color-warning: #ffc107;
    --theme-color-info: #17a2b8;
    --theme-color-light: #f8f9fa;
    --theme-color-dark: #343a40;
    
    --text-color: #333333;
    --text-color-light: #6c757d;
    --text-color-muted: #999999;
    
    --border-color: #dee2e6;
    --border-radius: 6px;
    --border-radius-sm: 4px;
    --border-radius-lg: 8px;
    
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;
    
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-xxl: 1.5rem;
    
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    --line-height-tight: 1.2;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.6;
    
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
    
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
  }
  
  /* Bootstrap-like grid system */
  .container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
  }
  
  .container-fluid {
    width: 100%;
    padding: 0 15px;
  }
  
  .row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
  }
  
  .col {
    flex: 1;
    padding: 0 15px;
  }
  
  .col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .col-3 { flex: 0 0 25%; max-width: 25%; }
  .col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
  .col-6 { flex: 0 0 50%; max-width: 50%; }
  .col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
  .col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .col-9 { flex: 0 0 75%; max-width: 75%; }
  .col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
  .col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
  .col-12 { flex: 0 0 100%; max-width: 100%; }
  
  .col-xs-12 { flex: 0 0 100%; max-width: 100%; }
  .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
  .col-md-6 { flex: 0 0 50%; max-width: 50%; }
  .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
  
  @media (max-width: 576px) {
    .col-sm-6, .col-md-6, .col-lg-6 {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }
  
  @media (max-width: 768px) {
    .col-md-6, .col-lg-6 {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }
  
  /* Alignment utilities */
  .align-items-start { align-items: flex-start; }
  .align-items-center { align-items: center; }
  .align-items-end { align-items: flex-end; }
  .align-items-top { align-items: flex-start; }
  .align-items-middle { align-items: center; }
  .align-items-bottom { align-items: flex-end; }
  
  .justify-content-start { justify-content: flex-start; }
  .justify-content-center { justify-content: center; }
  .justify-content-end { justify-content: flex-end; }
  .justify-content-between { justify-content: space-between; }
  .justify-content-around { justify-content: space-around; }
  
  /* Spacing utilities */
  .spaceAfter--xSmall { margin-bottom: var(--spacing-sm); }
  .spaceAfter--small { margin-bottom: var(--spacing-md); }
  .spaceAfter--medium { margin-bottom: var(--spacing-lg); }
  .spaceAfter--large { margin-bottom: var(--spacing-xl); }
  .spaceAfter--xLarge { margin-bottom: var(--spacing-xxl); }
  
  .spaceBefore--xSmall { margin-top: var(--spacing-sm); }
  .spaceBefore--small { margin-top: var(--spacing-md); }
  .spaceBefore--medium { margin-top: var(--spacing-lg); }
  .spaceBefore--large { margin-top: var(--spacing-xl); }
  .spaceBefore--xLarge { margin-top: var(--spacing-xxl); }
  
  /* Text utilities */
  .text-left { text-align: left; }
  .text-center { text-align: center; }
  .text-right { text-align: right; }
  
  .text-primary { color: var(--theme-color-brand1); }
  .text-secondary { color: var(--theme-color-secondary1); }
  .text-success { color: var(--theme-color-success); }
  .text-danger { color: var(--theme-color-danger); }
  .text-warning { color: var(--theme-color-warning); }
  .text-info { color: var(--theme-color-info); }
  .text-light { color: var(--theme-color-light); }
  .text-dark { color: var(--theme-color-dark); }
  .text-muted { color: var(--text-color-muted); }
  
  /* Display utilities */
  .d-none { display: none; }
  .d-block { display: block; }
  .d-inline { display: inline; }
  .d-inline-block { display: inline-block; }
  .d-flex { display: flex; }
  
  /* RTL support */
  .rtl {
    direction: rtl;
  }
  
  .rtl .text-left { text-align: right; }
  .rtl .text-right { text-align: left; }
`

// Inject Old Builder CSS variables and utilities
export function injectOldBuilderCSS() {
  if (typeof document === 'undefined') return
  
  const existingStyle = document.getElementById('old-builder-global-styles')
  if (existingStyle) return
  
  const style = document.createElement('style')
  style.id = 'old-builder-global-styles'
  style.textContent = oldBuilderCSSVariables
  
  document.head.appendChild(style)
}
