import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { saveSections, loadSections, saveSection, deleteSection as deleteSupabaseSection } from '@/lib/supabase/sections'

// Types based on Old Builder analysis
export interface Element {
  id: string
  type: 'text' | 'image' | 'video' | 'button' | 'form' | 'map' | 'social' | 'navmenu' | 'icon' | 'slideshow' | 'custom'
  props: Record<string, any>
  style: Record<string, any>
  children?: Element[]
  parentId?: string
}

export interface Section {
  id: string
  type: 'hero' | 'feature' | 'testimonial' | 'contact' | 'gallery' | 'custom'
  name: string
  elements: Element[]
  style: Record<string, any>
  responsive: {
    desktop: Record<string, any>
    tablet: Record<string, any>
    mobile: Record<string, any>
  }
  props?: Record<string, any>
  oldBuilderDesign?: {
    category: string
    designId: string
    name: string
    thumbnail?: string
  }
}

export interface Page {
  id: string
  name: string
  slug: string
  sections: Section[]
  seoSettings: {
    title: string
    description: string
    keywords: string[]
  }
  settings: Record<string, any>
}

export interface EditorAction {
  type: 'add' | 'update' | 'delete' | 'move'
  target: 'section' | 'element'
  data: any
  timestamp: number
}

export type Breakpoint = 'desktop' | 'tablet' | 'mobile'

interface EditorStore {
  // Current state
  currentPage: Page | null
  selectedElement: Element | null
  selectedSection: Section | null

  // Multi-selection state
  selectedElements: Element[]
  clipboard: {
    elements: Element[]
    sections: Section[]
  }

  // Editor state
  isPreviewMode: boolean
  currentBreakpoint: Breakpoint
  isDragging: boolean
  editMode: 'visual' | 'code' | 'preview'

  // Supabase integration
  currentWebsiteId: string | null
  isSaving: boolean
  isLoading: boolean

  // History
  undoStack: EditorAction[]
  redoStack: EditorAction[]

  // Actions
  setCurrentPage: (page: Page) => void
  selectElement: (element: Element | null) => void
  selectSection: (section: Section | null) => void

  // Multi-selection actions
  addToSelection: (element: Element) => void
  removeFromSelection: (elementId: string) => void
  clearSelection: () => void
  selectMultiple: (elements: Element[]) => void
  
  // Clipboard operations
  copyToClipboard: (elements: Element[], sections?: Section[]) => void
  pasteFromClipboard: (targetSectionId?: string) => void

  // Element operations
  addElement: (element: Element, sectionId: string) => void
  updateElement: (id: string, props: Partial<Element>) => void
  updateMultipleElements: (updates: Array<{ id: string; props: Partial<Element> }>) => void
  deleteElement: (id: string) => void
  deleteMultipleElements: (ids: string[]) => void
  moveElement: (elementId: string, newParentId: string, index: number) => void
  duplicateElement: (id: string) => void
  
  // Section operations
  addSection: (section: Section, index?: number) => void
  updateSection: (id: string, props: Partial<Section>) => void
  deleteSection: (id: string) => void
  moveSection: (sectionId: string, newIndex: number) => void
  
  // Editor controls
  setPreviewMode: (isPreview: boolean) => void
  setBreakpoint: (breakpoint: Breakpoint) => void
  setCurrentBreakpoint: (breakpoint: Breakpoint) => void
  setDragging: (isDragging: boolean) => void
  setEditMode: (mode: 'visual' | 'code' | 'preview') => void
  
  // History operations
  undo: () => void
  redo: () => void
  addToHistory: (action: EditorAction) => void
  canUndo: boolean
  canRedo: boolean

  // Element movement and visibility
  moveElementUp: (sectionId: string, elementId: string) => void
  moveElementDown: (sectionId: string, elementId: string) => void
  toggleElementVisibility: (sectionId: string, elementId: string) => void
  setSelectedElement: (elementId: string | null) => void

  // Utility
  getElementById: (id: string) => Element | null
  getSectionById: (id: string) => Section | null

  // Supabase integration
  setCurrentWebsite: (websiteId: string) => void
  saveToSupabase: () => Promise<{ success: boolean; error?: string }>
  loadFromSupabase: (websiteId: string) => Promise<{ success: boolean; error?: string }>
  saveSectionToSupabase: (sectionId: string) => Promise<{ success: boolean; error?: string }>
}

export const useEditorStore = create<EditorStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      currentPage: null,
      selectedElement: null,
      selectedSection: null,
      selectedElements: [],
      clipboard: {
        elements: [],
        sections: []
      },
      isPreviewMode: false,
      currentBreakpoint: 'desktop',
      isDragging: false,
      editMode: 'visual',
      currentWebsiteId: null,
      isSaving: false,
      isLoading: false,
      undoStack: [],
      redoStack: [],

      // Basic setters
      setCurrentPage: (page) => set({ currentPage: page }),
      selectElement: (element) => set({
        selectedElement: element,
        selectedElements: element ? [element] : []
      }),
      selectSection: (section) => set({ selectedSection: section }),

      // Multi-selection actions
      addToSelection: (element) => {
        const state = get()
        if (!state.selectedElements.find(e => e.id === element.id)) {
          set({
            selectedElements: [...state.selectedElements, element],
            selectedElement: element
          })
        }
      },

      removeFromSelection: (elementId) => {
        const state = get()
        const newSelection = state.selectedElements.filter(e => e.id !== elementId)
        set({
          selectedElements: newSelection,
          selectedElement: newSelection.length > 0 ? newSelection[newSelection.length - 1] : null
        })
      },

      clearSelection: () => set({
        selectedElements: [],
        selectedElement: null,
        selectedSection: null
      }),

      selectMultiple: (elements) => set({
        selectedElements: elements,
        selectedElement: elements.length > 0 ? elements[elements.length - 1] : null
      }),

      // Clipboard operations
      copyToClipboard: (elements, sections = []) => {
        set({
          clipboard: {
            elements: elements.map(el => ({ ...el, id: `${el.id}_copy` })),
            sections: sections.map(sec => ({ ...sec, id: `${sec.id}_copy` }))
          }
        })
      },

      pasteFromClipboard: (targetSectionId) => {
        const state = get()
        if (!state.currentPage || !targetSectionId) return

        const { elements } = state.clipboard
        if (elements.length === 0) return

        const newPage = { ...state.currentPage }
        const targetSection = newPage.sections.find(s => s.id === targetSectionId)

        if (targetSection) {
          elements.forEach(element => {
            const newElement = {
              ...element,
              id: `element_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
            }
            targetSection.elements.push(newElement)
          })

          set({ currentPage: newPage })

          // Add to history
          get().addToHistory({
            type: 'add',
            target: 'element',
            data: { elements, targetSectionId },
            timestamp: Date.now()
          })
        }
      },

      // Element operations
      addElement: (element, sectionId) => {
        const state = get()
        if (!state.currentPage) return

        const newPage = { ...state.currentPage }
        const section = newPage.sections.find(s => s.id === sectionId)
        if (section) {
          section.elements.push(element)
          set({ currentPage: newPage })
          
          // Add to history
          get().addToHistory({
            type: 'add',
            target: 'element',
            data: { element, sectionId },
            timestamp: Date.now()
          })
        }
      },

      updateElement: (id, props) => {
        const state = get()
        if (!state.currentPage) return

        const newPage = { ...state.currentPage }
        const element = get().getElementById(id)
        if (element) {
          Object.assign(element, props)
          set({ currentPage: newPage })

          // Add to history
          get().addToHistory({
            type: 'update',
            target: 'element',
            data: { id, props },
            timestamp: Date.now()
          })
        }
      },

      updateMultipleElements: (updates) => {
        const state = get()
        if (!state.currentPage) return

        const newPage = { ...state.currentPage }
        let hasChanges = false

        updates.forEach(({ id, props }) => {
          const element = get().getElementById(id)
          if (element) {
            Object.assign(element, props)
            hasChanges = true
          }
        })

        if (hasChanges) {
          set({ currentPage: newPage })

          // Add to history
          get().addToHistory({
            type: 'update',
            target: 'element',
            data: { updates },
            timestamp: Date.now()
          })
        }
      },

      deleteElement: (id) => {
        const state = get()
        if (!state.currentPage) return

        const newPage = { ...state.currentPage }
        for (const section of newPage.sections) {
          const index = section.elements.findIndex(e => e.id === id)
          if (index !== -1) {
            const deletedElement = section.elements.splice(index, 1)[0]
            set({
              currentPage: newPage,
              selectedElement: state.selectedElement?.id === id ? null : state.selectedElement,
              selectedElements: state.selectedElements.filter(e => e.id !== id)
            })

            // Add to history
            get().addToHistory({
              type: 'delete',
              target: 'element',
              data: { element: deletedElement, sectionId: section.id },
              timestamp: Date.now()
            })
            break
          }
        }
      },

      deleteMultipleElements: (ids) => {
        const state = get()
        if (!state.currentPage) return

        const newPage = { ...state.currentPage }
        const deletedElements: Array<{ element: Element; sectionId: string }> = []

        for (const section of newPage.sections) {
          for (let i = section.elements.length - 1; i >= 0; i--) {
            const element = section.elements[i]
            if (ids.includes(element.id)) {
              const deletedElement = section.elements.splice(i, 1)[0]
              deletedElements.push({ element: deletedElement, sectionId: section.id })
            }
          }
        }

        if (deletedElements.length > 0) {
          set({
            currentPage: newPage,
            selectedElement: null,
            selectedElements: []
          })

          // Add to history
          get().addToHistory({
            type: 'delete',
            target: 'element',
            data: { deletedElements },
            timestamp: Date.now()
          })
        }
      },

      duplicateElement: (id) => {
        const state = get()
        if (!state.currentPage) return

        const element = get().getElementById(id)
        if (!element) return

        const newPage = { ...state.currentPage }
        for (const section of newPage.sections) {
          const index = section.elements.findIndex(e => e.id === id)
          if (index !== -1) {
            const duplicatedElement = {
              ...element,
              id: `element_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
            }
            section.elements.splice(index + 1, 0, duplicatedElement)

            set({
              currentPage: newPage,
              selectedElement: duplicatedElement
            })

            // Add to history
            get().addToHistory({
              type: 'add',
              target: 'element',
              data: { element: duplicatedElement, sectionId: section.id },
              timestamp: Date.now()
            })
            break
          }
        }
      },

      moveElement: (elementId, newParentId, index) => {
        // Implementation for moving elements between sections
        const state = get()
        if (!state.currentPage) return
        
        // Add to history
        get().addToHistory({
          type: 'move',
          target: 'element',
          data: { elementId, newParentId, index },
          timestamp: Date.now()
        })
      },

      // Section operations
      addSection: (section, index) => {
        const state = get()
        if (!state.currentPage) return

        const newPage = { ...state.currentPage }
        if (index !== undefined) {
          newPage.sections.splice(index, 0, section)
        } else {
          newPage.sections.push(section)
        }
        
        set({ currentPage: newPage })
        
        // Add to history
        get().addToHistory({
          type: 'add',
          target: 'section',
          data: { section, index },
          timestamp: Date.now()
        })
      },

      updateSection: (id, props) => {
        const state = get()
        if (!state.currentPage) return

        const newPage = { ...state.currentPage }
        const section = newPage.sections.find(s => s.id === id)
        if (section) {
          Object.assign(section, props)
          set({ currentPage: newPage })
          
          // Add to history
          get().addToHistory({
            type: 'update',
            target: 'section',
            data: { id, props },
            timestamp: Date.now()
          })
        }
      },

      deleteSection: (id) => {
        const state = get()
        if (!state.currentPage) return

        const newPage = { ...state.currentPage }
        const index = newPage.sections.findIndex(s => s.id === id)
        if (index !== -1) {
          const deletedSection = newPage.sections.splice(index, 1)[0]
          set({ 
            currentPage: newPage,
            selectedSection: state.selectedSection?.id === id ? null : state.selectedSection
          })
          
          // Add to history
          get().addToHistory({
            type: 'delete',
            target: 'section',
            data: { section: deletedSection, index },
            timestamp: Date.now()
          })
        }
      },

      moveSection: (sectionId, newIndex) => {
        const state = get()
        if (!state.currentPage) return

        const newPage = { ...state.currentPage }
        const currentIndex = newPage.sections.findIndex(s => s.id === sectionId)
        if (currentIndex !== -1) {
          const section = newPage.sections.splice(currentIndex, 1)[0]
          newPage.sections.splice(newIndex, 0, section)
          set({ currentPage: newPage })
          
          // Add to history
          get().addToHistory({
            type: 'move',
            target: 'section',
            data: { sectionId, currentIndex, newIndex },
            timestamp: Date.now()
          })
        }
      },

      // Editor controls
      setPreviewMode: (isPreview) => set({ isPreviewMode: isPreview }),
      setBreakpoint: (breakpoint) => set({ currentBreakpoint: breakpoint }),
      setCurrentBreakpoint: (breakpoint) => set({ currentBreakpoint: breakpoint }),
      setDragging: (isDragging) => set({ isDragging }),
      setEditMode: (mode) => set({ editMode: mode }),

      // History operations
      undo: () => {
        const state = get()
        if (state.undoStack.length === 0) return

        const action = state.undoStack[state.undoStack.length - 1]
        const newUndoStack = state.undoStack.slice(0, -1)
        const newRedoStack = [...state.redoStack, action]

        // Reverse the action based on type
        if (action.target === 'section') {
          if (action.type === 'add') {
            // Remove the added section
            const newPage = { ...state.currentPage! }
            newPage.sections = newPage.sections.filter(s => s.id !== action.data.section.id)
            set({ currentPage: newPage })
          } else if (action.type === 'delete') {
            // Restore the deleted section
            const newPage = { ...state.currentPage! }
            newPage.sections.splice(action.data.index, 0, action.data.section)
            set({ currentPage: newPage })
          } else if (action.type === 'move') {
            // Move section back to original position
            const newPage = { ...state.currentPage! }
            const currentIndex = newPage.sections.findIndex(s => s.id === action.data.sectionId)
            if (currentIndex !== -1) {
              const section = newPage.sections.splice(currentIndex, 1)[0]
              newPage.sections.splice(action.data.currentIndex, 0, section)
              set({ currentPage: newPage })
            }
          }
        } else if (action.target === 'element') {
          if (action.type === 'add') {
            // Remove the added element
            const newPage = { ...state.currentPage! }
            const section = newPage.sections.find(s => s.id === action.data.sectionId)
            if (section) {
              section.elements = section.elements.filter(e => e.id !== action.data.element.id)
              set({ currentPage: newPage })
            }
          } else if (action.type === 'delete') {
            // Restore the deleted element
            const newPage = { ...state.currentPage! }
            const section = newPage.sections.find(s => s.id === action.data.sectionId)
            if (section) {
              section.elements.push(action.data.element)
              set({ currentPage: newPage })
            }
          }
        }

        set({
          undoStack: newUndoStack,
          redoStack: newRedoStack
        })
      },

      redo: () => {
        const state = get()
        if (state.redoStack.length === 0) return

        const action = state.redoStack[state.redoStack.length - 1]
        const newRedoStack = state.redoStack.slice(0, -1)
        const newUndoStack = [...state.undoStack, action]

        // Reapply the action based on type
        if (action.target === 'section') {
          if (action.type === 'add') {
            // Re-add the section
            const newPage = { ...state.currentPage! }
            if (action.data.index !== undefined) {
              newPage.sections.splice(action.data.index, 0, action.data.section)
            } else {
              newPage.sections.push(action.data.section)
            }
            set({ currentPage: newPage })
          } else if (action.type === 'delete') {
            // Re-delete the section
            const newPage = { ...state.currentPage! }
            newPage.sections = newPage.sections.filter(s => s.id !== action.data.section.id)
            set({ currentPage: newPage })
          } else if (action.type === 'move') {
            // Re-move section to new position
            const newPage = { ...state.currentPage! }
            const currentIndex = newPage.sections.findIndex(s => s.id === action.data.sectionId)
            if (currentIndex !== -1) {
              const section = newPage.sections.splice(currentIndex, 1)[0]
              newPage.sections.splice(action.data.newIndex, 0, section)
              set({ currentPage: newPage })
            }
          }
        } else if (action.target === 'element') {
          if (action.type === 'add') {
            // Re-add the element
            const newPage = { ...state.currentPage! }
            const section = newPage.sections.find(s => s.id === action.data.sectionId)
            if (section) {
              section.elements.push(action.data.element)
              set({ currentPage: newPage })
            }
          } else if (action.type === 'delete') {
            // Re-delete the element
            const newPage = { ...state.currentPage! }
            const section = newPage.sections.find(s => s.id === action.data.sectionId)
            if (section) {
              section.elements = section.elements.filter(e => e.id !== action.data.element.id)
              set({ currentPage: newPage })
            }
          }
        }

        set({
          undoStack: newUndoStack,
          redoStack: newRedoStack
        })
      },

      addToHistory: (action) => {
        const state = get()
        const newUndoStack = [...state.undoStack, action]
        
        // Limit history size
        if (newUndoStack.length > 50) {
          newUndoStack.shift()
        }
        
        set({
          undoStack: newUndoStack,
          redoStack: [] // Clear redo stack when new action is added
        })
      },

      // Utility functions
      getElementById: (id) => {
        const state = get()
        if (!state.currentPage) return null

        for (const section of state.currentPage.sections) {
          const element = section.elements.find(e => e.id === id)
          if (element) return element
        }
        return null
      },

      getSectionById: (id) => {
        const state = get()
        if (!state.currentPage) return null

        return state.currentPage.sections.find(s => s.id === id) || null
      },

      // Element movement and visibility methods
      moveElementUp: (sectionId, elementId) => {
        const state = get()
        if (!state.currentPage) return

        const newPage = { ...state.currentPage }
        const section = newPage.sections.find(s => s.id === sectionId)
        if (!section) return

        const elementIndex = section.elements.findIndex(e => e.id === elementId)
        if (elementIndex > 0) {
          const element = section.elements.splice(elementIndex, 1)[0]
          section.elements.splice(elementIndex - 1, 0, element)
          set({ currentPage: newPage })

          get().addToHistory({
            type: 'move',
            target: 'element',
            data: { sectionId, elementId, direction: 'up' },
            timestamp: Date.now()
          })
        }
      },

      moveElementDown: (sectionId, elementId) => {
        const state = get()
        if (!state.currentPage) return

        const newPage = { ...state.currentPage }
        const section = newPage.sections.find(s => s.id === sectionId)
        if (!section) return

        const elementIndex = section.elements.findIndex(e => e.id === elementId)
        if (elementIndex < section.elements.length - 1) {
          const element = section.elements.splice(elementIndex, 1)[0]
          section.elements.splice(elementIndex + 1, 0, element)
          set({ currentPage: newPage })

          get().addToHistory({
            type: 'move',
            target: 'element',
            data: { sectionId, elementId, direction: 'down' },
            timestamp: Date.now()
          })
        }
      },

      toggleElementVisibility: (sectionId, elementId) => {
        const state = get()
        if (!state.currentPage) return

        const newPage = { ...state.currentPage }
        const section = newPage.sections.find(s => s.id === sectionId)
        if (!section) return

        const element = section.elements.find(e => e.id === elementId)
        if (element) {
          element.props = { ...element.props, isVisible: !element.props.isVisible }
          set({ currentPage: newPage })

          get().addToHistory({
            type: 'update',
            target: 'element',
            data: { sectionId, elementId, props: { isVisible: element.props.isVisible } },
            timestamp: Date.now()
          })
        }
      },

      setSelectedElement: (elementId) => {
        const state = get()
        if (elementId) {
          const element = get().getElementById(elementId)
          if (element) {
            set({ selectedElement: element })
          }
        } else {
          set({ selectedElement: null })
        }
      },



      // Computed properties
      get canUndo() {
        return get().undoStack.length > 0
      },

      get canRedo() {
        return get().redoStack.length > 0
      },

      // Supabase integration methods
      setCurrentWebsite: (websiteId) => set({ currentWebsiteId: websiteId }),

      saveToSupabase: async () => {
        const state = get()
        if (!state.currentWebsiteId || !state.currentPage) {
          return { success: false, error: 'No website or page selected' }
        }

        set({ isSaving: true })
        try {
          const result = await saveSections(state.currentWebsiteId, state.currentPage.sections)
          return result
        } catch (error) {
          console.error('Error saving to Supabase:', error)
          return { success: false, error: 'Failed to save to database' }
        } finally {
          set({ isSaving: false })
        }
      },

      loadFromSupabase: async (websiteId) => {
        set({ isLoading: true, currentWebsiteId: websiteId })
        try {
          const result = await loadSections(websiteId)
          if (result.error) {
            return { success: false, error: result.error }
          }

          // Create a page with the loaded sections
          const page: Page = {
            id: `page_${websiteId}`,
            name: 'Home',
            slug: 'home',
            sections: result.sections,
            seoSettings: {
              title: 'Home',
              description: '',
              keywords: []
            },
            settings: {}
          }

          set({ currentPage: page })
          return { success: true }
        } catch (error) {
          console.error('Error loading from Supabase:', error)
          return { success: false, error: 'Failed to load from database' }
        } finally {
          set({ isLoading: false })
        }
      },

      saveSectionToSupabase: async (sectionId) => {
        const state = get()
        if (!state.currentWebsiteId || !state.currentPage) {
          return { success: false, error: 'No website or page selected' }
        }

        const section = state.currentPage.sections.find(s => s.id === sectionId)
        if (!section) {
          return { success: false, error: 'Section not found' }
        }

        const position = state.currentPage.sections.findIndex(s => s.id === sectionId)

        try {
          const result = await saveSection(state.currentWebsiteId, section, position)
          return result
        } catch (error) {
          console.error('Error saving section to Supabase:', error)
          return { success: false, error: 'Failed to save section to database' }
        }
      }
    }),
    {
      name: 'editor-store'
    }
  )
)
