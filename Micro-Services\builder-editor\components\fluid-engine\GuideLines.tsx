import React from 'react';
import { Box } from '@chakra-ui/react';

interface GuideLine {
  position: number;
  color?: string;
  opacity?: number;
}

interface GuideLinesProps {
  gridGuideLines: {
    vertical: GuideLine[];
    horizontal: GuideLine[];
  };
  type: 'grid' | 'element';
}

export const GuideLines: React.FC<GuideLinesProps> = ({
  gridGuideLines,
  type,
}) => {
  const getLineColor = () => {
    switch (type) {
      case 'grid':
        return 'rgba(66, 153, 225, 0.6)'; // Blue for grid guidelines
      case 'element':
        return 'rgba(245, 101, 101, 0.8)'; // Red for element guidelines
      default:
        return 'rgba(160, 174, 192, 0.6)'; // Gray default
    }
  };

  const lineColor = getLineColor();

  return (
    <>
      {/* Vertical Guidelines */}
      {gridGuideLines.vertical.map((guideLine, index) => (
        <Box
          key={`vertical-${type}-${index}`}
          position="absolute"
          left={`${guideLine.position}px`}
          top="0"
          bottom="0"
          width="1px"
          bg={guideLine.color || lineColor}
          opacity={guideLine.opacity || 1}
          zIndex={1000}
          pointerEvents="none"
          transition="opacity 0.2s ease"
        />
      ))}

      {/* Horizontal Guidelines */}
      {gridGuideLines.horizontal.map((guideLine, index) => (
        <Box
          key={`horizontal-${type}-${index}`}
          position="absolute"
          top={`${guideLine.position}px`}
          left="0"
          right="0"
          height="1px"
          bg={guideLine.color || lineColor}
          opacity={guideLine.opacity || 1}
          zIndex={1000}
          pointerEvents="none"
          transition="opacity 0.2s ease"
        />
      ))}

      {/* Center Line (for alignment) */}
      {type === 'element' && gridGuideLines.vertical.length > 0 && (
        <Box
          position="absolute"
          left="50%"
          top="0"
          bottom="0"
          width="2px"
          bg="rgba(245, 101, 101, 0.4)"
          transform="translateX(-50%)"
          zIndex={999}
          pointerEvents="none"
          opacity={0.6}
        />
      )}
    </>
  );
};
