import React, { DragEvent, useEffect, useState } from "react";
import styled from "styled-components";
import AddColumnButton from "./AddColumnButton";
import ResizeColumnButton from "./ResizeColumnButton";
import { UpdateDataFunc } from "@wuilt/section-preview";
import { Divider } from "@chakra-ui/react";

// 1px transparent image for the dragged element
const draggedElement = document.createElement("img");
draggedElement.src =
  "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";

interface AddResizeColumnButtonsProps {
  addColumnBefore?: boolean;
  disableAdding?: boolean;
  disableResizing?: boolean;
  resizableElementRef?: React.MutableRefObject<HTMLDivElement | null>;
  columnIndex?: number;
  rowGridTemplates?: number[];
  siteDirection?: string;
  shiftPosition?: boolean;
  addColumn: (location?: "after" | "before") => void;
  mutateApi?: UpdateDataFunc;
  updateRowGridTemplates?: (gridTemplates: number[]) => void;
}

const AddResizeColumnButtons: React.FC<AddResizeColumnButtonsProps> = ({
  disableAdding,
  disableResizing,
  addColumnBefore,
  resizableElementRef,
  rowGridTemplates: rowGridTemplatesProp,
  columnIndex,
  siteDirection,
  addColumn,
  mutateApi,
  updateRowGridTemplates,
}) => {
  const [initialPos, setInitialPos] = useState(0);
  const [initialColumnSize, setInitialColumnSize] = useState(0);
  const [initialNextColumnSize, setInitialNextColumnSize] = useState(0);
  const rowGridTemplates = [...(rowGridTemplatesProp || [])];

  useEffect(() => {
    const handleDragOver = (event: any) => {
      event.preventDefault();
    };
    document.addEventListener("dragover", handleDragOver);
    return () => {
      document.removeEventListener("dragover", handleDragOver);
    };
  }, []);

  const onDragStart = (event: DragEvent<HTMLDivElement>) => {
    const columnInitialSize = rowGridTemplates?.[columnIndex!];
    const nextColumnInitialSize = rowGridTemplates?.[columnIndex! + 1];
    if (!columnInitialSize || !nextColumnInitialSize) return;
    event?.dataTransfer?.setDragImage(draggedElement, 0, 0);
    setInitialPos(event.clientX);
    setInitialColumnSize(columnInitialSize);
    setInitialNextColumnSize(nextColumnInitialSize);
  };

  const onDrag = (event: DragEvent<HTMLDivElement>) => {
    if (
      event?.clientX <= 0 ||
      columnIndex == null ||
      rowGridTemplates == null ||
      !resizableElementRef?.current
    ) {
      return;
    }
    const cellSize =
      resizableElementRef?.current?.offsetWidth / rowGridTemplates[columnIndex];
    const isRTL = siteDirection === "rtl";
    let delta = event?.clientX - initialPos;
    delta = isRTL ? -delta : delta;
    const steps = Math.floor(Math.abs(delta) / cellSize);
    if (delta >= 0) {
      if (initialNextColumnSize - steps < 1) return;
      rowGridTemplates[columnIndex] = initialColumnSize + steps;
      rowGridTemplates[columnIndex + 1] = initialNextColumnSize - steps;
      updateRowGridTemplates?.(rowGridTemplates);
    } else if (delta < 0) {
      if (initialColumnSize - steps < 1) return;
      rowGridTemplates[columnIndex] = initialColumnSize - steps;
      rowGridTemplates[columnIndex + 1] = initialNextColumnSize + steps;
      updateRowGridTemplates?.(rowGridTemplates);
    }
  };

  const onDragEnd = () => {
    mutateApi?.();
  };

  if (disableAdding && disableResizing) return null;

  return (
    <StyledWrapper
      data-test="AddResizeColumnButtons"
      addColumnBefore={!!addColumnBefore}
      draggable={!disableResizing}
      onDragStart={onDragStart}
      onDrag={onDrag}
      onDragEnd={onDragEnd}
      cursor={!disableResizing ? "col-resize" : "unset"}
      isSiteDirectionRtl={siteDirection === "rtl"}
    >
      <Divider
        orientation="vertical"
        position="absolute"
        borderLeftColor="primary.500"
        borderLeftWidth="3px"
      />

      <ResizeColumnButton disableResizing={!!disableResizing} />
      <AddColumnButton
        addColumnBefore={!!addColumnBefore}
        disableAdding={!!disableAdding}
        addColumn={addColumn}
      />
    </StyledWrapper>
  );
};

export default AddResizeColumnButtons;

/**
 * Styles
 */

const StyledWrapper = styled.div<{
  addColumnBefore: boolean;
  isSiteDirectionRtl: boolean;
  cursor: string;
}>`
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 0;
  justify-content: flex-end;
  align-items: center;
  box-sizing: border-box;
  height: 100%;
  width: 40px;
  background: transparent;
  justify-content: center;
  z-index: 5;
  cursor: ${({ cursor }) => cursor};
  ${({ addColumnBefore, isSiteDirectionRtl }) =>
    addColumnBefore
      ? `${
          isSiteDirectionRtl
            ? "transform:translateX(50%)"
            : "transform:translateX(-50%)"
        }`
      : `${
          isSiteDirectionRtl
            ? "transform:translateX(-50%)"
            : "transform:translateX(50%)"
        }`};
  ${({ addColumnBefore, isSiteDirectionRtl }) =>
    addColumnBefore
      ? `${isSiteDirectionRtl ? `right:0` : `left:0`}`
      : `${isSiteDirectionRtl ? `left:0` : `right:0`}`};

  *,
  button {
    display: none;
  }
  &:hover {
    * {
      display: block;
    }
    button {
      display: flex;
    }
  }
`;
