import React, { <PERSON><PERSON><PERSON>, SetStateAction } from "react";
import { FormattedMessage } from "react-intl";
import { FormSettingsViews } from "../FormSettings";
import {
  AutoCompleteType,
  FieldType,
  FormField,
  TextObject,
} from "@wuilt/section-preview";
import { getAllFieldTexts } from "../../../../shared/utils/getTexts";
import {
  Button,
  Divider,
  FormControl,
  FormLabel,
  Switch,
  Text,
} from "@chakra-ui/react";
import { TrashIcon } from "@wuilt/react-icons";
import InputField from "../../../../components/InputField";
import Select from "../../../../components/Select";

const AUTOCOMPLETE_OPTIONS = [
  {
    label: <FormattedMessage defaultMessage="Name" id="HAlOn1" />,
    value: AutoCompleteType.Name,
  },
  {
    label: <FormattedMessage defaultMessage="User name" id="o7nzDs" />,
    value: AutoCompleteType.UserName,
  },
  {
    label: <FormattedMessage defaultMessage="Organization title" id="vx6gmF" />,
    value: AutoCompleteType.OrganizationTitle,
  },
  {
    label: <FormattedMessage defaultMessage="Organization" id="K56Dim" />,
    value: AutoCompleteType.Organization,
  },
  {
    label: <FormattedMessage defaultMessage="Street address" id="VYn2g8" />,
    value: AutoCompleteType.StreetAddress,
  },
  {
    label: <FormattedMessage defaultMessage="Address line 1" id="B52Em/" />,
    value: AutoCompleteType.AddressLine1,
  },
  {
    label: <FormattedMessage defaultMessage="Address line 2" id="oQY0a2" />,
    value: AutoCompleteType.AddressLine2,
  },
  {
    label: <FormattedMessage defaultMessage="Address line 3" id="WNg/rT" />,
    value: AutoCompleteType.AddressLine3,
  },
  {
    label: <FormattedMessage defaultMessage="Country" id="vONi+O" />,
    value: AutoCompleteType.Country,
  },
  {
    label: <FormattedMessage defaultMessage="Country name" id="SbN+uq" />,
    value: AutoCompleteType.CountryName,
  },
  {
    label: <FormattedMessage defaultMessage="Postal code" id="3EnruA" />,
    value: AutoCompleteType.PostalCode,
  },
  {
    label: <FormattedMessage defaultMessage="Language" id="y1Z3or" />,
    value: AutoCompleteType.Language,
  },
];

interface TextSettingsProps {
  field?: FormField;
  text: TextObject;
  fieldIndex: number;
  isAppRtl?: boolean;
  disablePlaceHolder?: boolean;
  updateFieldSettings: (newField: FormField, index: number) => void;
  deleteField: (id: string, deletedTexts: TextObject) => void;
  setView: Dispatch<SetStateAction<FormSettingsViews>>;
  updateText: (newTexts?: TextObject) => void;
}

const TextFieldSettings: React.FC<TextSettingsProps> = ({
  field,
  text,
  fieldIndex,
  disablePlaceHolder,
  updateFieldSettings,
  deleteField,
  setView,
  updateText,
}) => {
  return (
    <>
      <InputField
        label={<FormattedMessage defaultMessage="Label" id="753yX5" />}
        placeholder={<FormattedMessage defaultMessage="label" id="RYNL+m" />}
        value={text[field?.settings?.label?.textId!]}
        onChange={(event) =>
          updateText({
            [field?.settings?.label?.textId!]: event.target.value || "",
          })
        }
      />
      <InputField
        label={<FormattedMessage defaultMessage="Description" id="Q8Qw5B" />}
        placeholder={
          <FormattedMessage
            defaultMessage="Add description under the label"
            id="p7DADn"
          />
        }
        value={text[field?.settings?.description?.textId!]}
        onChange={(event) =>
          updateText({
            [field?.settings?.description?.textId!]: event.target.value || "",
          })
        }
      />
      {!disablePlaceHolder && (
        <InputField
          label={<FormattedMessage defaultMessage="Placeholder" id="h2T7yV" />}
          placeholder={
            <FormattedMessage
              defaultMessage="ex. Enter your full name"
              id="IT/AD2"
            />
          }
          value={text[field?.settings?.placeholder?.textId!]}
          onChange={(event) =>
            updateText({
              [field?.settings?.placeholder?.textId!]: event.target.value || "",
            })
          }
        />
      )}
      {field?.type === FieldType.Text && (
        <FormControl>
          <FormLabel htmlFor="auto-complete-select">
            <FormattedMessage defaultMessage="Auto-complete" id="uNhJX5" />
          </FormLabel>
          <Select
            id="auto-complete-select"
            value={createOption(field?.settings?.autoComplete)}
            placeholder={
              <FormattedMessage
                defaultMessage="Select auto-complete"
                id="UiSVbx"
              />
            }
            options={AUTOCOMPLETE_OPTIONS}
            onChange={(option) => {
              updateFieldSettings(
                {
                  ...field!,
                  settings: {
                    ...field?.settings,
                    autoComplete: option?.value,
                  },
                },
                fieldIndex
              );
            }}
          />
        </FormControl>
      )}
      <InputField
        label={<FormattedMessage defaultMessage="Error text" id="P5aj1m" />}
        placeholder={
          <FormattedMessage
            defaultMessage="ex. Please enter a valid value"
            id="bSc0ja"
          />
        }
        value={text[field?.settings?.errorMessage?.textId!]}
        onChange={(e) =>
          updateText({
            [field?.settings?.errorMessage?.textId!]: e.target.value || "",
          })
        }
      />
      <Divider />
      <FormControl
        display="flex"
        alignItems="center"
        justifyContent="space-between"
      >
        <FormLabel htmlFor="required-field-switch" margin="0px">
          <Text variant="textSm" fontWeight="medium" color="gray.800">
            <FormattedMessage defaultMessage="Required" id="Seanpx" />
          </Text>
        </FormLabel>
        <Switch
          size="md"
          id="required-field-switch"
          isChecked={field?.settings?.required}
          onChange={(e) =>
            updateFieldSettings(
              {
                ...field!,
                settings: {
                  ...field?.settings,
                  required: e.target.checked,
                },
              },
              fieldIndex
            )
          }
        />
      </FormControl>
      <Divider />
      <Button
        size="sm"
        width="full"
        padding="16px"
        borderRadius="8px"
        justifyContent="start"
        variant="errorTertiaryColor"
        leftIcon={<TrashIcon size="16px" />}
        onClick={() => {
          const deletedFieldTexts = getAllFieldTexts(field!);
          deletedFieldTexts?.forEach((textId) => delete text[textId]);
          deleteField(field?.id!, text);
          deleteField(field?.id!, text);
          setView(FormSettingsViews.Tabs);
        }}
      >
        <Text variant="textSm" fontWeight="semibold">
          <FormattedMessage defaultMessage="Delete field" id="s6O1xS" />
        </Text>
      </Button>
    </>
  );
};

export default TextFieldSettings;

/* Helpers */

function createOption(type: AutoCompleteType | undefined) {
  if (!type) return AUTOCOMPLETE_OPTIONS[0];
  return AUTOCOMPLETE_OPTIONS.find((i) => i.value === type);
}
