import React from "react";
import { FormattedMessage } from "react-intl";
import { ElementSettingsProps } from "../../Element/components/ElementSettings";
import ButtonSettingsTabs from "./ButtonSettingsTabs";
import { Text } from "@chakra-ui/react";
import { Popup } from "../../../components/Popup";

interface ButtonSettingsProps extends ElementSettingsProps {}

const ButtonSettings: React.FC<ButtonSettingsProps> = (props) => {
  return (
    <>
      <Popup.Header>
        <Text fontWeight="600" fontSize="16px" color="white">
          <FormattedMessage defaultMessage="Button Settings" id="7w7QNO" />
        </Text>
      </Popup.Header>
      <Popup.Body width="320px" padding="0">
        <ButtonSettingsTabs {...props} />
      </Popup.Body>
    </>
  );
};

export default ButtonSettings;
