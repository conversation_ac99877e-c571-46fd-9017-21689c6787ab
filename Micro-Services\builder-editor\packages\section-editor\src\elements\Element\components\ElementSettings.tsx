import { ButtonIcon, SettingsGeneralIcon, utils } from "@wuilt/quilt";
import React from "react";
import { FormattedMessage } from "react-intl";
import ButtonSettings from "../../Button/components/ButtonSettings";
import AppStoreButtonsSettings from "../../AppStoreButtons/components/AppStoreButtonsSettings";
import { ElementProps } from "../ElementEdit";
import ButtonsSettings from "../../Buttons/components/ButtonsSettings";
import VideoSettings from "../../Video/components/VideoSettings";
import styled from "styled-components";
import ImageSettings from "../../Image/components/ImageSettings";
import SocialLinksSettings from "../../SocialLinks/components/SocialLinksSettings";
import CounterSetting from "../../Counter/components/CounterSetting";
import { Popup } from "../../../components/Popup";
import { Text, Tooltip } from "@chakra-ui/react";

const Mapper = {
  Text: { Component: null, tooltip: null },
  Form: { Component: null, tooltip: null },
  Button: {
    Component: ButtonSettings,
    tooltip: <FormattedMessage defaultMessage="Button settings" id="7krETq" />,
  },
  Buttons: {
    Component: ButtonsSettings,
    tooltip: <FormattedMessage defaultMessage="Buttons settings" id="z1bRes" />,
  },
  AppStoreButtons: {
    Component: AppStoreButtonsSettings,
    tooltip: (
      <FormattedMessage
        defaultMessage="App store buttons settings"
        id="T86lvL"
      />
    ),
  },
  SocialLinks: {
    Component: SocialLinksSettings,
    tooltip: (
      <FormattedMessage defaultMessage="Social Links settings" id="AmvGpi" />
    ),
  },
  Image: {
    Component: ImageSettings,
    tooltip: <FormattedMessage defaultMessage="Image settings" id="VNUpYp" />,
  },
  Video: {
    Component: VideoSettings,
    tooltip: <FormattedMessage defaultMessage="Video settings" id="qFSzHO" />,
  },
  Counter: {
    Component: CounterSetting,
    tooltip: <FormattedMessage defaultMessage="Counter settings" id="JM7Prf" />,
  },
};

export interface ElementSettingsProps extends ElementProps {
  onClosePopup: () => void;
  openSettingsPopup?: boolean;
  setOpenSettingsPopup: React.Dispatch<React.SetStateAction<boolean>>;
}

const ElementSettings: React.FC<ElementSettingsProps> = ({
  element,
  pages,
  wuiltContext,
  updateElementUi,
  mutateElementApi,
  onClosePopup,
  onUploadImage,
  text,
  updateTextUi,
  updateTextApi,
  openSettingsPopup,
  setOpenSettingsPopup,
}) => {
  const { Component, tooltip } = Mapper[element?.type!];
  if (!Component) return null;
  return (
    <StyledIconWrapper data-test="ElementSettings">
      <Tooltip
        hasArrow
        placement="top"
        backgroundColor="gray.900"
        label={
          <Text variant="textXs" fontWeight="semibold" color="white">
            {tooltip}
          </Text>
        }
      >
        <ButtonIcon
          color="white"
          stopOpacity
          onClick={() => setOpenSettingsPopup(true)}
        >
          <SettingsGeneralIcon />
        </ButtonIcon>
      </Tooltip>
      <Popup
        open={openSettingsPopup}
        setOpen={setOpenSettingsPopup}
        onPopupClose={onClosePopup}
      >
        <Component
          element={element}
          pages={pages}
          wuiltContext={wuiltContext}
          onClosePopup={onClosePopup}
          updateElementUi={updateElementUi}
          mutateElementApi={mutateElementApi}
          onUploadImage={onUploadImage}
          text={text}
          updateTextApi={updateTextApi}
          updateTextUi={updateTextUi}
        />
      </Popup>
    </StyledIconWrapper>
  );
};

export default ElementSettings;

/**
 * Styles
 */

export const StyledIconWrapper = styled.div`
  cursor: pointer;
  padding-inline: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  color: ${utils.color("ink", "light")};
  font-size: 10px;

  ${({ theme }) =>
    `${
      theme.rtl
        ? "border-right: solid 1px #0e9384;"
        : "border-left: solid 1px #0e9384;"
    }`};

  &:nth-child(1) {
    border: none;
  }

  button,
  button:hover {
    padding: 0;
    background: none;
    border: none;
    box-shadow: none;
  }
`;
