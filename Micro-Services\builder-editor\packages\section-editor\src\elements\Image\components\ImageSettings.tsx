import React from "react";
import { FormattedMessage } from "react-intl";
import { SectionElement } from "@wuilt/section-preview";
import ImageSettingsTabs from "./ImageSettingsTabs";
import { Heading } from "@chakra-ui/react";
import { Popup } from "../../../components/Popup";

interface ImageSettingsProps {
  element: SectionElement;
  pages?: any[];
  desktopOnly?: boolean;
  updateElementUi: (v: SectionElement) => void;
  onUploadImage: (cb: (src: string) => void) => void;
}

const ImageSettings: React.FC<ImageSettingsProps> = (props) => {
  return (
    <>
      <Popup.Header>
        <Heading variant="h5">
          <FormattedMessage defaultMessage="Image Settings" id="Bl2raZ" />
        </Heading>
      </Popup.Header>
      <Popup.Body width="350px" pt="0">
        <ImageSettingsTabs {...props} />
      </Popup.Body>
    </>
  );
};

export default ImageSettings;
