import {
  CustomSection,
  SectionElement,
  FormSettings,
} from "@wuilt/section-preview";

type CustomSectionWithMeta = CustomSection & {
  metadata: Record<string, any>;
};

function getFormExtraData(element: SectionElement) {
  return {
    formId: (element?.settings as FormSettings)?.formId,
  };
}

const getExtraData = {
  Form: getFormExtraData,
};

export function addMetaData(data: CustomSection): CustomSectionWithMeta {
  const metadata = {};

  data.rows.forEach((row, rowIndex) =>
    row?.columns?.forEach((column, columnIndex) =>
      column.elements.forEach((element, elementIndex) => {
        const elementToAppend = {
          [element.id]: {
            ...getExtraData[element.type!]?.(element),
            id: element.id,
            location: {
              row: rowIndex,
              column: columnIndex,
              element: elementIndex,
            },
          },
        };

        if (!Object.hasOwn(metadata, element.type!)) {
          Object.assign(metadata, { [element.type!]: elementToAppend });
        } else {
          Object.assign(metadata[element.type!], elementToAppend);
        }
      })
    )
  );

  return { ...data, metadata };
}
