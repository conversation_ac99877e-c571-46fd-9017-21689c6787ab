'use client'

import { ChakraProvider, extendTheme } from '@chakra-ui/react'
import { LanguageProvider } from '@/lib/contexts/LanguageContext'

// Custom theme to match Old Builder's exact design system
const theme = extendTheme({
  config: {
    initialColorMode: 'light',
    useSystemColorMode: false,
  },
  direction: 'ltr', // Will be dynamically updated
  colors: {
    // Old Builder's exact color palette
    brand: {
      50: '#e8f4fd',
      100: '#bee1f9',
      200: '#91cdf5',
      300: '#64b8f1',
      400: '#42a9ee',
      500: '#1f9aeb', // Primary blue
      600: '#1c8cd4',
      700: '#177ab8',
      800: '#13689c',
      900: '#0d4a70',
    },
    // Old Builder specific colors
    oldBuilder: {
      primary: '#1f9aeb',      // Main blue
      secondary: '#6c757d',    // Gray
      success: '#28a745',      // Green
      warning: '#ffc107',      // Yellow
      danger: '#dc3545',       // Red
      light: '#f8f9fa',        // Light gray
      dark: '#343a40',         // Dark gray
      border: '#dfe3e8',       // Border color
      background: '#f9fafb',   // Background
      sidebar: '#ffffff',      // Sidebar background
    },
  },
  fonts: {
    heading: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    body: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
  },
  styles: {
    global: {
      body: {
        bg: '#f9fafb',
        color: '#343a40',
        fontFamily: 'body',
      },
    },
  },
  components: {
    Button: {
      defaultProps: {
        colorScheme: 'brand',
      },
      variants: {
        // Old Builder primary button style
        solid: {
          bg: 'oldBuilder.primary',
          color: 'white',
          borderRadius: '4px',
          fontWeight: '600',
          fontSize: 'sm',
          px: '16px',
          py: '8px',
          _hover: {
            bg: '#177ab8',
            boxShadow: 'none',
          },
          _active: {
            bg: '#13689c',
          },
        },
        // Old Builder secondary button style
        outline: {
          borderColor: 'oldBuilder.border',
          color: 'oldBuilder.dark',
          bg: 'white',
          borderRadius: '4px',
          fontWeight: '600',
          fontSize: 'sm',
          px: '16px',
          py: '8px',
          _hover: {
            bg: 'oldBuilder.light',
            borderColor: 'oldBuilder.primary',
            color: 'oldBuilder.primary',
          },
        },
        // Old Builder ghost button style
        ghost: {
          color: 'oldBuilder.secondary',
          bg: 'transparent',
          borderRadius: '4px',
          fontWeight: '500',
          fontSize: 'sm',
          px: '12px',
          py: '6px',
          _hover: {
            bg: 'oldBuilder.light',
            color: 'oldBuilder.dark',
          },
        },
      },
    },
    Input: {
      defaultProps: {
        focusBorderColor: 'oldBuilder.primary',
      },
      variants: {
        outline: {
          field: {
            borderColor: 'oldBuilder.border',
            borderRadius: '4px',
            _hover: {
              borderColor: 'oldBuilder.primary',
            },
            _focus: {
              borderColor: 'oldBuilder.primary',
              boxShadow: '0 0 0 1px #1f9aeb',
            },
          },
        },
      },
    },
    Select: {
      defaultProps: {
        focusBorderColor: 'oldBuilder.primary',
      },
    },
    Textarea: {
      defaultProps: {
        focusBorderColor: 'oldBuilder.primary',
      },
    },
  },
})

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <LanguageProvider>
      <ChakraProvider theme={theme}>
        {children}
      </ChakraProvider>
    </LanguageProvider>
  )
}
