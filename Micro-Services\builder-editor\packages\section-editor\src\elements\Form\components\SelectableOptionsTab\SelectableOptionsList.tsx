import { nanoid } from "nanoid";
import React from "react";
import { FormattedMessage } from "react-intl";
import _cloneDeep from "lodash/cloneDeep";
import { OptionType, TextObject } from "@wuilt/section-preview";
import { But<PERSON>, HStack, IconButton, Text, VStack } from "@chakra-ui/react";
import { DragHandle, VerticalSort } from "../../../../components/DragAndDrop";
import { AddIcon, TrashIcon } from "@wuilt/react-icons";
import InputField from "../../../../components/InputField";

interface SelectableOptionsListProps {
  text: TextObject;
  options: OptionType[];
  updateOptions: (options: OptionType[], newTexts?: TextObject) => void;
  updateText?: (newTexts?: TextObject) => void;
}

const SelectableOptionsList: React.FC<SelectableOptionsListProps> = ({
  text,
  options,
  updateOptions,
  updateText,
}) => {
  const updateOptionValue = (value: string, optionIndex: number) => {
    const cloned = _cloneDeep(options);
    updateText?.({
      [cloned[optionIndex].label?.textId!]: value,
    });
  };

  const addNewOption = () => {
    if (options.some((o) => !text[o.label?.textId!])) return;
    const newOptionTextId = `Content:${nanoid()}`;
    updateOptions(
      [
        ...options,
        {
          id: `SelectableOption:${nanoid()}`,
          label: { textId: newOptionTextId, tag: "p" },
        },
      ],
      { [newOptionTextId]: "" }
    );
  };

  const removeOption = (option: OptionType) => {
    delete text[option?.label?.textId!];
    updateOptions(
      options?.filter((o) => o?.id !== option?.id),
      text
    );
  };

  return (
    <VStack gap="8px" paddingTop="8px">
      <VerticalSort
        useHandleOnly
        value={options || []}
        onChange={(sortedOptions) => updateOptions(sortedOptions)}
      >
        {({ item: option, index }) => (
          <HStack
            gap="6px"
            alignItems="center"
            className="dnd-item"
            backgroundColor="white"
            padding="8px 16px"
          >
            <DragHandle id={option?.id} />
            <InputField
              value={text[option?.label?.textId!] || ""}
              onChange={(event) => updateOptionValue(event.target.value, index)}
            />
            <IconButton
              size="sm"
              padding="8px"
              borderRadius="8px"
              // eslint-disable-next-line formatjs/no-literal-string-in-jsx
              aria-label="Delete"
              variant="errorTertiaryColor"
              isDisabled={options?.length === 1}
              onClick={() => removeOption(option)}
            >
              <TrashIcon size="20px" color="error.600" />
            </IconButton>
          </HStack>
        )}
      </VerticalSort>
      <Button
        size="sm"
        width="full"
        padding="16px"
        borderRadius="0px"
        justifyContent="start"
        variant="tertiaryColor"
        leftIcon={<AddIcon size="20px" />}
        onClick={addNewOption}
        isDisabled={options.some((option) => !text[option.label?.textId!])}
      >
        <Text variant="textSm" fontWeight="semibold">
          <FormattedMessage defaultMessage="Add option" id="rAMgEh" />
        </Text>
      </Button>
    </VStack>
  );
};

export default SelectableOptionsList;
