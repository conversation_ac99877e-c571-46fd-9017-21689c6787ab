import React from "react";
import ColumnAdvancedSettings from "../../Column/components/ColumnAdvancedSettings";
import { Row, Settings } from "@wuilt/section-preview";

interface RowAdvancedSettingsProps {
  settings: Settings | undefined;
  updateSettings: (v: Row["settings"]) => void;
}

const RowAdvancedSettings: React.FC<RowAdvancedSettingsProps> = ({
  settings,
  updateSettings,
}) => {
  return (
    <ColumnAdvancedSettings
      settings={settings}
      updateSettings={updateSettings}
    />
  );
};

export default RowAdvancedSettings;
