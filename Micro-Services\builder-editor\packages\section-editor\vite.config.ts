import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import dts from "vite-plugin-dts";
import packageJSON from "./package.json";

const externalPackages = [
  ...Object.keys(packageJSON.peerDependencies),
  ...Object.keys(packageJSON.dependencies),
  ...Object.keys(packageJSON.devDependencies),
];

function external(id: string) {
  return externalPackages.some((aPackage) => id.startsWith(aPackage));
}

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    // dts({
    //   insertTypesEntry: true,
    // }),
    react(),
  ],
  build: {
    target: "es2015",
    outDir: "build",
    sourcemap: true,
    lib: {
      entry: ["src/main.tsx"],
      name: "CustomSectionEditor",
      formats: ["es", "cjs"],
      fileName: (format, entryName) => `${entryName}.${format}.js`,
    },
    rollupOptions: {
      external,
      output: {
        globals: {
          react: "React",
          "react-dom": "ReactDOM",
        },
      },
    },
  },
});
