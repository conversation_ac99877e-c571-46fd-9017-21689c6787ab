import { Icon<PERSON>utton, Toolt<PERSON> } from "@chakra-ui/react";
import { ChevronSelectorHorizontalIcon } from "@wuilt/react-icons";
import React from "react";
import { FormattedMessage } from "react-intl";
import styled from "styled-components";

interface ResizeColumnButtonProps {
  disableResizing: boolean;
}

const ResizeColumnButton: React.FC<ResizeColumnButtonProps> = ({
  disableResizing,
}) => {
  if (disableResizing) return null;

  return (
    <Tooltip
      hasArrow
      placement="top"
      label={<FormattedMessage defaultMessage="Resize Column" id="0CYQ/y" />}
    >
      <StyledDragButtonIcon
        isRound
        variant="plain"
        background="white"
        shadow="sm"
        minW="24px"
        height="24px"
        _hover={{ background: "gray.100" }}
      >
        <ChevronSelectorHorizontalIcon size="16px" />
      </StyledDragButtonIcon>
    </Tooltip>
  );
};

export default ResizeColumnButton;

/**
 * Styles
 */

const StyledDragButtonIcon = styled(IconButton)`
  position: absolute !important;
  top: 0;
  padding: 4px;
  cursor: col-resize;
`;
