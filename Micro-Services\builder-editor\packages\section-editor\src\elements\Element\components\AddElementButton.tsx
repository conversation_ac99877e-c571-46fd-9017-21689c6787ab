import { Tooltip, ButtonIcon, Box, PlusCircleIcon } from "@wuilt/quilt";
import React from "react";
import { FormattedMessage } from "react-intl";
import styled from "styled-components";
import AddElementPopup from "./AddElementPopup";
import {
  SectionElement,
  TextObject,
  WuiltContext,
} from "@wuilt/section-preview";
import { Popup } from "../../../components/Popup";

interface AddElementButtonProps {
  wuiltContext?: WuiltContext;
  addElement: (newElement: SectionElement, newTexts: TextObject) => void;
}

const AddElementButton: React.FC<AddElementButtonProps> = ({
  wuiltContext,
  addElement,
}) => {
  return (
    <StyledWrapper data-test="AddElementButton">
      <Box
        position="absolute"
        borderBottom="3px dashed"
        borderColor="primary"
        width="100%"
      />
      <Popup
        activator={
          <span>
            <Tooltip
              content={
                <FormattedMessage defaultMessage="Add Element" id="Y/H8Vd" />
              }
            >
              <ButtonIcon rounded color="primary" stopOpacity>
                <PlusCircleIcon size="lg" />
              </ButtonIcon>
            </Tooltip>
          </span>
        }
      >
        {({ closePopup }) => (
          <AddElementPopup
            wuiltContext={wuiltContext}
            addElement={addElement}
            closePopup={closePopup}
          />
        )}
      </Popup>
    </StyledWrapper>
  );
};

export default AddElementButton;

/**
 * Styles
 */

const StyledWrapper = styled.div`
  display: flex;
  position: absolute;
  bottom: 0;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 100%;
  height: 10px;
  background: transparent;
  z-index: 5;
  transform: translateY(50%);

  * {
    display: none;
  }
  &:hover {
    * {
      display: block;
    }
  }
`;
