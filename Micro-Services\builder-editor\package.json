{"name": "builder-editor", "version": "1.0.0", "description": "Visual website editor frontend", "scripts": {"dev": "next dev -p 3003", "build": "next build", "start": "next start -p 3002", "lint": "next lint", "test": "vitest"}, "dependencies": {"@chakra-ui/react": "^2.8.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.45.4", "@types/uuid": "^10.0.0", "framer-motion": "^10.18.0", "next": "^14.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "uuid": "^11.1.0", "zod": "^3.22.4", "zustand": "^5.0.5"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-config-next": "^14.0.0", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.3.0", "vitest": "^1.0.0"}}