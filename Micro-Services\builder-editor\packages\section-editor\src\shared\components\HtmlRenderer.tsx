import React from "react";
import styled from "styled-components";

interface HtmlRendererProps {
  htmlText: string | undefined | null;
  color?: string;
}

const HtmlRenderer: React.FC<HtmlRendererProps> = ({ htmlText, color }) => {
  return (
    <StyledHtmlRenderer
      color={color}
      dangerouslySetInnerHTML={{
        __html: String(htmlText || ""),
      }}
    />
  );
};

export default HtmlRenderer;

const StyledHtmlRenderer = styled.div<{ color }>`
  p {
    span {
      color: ${({ color }) => color || "black"} !important;
    }
  }
`;
