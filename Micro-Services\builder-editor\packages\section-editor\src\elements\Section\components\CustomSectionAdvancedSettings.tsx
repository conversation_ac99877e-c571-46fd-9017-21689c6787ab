import React from "react";
import ColumnAdvancedSettings from "../../Column/components/ColumnAdvancedSettings";
import { Settings } from "@wuilt/section-preview";

interface CustomSectionAdvancedSettingsProps {
  settings: Settings | undefined;
  updateSettings: (v: Settings) => void;
}

const CustomSectionAdvancedSettings: React.FC<
  CustomSectionAdvancedSettingsProps
> = ({ settings, updateSettings }) => {
  return (
    <ColumnAdvancedSettings
      settings={settings}
      updateSettings={updateSettings}
    />
  );
};

export default CustomSectionAdvancedSettings;
