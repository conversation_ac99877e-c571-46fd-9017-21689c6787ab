{"name": "sites-serve", "version": "1.0.0", "description": "Website rendering and serving service for published sites", "main": "index.js", "scripts": {"dev": "next dev -p 3004", "build": "next build", "start": "next start -p 3004", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.2.29", "react": "^18.3.1", "react-dom": "^18.3.1", "@supabase/supabase-js": "^2.45.4", "@supabase/ssr": "^0.5.1", "mongodb": "^6.3.0", "aos": "^2.3.4", "framer-motion": "^11.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.2.29"}, "keywords": ["website", "rendering", "publishing", "nextjs", "ssr"], "author": "New Builder Team", "license": "MIT"}