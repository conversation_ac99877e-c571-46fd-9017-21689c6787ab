import React, { useState } from 'react';
import {
  Box,
  Button,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverBody,
  SimpleGrid,
  Text,
  Icon,
  VStack,
  HStack,
} from '@chakra-ui/react';
import { AddIcon } from '@chakra-ui/icons';
import { v4 as uuidv4 } from 'uuid';

interface AddElementPopoverProps {
  sectionData: any;
  onAddElement: (elementData: any) => void;
  gridCellWidth: number;
  rowGap: number;
}

const elementTypes = [
  {
    type: 'text',
    label: 'Text',
    icon: '📝',
    defaultProps: {
      content: 'Your text here',
      fontSize: '16px',
      color: '#000000',
      textAlign: 'left',
    },
    defaultLayout: {
      offset: { top: 0, left: 0 },
      size: { width: 6, height: 2 },
    },
  },
  {
    type: 'image',
    label: 'Image',
    icon: '🖼️',
    defaultProps: {
      src: 'https://via.placeholder.com/400x300',
      alt: 'Image',
      objectFit: 'cover',
    },
    defaultLayout: {
      offset: { top: 0, left: 0 },
      size: { width: 8, height: 6 },
    },
  },
  {
    type: 'button',
    label: 'Button',
    icon: '🔘',
    defaultProps: {
      text: 'Click me',
      backgroundColor: '#3182ce',
      color: '#ffffff',
      borderRadius: '4px',
      padding: '12px 24px',
    },
    defaultLayout: {
      offset: { top: 0, left: 0 },
      size: { width: 4, height: 2 },
    },
  },
  {
    type: 'video',
    label: 'Video',
    icon: '🎥',
    defaultProps: {
      src: '',
      autoplay: false,
      controls: true,
      loop: false,
    },
    defaultLayout: {
      offset: { top: 0, left: 0 },
      size: { width: 12, height: 8 },
    },
  },
  {
    type: 'form',
    label: 'Form',
    icon: '📋',
    defaultProps: {
      fields: [
        { type: 'text', label: 'Name', required: true },
        { type: 'email', label: 'Email', required: true },
        { type: 'textarea', label: 'Message', required: false },
      ],
    },
    defaultLayout: {
      offset: { top: 0, left: 0 },
      size: { width: 10, height: 12 },
    },
  },
  {
    type: 'map',
    label: 'Map',
    icon: '🗺️',
    defaultProps: {
      location: 'New York, NY',
      zoom: 12,
      mapType: 'roadmap',
    },
    defaultLayout: {
      offset: { top: 0, left: 0 },
      size: { width: 12, height: 8 },
    },
  },
  {
    type: 'social-links',
    label: 'Social Links',
    icon: '🔗',
    defaultProps: {
      platforms: [
        { name: 'facebook', url: '#', icon: 'facebook' },
        { name: 'twitter', url: '#', icon: 'twitter' },
        { name: 'instagram', url: '#', icon: 'instagram' },
      ],
      layout: 'horizontal',
    },
    defaultLayout: {
      offset: { top: 0, left: 0 },
      size: { width: 6, height: 2 },
    },
  },
  {
    type: 'icon',
    label: 'Icon',
    icon: '⭐',
    defaultProps: {
      iconName: 'star',
      size: '32px',
      color: '#3182ce',
    },
    defaultLayout: {
      offset: { top: 0, left: 0 },
      size: { width: 2, height: 2 },
    },
  },
];

export const AddElementPopover: React.FC<AddElementPopoverProps> = ({
  sectionData,
  onAddElement,
  gridCellWidth,
  rowGap,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleAddElement = (elementType: any) => {
    const newElement = {
      id: uuidv4(),
      type: elementType.type,
      props: { ...elementType.defaultProps },
      layout: {
        desktop: { ...elementType.defaultLayout },
        tablet: { ...elementType.defaultLayout },
        mobile: {
          offset: { top: 0, left: 0 },
          size: { width: 12, height: elementType.defaultLayout.size.height },
        },
      },
      styles: {},
      animations: [],
      interactions: [],
    };

    onAddElement(newElement);
    setIsOpen(false);
  };

  return (
    <Popover isOpen={isOpen} onClose={() => setIsOpen(false)} placement="bottom-start">
      <PopoverTrigger>
        <Button
          position="absolute"
          top="-50px"
          left="50%"
          transform="translateX(-50%)"
          size="sm"
          colorScheme="blue"
          leftIcon={<AddIcon />}
          onClick={() => setIsOpen(!isOpen)}
          zIndex={1001}
          boxShadow="md"
        >
          Add Element
        </Button>
      </PopoverTrigger>
      
      <PopoverContent width="400px" maxHeight="500px" overflowY="auto">
        <PopoverBody p={4}>
          <VStack spacing={4} align="stretch">
            <Text fontWeight="bold" fontSize="lg">
              Add Element
            </Text>
            
            <SimpleGrid columns={3} spacing={3}>
              {elementTypes.map((elementType) => (
                <Box
                  key={elementType.type}
                  p={3}
                  border="1px solid"
                  borderColor="gray.200"
                  borderRadius="md"
                  cursor="pointer"
                  textAlign="center"
                  transition="all 0.2s"
                  _hover={{
                    borderColor: 'blue.300',
                    bg: 'blue.50',
                    transform: 'translateY(-2px)',
                    boxShadow: 'md',
                  }}
                  onClick={() => handleAddElement(elementType)}
                >
                  <VStack spacing={2}>
                    <Text fontSize="2xl">{elementType.icon}</Text>
                    <Text fontSize="sm" fontWeight="medium">
                      {elementType.label}
                    </Text>
                  </VStack>
                </Box>
              ))}
            </SimpleGrid>
            
            <Box pt={2} borderTop="1px solid" borderColor="gray.100">
              <Text fontSize="xs" color="gray.500" textAlign="center">
                Click on any element type to add it to your section
              </Text>
            </Box>
          </VStack>
        </PopoverBody>
      </PopoverContent>
    </Popover>
  );
};
