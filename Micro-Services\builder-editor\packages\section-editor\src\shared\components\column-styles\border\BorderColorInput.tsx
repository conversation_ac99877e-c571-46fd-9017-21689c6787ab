import { Box, InputColor, Label } from "@wuilt/quilt";
import { FormattedMessage } from "react-intl";
import { updateBorderValue, BorderPosition } from "./helper";
import { Borders } from "@wuilt/section-preview";
interface BorderProps {
  borders: Borders | undefined;
  onChange: (v: Borders) => void;
  activeBorder: BorderPosition;
}

function BorderColorInput({ borders, onChange, activeBorder }: BorderProps) {
  return (
    <Box>
      <Label>
        <FormattedMessage defaultMessage="Border color" id="pTpuA9" />
      </Label>
      <InputColor
        value={borders?.borderColor?.[activeBorder] || "#000000"}
        onChange={(color) => {
          onChange({
            ...borders,
            isAllSides: activeBorder === BorderPosition.all,
            borderColor: {
              ...borders?.borderColor,
              ...updateBorderValue(activeBorder, color),
            },
          });
        }}
      />
    </Box>
  );
}

export default BorderColorInput;
