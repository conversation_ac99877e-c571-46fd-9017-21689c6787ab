import React from "react";
import ColumnBackground from "../../Column/components/ColumnBackground";
import { Row, Settings } from "@wuilt/section-preview";

interface RowBackgroundProps {
  settings: Settings | undefined;
  updateSettings: (v: Row["settings"]) => void;
  onUploadImage: (cb: (src: string) => void) => void;
}

const RowBackground: React.FC<RowBackgroundProps> = ({
  settings,
  updateSettings,
  onUploadImage,
}) => {
  return (
    <ColumnBackground
      settings={settings!}
      updateSettings={updateSettings}
      onUploadImage={onUploadImage}
      source="ROW"
    />
  );
};

export default RowBackground;
