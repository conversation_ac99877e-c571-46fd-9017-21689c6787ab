import {
  SelectionsLimitationType,
  FormField,
  TextObject,
} from "@wuilt/section-preview";
import React, { Dispatch, SetStateAction } from "react";
import { FormattedMessage } from "react-intl";
import { FormSettingsViews } from "../FormSettings";
import { getAllFieldTexts } from "../../../../shared/utils/getTexts";
import {
  FormLabel,
  Divider,
  InputGroup,
  InputRightElement,
  Text,
  Button,
  Switch,
  FormControl,
} from "@chakra-ui/react";
import NumberInput from "../../../../components/NumberInput";
import InputField from "../../../../components/InputField";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DropdownMenuIcon,
  TrashIcon,
} from "@wuilt/react-icons";
import Select from "../../../../components/Select";

const LimitationOptions = [
  {
    label: (
      <FormattedMessage defaultMessage="Unlimited selections" id="3NobGa" />
    ),
    value: SelectionsLimitationType.Unlimited,
  },
  {
    label: <FormattedMessage defaultMessage="Limited selections" id="B1b0iu" />,
    value: SelectionsLimitationType.Limited,
  },
];

interface SelectableFiledSettingsProps {
  field?: FormField;
  fieldIndex: number;
  isAppRtl?: boolean;
  disableMultipleSelections?: boolean;
  disablePlaceHolder?: boolean;
  updateFieldSettings: (newField: FormField, index: number) => void;
  deleteField: (id: string, deletedTexts: TextObject) => void;
  setView: Dispatch<SetStateAction<FormSettingsViews>>;
  text: TextObject;
  updateText: (newTexts?: TextObject) => void;
}

const SelectableFiledSettings: React.FC<SelectableFiledSettingsProps> = ({
  field,
  text,
  fieldIndex,
  isAppRtl,
  disableMultipleSelections,
  disablePlaceHolder,
  updateFieldSettings,
  deleteField,
  setView,
  updateText,
}) => {
  return (
    <>
      <InputField
        label={<FormattedMessage defaultMessage="Label" id="753yX5" />}
        placeholder={<FormattedMessage defaultMessage="label" id="RYNL+m" />}
        value={text[field?.settings?.label?.textId!]}
        onChange={(event) =>
          updateText({
            [field?.settings?.label?.textId!]: event.target.value || "",
          })
        }
      />

      <InputField
        label={<FormattedMessage defaultMessage="Description" id="Q8Qw5B" />}
        placeholder={
          <FormattedMessage
            defaultMessage="Add description under the label"
            id="p7DADn"
          />
        }
        value={text[field?.settings?.description?.textId!]}
        onChange={(event) =>
          updateText({
            [field?.settings?.description?.textId!]: event.target.value || "",
          })
        }
      />
      {!disablePlaceHolder && (
        <InputField
          label={<FormattedMessage defaultMessage="Placeholder" id="h2T7yV" />}
          placeholder={
            <FormattedMessage
              defaultMessage="ex. Select one option"
              id="mga5LK"
            />
          }
          value={text[field?.settings?.placeholder?.textId!]}
          onChange={(event) =>
            updateText({
              [field?.settings?.placeholder?.textId!]: event.target.value || "",
            })
          }
        />
      )}
      <Divider />
      <Button
        width="full"
        borderRadius="8px"
        variant="secondaryGray"
        leftIcon={<DropdownMenuIcon size="20px" color="gray.500" />}
        rightIcon={
          isAppRtl ? (
            <ChevronLeftIcon size="20px" color="gray.500" />
          ) : (
            <ChevronRightIcon size="20px" color="gray.500" />
          )
        }
        onClick={() => setView(FormSettingsViews.SelectableFieldOptions)}
      >
        <Text
          width="full"
          variant="textSm"
          fontWeight="medium"
          color="gray.800"
        >
          <FormattedMessage
            id="eMfnIB"
            defaultMessage="Options ({optionsCount})"
            values={{ optionsCount: field?.settings?.options?.length || 0 }}
          />
        </Text>
      </Button>
      {!disableMultipleSelections && (
        <>
          <FormControl
            display="flex"
            alignItems="center"
            justifyContent="space-between"
          >
            <FormLabel htmlFor="multiple-selections-switch" margin="0px">
              <Text variant="textSm" fontWeight="medium" color="gray.800">
                <FormattedMessage
                  defaultMessage="Multiple selections"
                  id="2mMyMR"
                />
              </Text>
            </FormLabel>
            <Switch
              size="md"
              id="multiple-selections-switch"
              isChecked={!!field?.settings?.isMultipleSelection}
              onChange={(event) =>
                updateFieldSettings(
                  {
                    ...field!,
                    settings: {
                      ...field?.settings,
                      isMultipleSelection: event.target.checked,
                    },
                  },
                  fieldIndex
                )
              }
            />
          </FormControl>
          {!!field?.settings?.isMultipleSelection && (
            <>
              <Select
                value={createOption(field?.settings?.selections?.type)}
                placeholder={
                  <FormattedMessage
                    defaultMessage="Select auto-complete"
                    id="UiSVbx"
                  />
                }
                options={LimitationOptions}
                onChange={(option) =>
                  updateFieldSettings(
                    {
                      ...field!,
                      settings: {
                        ...field?.settings,
                        selections: {
                          ...field?.settings?.selections,
                          type: option?.value,
                        },
                      },
                    },
                    fieldIndex
                  )
                }
              />
              {field?.settings?.selections?.type ===
                SelectionsLimitationType.Limited && (
                <InputGroup gap="8px" display="flex" alignItems="center">
                  <NumberInput
                    min={0}
                    width="80%"
                    max={field?.settings?.options?.length || 0}
                    value={field?.settings?.selections?.selectionsCount || 0}
                    onChange={(selectionsCount: any) =>
                      updateFieldSettings(
                        {
                          ...field!,
                          settings: {
                            ...field?.settings,
                            selections: {
                              ...field?.settings?.selections,
                              selectionsCount,
                            },
                          },
                        },
                        fieldIndex
                      )
                    }
                  />
                  <InputRightElement
                    width="20%"
                    height="full"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                  >
                    <Text variant="textSm" color="gray.500">
                      <FormattedMessage
                        id="ScYhNY"
                        defaultMessage="of {maxCount}"
                        values={{
                          maxCount: field?.settings?.options?.length || 1,
                        }}
                      />
                    </Text>
                  </InputRightElement>
                </InputGroup>
              )}
            </>
          )}
        </>
      )}
      <Divider />
      <InputField
        label={<FormattedMessage defaultMessage="Error text" id="P5aj1m" />}
        placeholder={
          <FormattedMessage
            defaultMessage="ex. Please enter a valid value"
            id="bSc0ja"
          />
        }
        value={text[field?.settings?.errorMessage?.textId!]}
        onChange={(event) =>
          updateText({
            [field?.settings?.errorMessage?.textId!]: event.target.value || "",
          })
        }
      />
      <Divider />
      <FormControl
        display="flex"
        alignItems="center"
        justifyContent="space-between"
      >
        <FormLabel htmlFor="required-field-switch" margin="0px">
          <Text variant="textSm" fontWeight="medium" color="gray.800">
            <FormattedMessage defaultMessage="Required" id="Seanpx" />
          </Text>
        </FormLabel>
        <Switch
          size="md"
          id="required-field-switch"
          isChecked={field?.settings?.required}
          onChange={(event) =>
            updateFieldSettings(
              {
                ...field!,
                settings: {
                  ...field?.settings,
                  required: event.target.checked,
                },
              },
              fieldIndex
            )
          }
        />
      </FormControl>
      <Divider />
      <Button
        size="sm"
        width="full"
        padding="16px"
        borderRadius="8px"
        justifyContent="start"
        variant="errorTertiaryColor"
        leftIcon={<TrashIcon size="16px" />}
        onClick={() => {
          const deletedFieldTexts = getAllFieldTexts(field!);
          deletedFieldTexts?.forEach((textId) => delete text[textId]);
          deleteField(field?.id!, text);
          deleteField(field?.id!, text);
          setView(FormSettingsViews.Tabs);
        }}
      >
        <Text variant="textSm" fontWeight="semibold">
          <FormattedMessage defaultMessage="Delete field" id="s6O1xS" />
        </Text>
      </Button>
    </>
  );
};

export default SelectableFiledSettings;

/**
 * Helpers
 */

function createOption(type: SelectionsLimitationType | undefined) {
  if (!type) return LimitationOptions[0];
  return LimitationOptions.find((option) => option.value === type);
}
