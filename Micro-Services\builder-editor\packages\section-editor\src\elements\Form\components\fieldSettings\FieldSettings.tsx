import React, { <PERSON><PERSON><PERSON>, SetStateAction } from "react";
import { FormSettingsViews } from "../FormSettings";
import TextFieldSettings from "./TextFieldSettings";
import SelectableFiledSettings from "./SelectableFiledSettings";
import {
  FieldType,
  FormField,
  TextObject,
  WuiltContext,
} from "@wuilt/section-preview";
import FileUploadFieldSettings from "./FileUploadFieldSettings";
import { VStack } from "@chakra-ui/react";

const FIELDS_MAPPER: any = {
  [FieldType.Text]: {
    component: TextFieldSettings,
  },
  [FieldType.Email]: {
    component: TextFieldSettings,
  },
  [FieldType.Phone]: {
    component: TextFieldSettings,
  },
  [FieldType.TextArea]: {
    component: TextFieldSettings,
  },
  [FieldType.Link]: {
    component: TextFieldSettings,
  },
  [FieldType.Date]: {
    component: TextFieldSettings,
  },
  [FieldType.Select]: {
    component: SelectableFiledSettings,
  },
  [FieldType.Checkbox]: {
    component: SelectableFiledSettings,
  },
  [FieldType.Radio]: {
    component: SelectableFiledSettings,
  },
  [FieldType.Time]: {
    component: TextFieldSettings,
  },
  [FieldType.File]: {
    component: FileUploadFieldSettings,
  },
};

interface FieldSettingsProps {
  field?: FormField;
  text: TextObject;
  fieldIndex: number;
  isAppRtl?: boolean;
  wuiltContext: WuiltContext | undefined;
  updateFieldSettings: (newField: FormField, index: number) => void;
  deleteField: (id: string, deletedTexts: TextObject) => void;
  setView: Dispatch<SetStateAction<FormSettingsViews>>;
  updateText?: (newTexts?: TextObject) => void;
}

const FieldSettings: React.FC<FieldSettingsProps> = ({
  field,
  text,
  isAppRtl,
  fieldIndex,
  wuiltContext,
  updateFieldSettings,
  deleteField,
  setView,
  updateText,
}) => {
  const Component = FIELDS_MAPPER[field?.type!]?.component;
  return (
    <VStack gap="12px" padding="16px">
      <Component
        field={field}
        fieldIndex={fieldIndex}
        isAppRtl={isAppRtl}
        updateFieldSettings={updateFieldSettings}
        deleteField={deleteField}
        setView={setView}
        disableMultipleSelections={field?.type === FieldType.Radio}
        updateText={updateText}
        text={text}
        wuiltContext={wuiltContext}
        disablePlaceHolder={
          field?.type === FieldType.Time ||
          field?.type === FieldType.Checkbox ||
          field?.type === FieldType.Radio
        }
      />
    </VStack>
  );
};

export default FieldSettings;
