import React from "react";
import trackEvent from "../../../shared/utils/trackEvent";
import { Button } from "@chakra-ui/react";
import { AddIcon } from "@wuilt/react-icons";
interface AddRowButtonProps {
  addRow: (location?: "before" | "after") => void;
}
const AddRowButtonV2: React.FC<AddRowButtonProps> = ({ addRow }) => {
  return (
    <Button
      size="none"
      paddingInline={0}
      variant="plain"
      onClick={() => {
        addRow("after");
        trackEvent("rowAdded");
      }}
    >
      <AddIcon color="white" size="16px" />
    </Button>
  );
};

export default AddRowButtonV2;
