import { ElementsViewer } from "@wuilt/section-preview";
import React from "react";
import { ElementProps } from "../Element";
import { TextEdit } from "../Text";
import { Box } from "@chakra-ui/react";

export interface ButtonEditProps extends ElementProps {}

const ButtonEdit: React.FC<ButtonEditProps> = ({
  element,
  wuiltContext,
  mutateElementApi,
  updateElementUi,
  text,
  updateTextApi,
}) => {
  return (
    <Box
      data-test="ButtonEdit"
      id={element?.id}
      p="4px 0"
      position="relative"
      zIndex={2}
    >
      <ElementsViewer.Button
        element={element}
        wuiltContext={wuiltContext}
        stopAction
      >
        <TextEdit
          element={element}
          mutateElementApi={mutateElementApi}
          updateElementUi={updateElementUi}
          simple
          outline
          text={text}
          updateTextApi={updateTextApi}
        />
      </ElementsViewer.Button>
    </Box>
  );
};

export { ButtonEdit };
