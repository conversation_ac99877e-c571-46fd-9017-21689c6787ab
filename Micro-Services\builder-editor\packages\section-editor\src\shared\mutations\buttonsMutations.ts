import {
  ButtonsSettings,
  SectionElement,
  TextObject,
} from "@wuilt/section-preview";
import _cloneDeep from "lodash/cloneDeep";
import { nanoid } from "nanoid";

export function updateButtonsSettingsMutation(
  element: SectionElement,
  newButton: SectionElement,
  index: number
) {
  const values = element?.settings as ButtonsSettings;
  const clonedButtons = _cloneDeep(values?.buttons || []);
  clonedButtons[index] = {
    ...newButton?.settings,
    id: newButton?.id,
  };
  return {
    ...element,
    settings: { ...element?.settings, buttons: clonedButtons },
  };
}

export function duplicateButtonsMutation(
  element: SectionElement,
  textObject: TextObject
) {
  const type = element?.type;
  const buttonsSettings = element?.settings as ButtonsSettings;

  let newTextIds: TextObject = {};
  const newElementSettings = {
    type,
    id: `Buttons:${nanoid()}`,
    settings: {
      ...buttonsSettings,
      buttons: buttonsSettings?.buttons?.map((b) => {
        const newTextId = `Content:${nanoid()}`;
        newTextIds = {
          ...textObject,
          ...newTextIds,
          [newTextId]: textObject[b?.textId!],
        };
        return {
          ...b,
          textId: newTextId,
          id: `Button:${nanoid()}`,
        };
      }),
    },
  };

  return {
    newElementSettings,
    newTextIds,
  };
}
