import React from "react";
import { FormattedMessage } from "react-intl";
import PaddingInput from "../../../shared/components/PaddingInput";
import { Settings, Row } from "@wuilt/section-preview";
import { Stack, Divider, Box, Text, Switch } from "@chakra-ui/react";

interface RowLayoutProps {
  settings: Settings;
  updateSettings: (v: Row["settings"]) => void;
}

const RowLayout: React.FC<RowLayoutProps> = ({ settings, updateSettings }) => {
  return (
    <Stack gap="16px" align="start">
      <Stack gap="16px" direction="row" align="start">
        <Switch
          mb="0"
          size="md"
          isChecked={!!settings?.layout?.fullWidth}
          onChange={(e: any) => {
            const fullWidth = e.target.checked;
            updateSettings({
              ...settings,
              layout: { ...settings?.layout, fullWidth },
            });
          }}
        />
        <Box>
          <Text color="black" fontWeight="700" fontSize="12px">
            <FormattedMessage
              defaultMessage="Stretch to full width"
              id="Nff2SU"
            />
          </Text>
          <Text color="gray.500" fontSize="14px" fontWeight="400">
            <FormattedMessage
              defaultMessage="Make the section take the full width of the screen"
              id="SWqI1L"
            />
          </Text>
        </Box>
      </Stack>
      <Divider />

      <PaddingInput
        value={settings?.layout?.padding}
        onChange={(padding) => {
          updateSettings({
            ...settings,
            layout: { ...settings?.layout, padding },
          });
        }}
      />
    </Stack>
  );
};

export default RowLayout;
