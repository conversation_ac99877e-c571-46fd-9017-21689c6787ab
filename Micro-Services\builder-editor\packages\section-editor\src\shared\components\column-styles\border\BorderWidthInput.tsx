import { Box, InputField, Label, Text } from "@wuilt/quilt";
import { FormattedMessage } from "react-intl";
import { updateBorderValue, BorderPosition } from "./helper";
import { Borders } from "@wuilt/section-preview";
interface BorderProps {
  borders: Borders | undefined;
  onChange: (v: Borders) => void;
  activeBorder: BorderPosition;
}

function BorderWidth({ borders, onChange, activeBorder }: BorderProps) {
  return (
    <Box>
      <Label>
        <FormattedMessage defaultMessage="Width" id="5IP7AP" />
      </Label>
      <InputField
        width="100%"
        prefix={<Text fontSize="medium" children="px" />}
        type="number"
        minValue={1}
        value={borders?.borderWidth?.[activeBorder] || 1}
        onChange={(value: any) => {
          onChange({
            ...borders,
            isAllSides: activeBorder === BorderPosition.all,
            borderWidth: {
              ...borders?.borderWidth,
              ...updateBorderValue(activeBorder, value),
            },
          });
        }}
      />
    </Box>
  );
}

export default BorderWidth;
