import {
  AlignRightIcon,
  AlignCenterIcon,
  AlignLeftIcon,
  AlignTopIcon,
  AlignVerticalCenterIcon,
  AlignBottomIcon,
  Box,
  Stack,
  Text,
  Label,
} from "@wuilt/quilt";
import { Alignment } from "@wuilt/section-preview";
import React from "react";
import { FormattedMessage } from "react-intl";
import styled, { css } from "styled-components";

const ALIGN_HORIZONTAL_OPTIONS_RTL = [
  { Icon: AlignRightIcon, value: "start" },
  { Icon: AlignCenterIcon, value: "center" },
  { Icon: AlignLeftIcon, value: "end" },
] as const;
const ALIGN_HORIZONTAL_OPTIONS_LTR = [
  { Icon: AlignLeftIcon, value: "start" },
  { Icon: AlignCenterIcon, value: "center" },
  { Icon: AlignRightIcon, value: "end" },
] as const;
const ALIGN_VERTICAL_OPTIONS = [
  { Icon: AlignTopIcon, value: "start" },
  { Icon: AlignVerticalCenterIcon, value: "center" },
  { Icon: AlignBottomIcon, value: "end" },
] as const;
const VALUE_INDEX = {
  start: 0,
  center: 1,
  end: 2,
};

interface AlignmentInputProps {
  isSiteRtl?: boolean;
  value: Alignment | undefined;
  onChange: (v: Alignment) => void;
}

const AlignmentInput: React.FC<AlignmentInputProps> = ({
  isSiteRtl,
  value: alignment = { horizontal: "start", vertical: "start" },
  onChange,
}) => {
  const ALIGN_HORIZONTAL_OPTIONS = isSiteRtl
    ? ALIGN_HORIZONTAL_OPTIONS_RTL
    : ALIGN_HORIZONTAL_OPTIONS_LTR;
  return (
    <Box>
      <Text fontWeight="medium">
        <FormattedMessage defaultMessage="CONTENT ALIGNMENT" id="2yGNAS" />
      </Text>
      <Stack direction="row">
        <Box flex="1">
          <Label>
            <FormattedMessage defaultMessage="Horizontal" id="NfU3/O" />
          </Label>
          <StyledRowLocal
            style={{
              direction: isSiteRtl ? "rtl" : "ltr",
            }}
          >
            {ALIGN_HORIZONTAL_OPTIONS.map(({ Icon, value }, index) => (
              <StyledIconWrapper
                key={index}
                isSelected={
                  VALUE_INDEX[alignment?.horizontal || "start"] === index
                }
                onClick={() => onChange({ ...alignment, horizontal: value })}
              >
                <Icon />
              </StyledIconWrapper>
            ))}
          </StyledRowLocal>
        </Box>
        <Box flex="1">
          <Label>
            <FormattedMessage defaultMessage="Vertical" id="cLrroF" />
          </Label>
          <StyledRowLocal>
            {ALIGN_VERTICAL_OPTIONS.map(({ Icon, value }, index) => (
              <StyledIconWrapper
                key={index}
                isSelected={
                  VALUE_INDEX[alignment?.vertical || "start"] === index
                }
                onClick={() => onChange({ ...alignment, vertical: value })}
              >
                <Icon />
              </StyledIconWrapper>
            ))}
          </StyledRowLocal>
        </Box>
      </Stack>
    </Box>
  );
};

export default AlignmentInput;

/**
 * Styles
 */

const StyledRowLocal = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: solid 1px #bac7d5;
  border-radius: 4px;
  height: 34px;
`;

const StyledIconWrapper = styled.div<{ isSelected: boolean }>`
  cursor: pointer;
  padding: 8px 0;
  width: calc(100% / 3);
  height: 100%;
  box-sizing: border-box;

  display: flex;
  align-items: center;
  justify-content: center;

  color: #5f738c;

  ${({ isSelected }) =>
    isSelected &&
    css`
      background-color: #0e9384;
      color: #fff;
    `}

  &:nth-child(2) {
    padding: 8px 0;
    border: solid 1px #bac7d5;
    border-top: none;
    border-bottom: none;
  }
`;
