import {
  SectionElement,
  FormSettings,
  FormField,
  WuiltContext,
  TextObject,
} from "@wuilt/section-preview";
import _cloneDeep from "lodash/cloneDeep";
import { nanoid } from "nanoid";
import { createNewFormTexts } from "../utils/getTexts";

export function updateFormSettingsMutation(
  element: SectionElement,
  newFormSettings: FormSettings
) {
  return {
    ...element,
    settings: { ...element?.settings, ...newFormSettings },
  };
}

// Fields Mutations
export function updateFieldSettingsMutation(
  element: SectionElement,
  updatedField: FormField,
  index: number
) {
  const values = element?.settings as FormSettings;
  const clonedFields = _cloneDeep(values?.fields || []);
  clonedFields[index] = {
    ...updatedField,
    settings: { ...updatedField?.settings },
  };
  const newFormSettings: FormSettings = {
    ...element?.settings,
    fields: clonedFields,
  };
  return updateFormSettingsMutation(element, newFormSettings);
}

export function addFieldMutation(element: SectionElement, newField: FormField) {
  const values = element?.settings as FormSettings;
  const clonedFields = _cloneDeep(values?.fields || []);
  const newFormSettings: FormSettings = {
    ...element?.settings,
    fields: [...clonedFields, newField],
  };
  return updateFormSettingsMutation(element, newFormSettings);
}

export function deleteFieldMutation(element: SectionElement, fieldId: string) {
  const values = element?.settings as FormSettings;
  const clonedFields = _cloneDeep(values?.fields || [])?.filter(
    (field) => field?.id !== fieldId
  );
  const newFormSettings: FormSettings = {
    ...element?.settings,
    fields: clonedFields,
  };
  return updateFormSettingsMutation(element, newFormSettings);
}

export function sortFieldsMutation(
  element: SectionElement,
  sortedFields: FormField[]
) {
  const newFormSettings: FormSettings = {
    ...element?.settings,
    fields: sortedFields,
  };
  return updateFormSettingsMutation(element, newFormSettings);
}

// Notifications Mutations
export function updateNotificationsEmailMutation(
  element: SectionElement,
  updatedEmail: string,
  index: number
) {
  const values = element?.settings as FormSettings;
  const clonedEmails = _cloneDeep(values?.notifications?.emails || []);
  clonedEmails[index] = updatedEmail;
  const newFormSettings: FormSettings = {
    ...element?.settings,
    notifications: {
      emails: clonedEmails,
    },
  };
  return updateFormSettingsMutation(element, newFormSettings);
}

export function addNotificationsEmailMutation(
  element: SectionElement,
  newEmail: string
) {
  const values = element?.settings as FormSettings;
  const clonedEmails = _cloneDeep(values?.notifications?.emails || []);
  const newFormSettings: FormSettings = {
    ...element?.settings,
    notifications: {
      emails: [...clonedEmails, newEmail],
    },
  };
  return updateFormSettingsMutation(element, newFormSettings);
}

export function deleteNotificationsEmailMutation(
  element: SectionElement,
  emailIndex: number
) {
  const values = element?.settings as FormSettings;
  const clonedEmails = _cloneDeep(values?.notifications?.emails || [])?.filter(
    (e, index) => index !== emailIndex
  );
  const newFormSettings: FormSettings = {
    ...element?.settings,
    notifications: {
      emails: clonedEmails,
    },
  };
  return updateFormSettingsMutation(element, newFormSettings);
}

export async function createFormApiMutation(
  wuiltContext?: WuiltContext,
  element?: SectionElement
) {
  if (!wuiltContext?.props?.createFormUrl) return;
  const formSettings = element?.settings as FormSettings;
  const res = await fetch(wuiltContext?.props?.createFormUrl, {
    credentials: "include",
    method: "post",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
    },

    body: JSON.stringify({
      form_id: formSettings?.formId,
      form_data: formSettings,
      send_to: formSettings?.notifications?.emails?.[0],
      page_id: wuiltContext?.props?.activePage?.id,
      site_section_id: wuiltContext?.props?.sectionId,
    }),
  });
  const data = await res.json();
  return data?.form_id as string;
}

export async function duplicateFormMutation(
  element: SectionElement,
  newElementTexts: TextObject,
  wuiltContext: WuiltContext
) {
  const type = element?.type;
  const formSettings = element?.settings as FormSettings;
  const formId = await createFormApiMutation(wuiltContext, element);
  const { newTextIds, textsIds, fields } = createNewFormTexts(
    newElementTexts,
    formSettings
  );

  const newElementSettings = {
    id: `Form:${nanoid()}`,
    type,
    settings: {
      ...formSettings,
      formId,
      formPostSubmit: {
        ...formSettings?.formPostSubmit,
        successMessage: {
          textId: textsIds?.newSuccessMessageTextId,
        },
        failureMessage: {
          textId: textsIds?.newFailureMessageTextId,
        },
      },
      formButton: {
        ...formSettings?.formButton,
        textId: textsIds?.newFormButtonTextId,
        id: `Button:${nanoid()}`,
      },
      fields,
    },
  };
  const newForm = { formId, elementId: newElementSettings?.id };
  return {
    newElementSettings,
    newTextIds,
    newForm,
  };
}

export function updateFormApiMutation(
  element?: SectionElement,
  wuiltContext?: WuiltContext
) {
  if (!wuiltContext?.props?.updateFormUrl) return;
  const formSettings = element?.settings as FormSettings;
  const { formId: form_id, ...form_data } = formSettings;
  fetch(wuiltContext?.props?.updateFormUrl, {
    credentials: "include",
    method: "post",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      forms: [
        {
          form_id,
          form_data,
          send_to: formSettings?.notifications?.emails?.[0],
          page_id: wuiltContext?.props?.activePage?.id,
          site_section_id: wuiltContext?.props?.sectionId,
        },
      ],
    }),
  });
}

export function deleteFormApiMutation(
  formsIds: string[],
  wuiltContext: WuiltContext
) {
  if (!wuiltContext?.props?.deleteFormUrl) return;

  fetch(wuiltContext?.props?.deleteFormUrl(), {
    credentials: "include",
    method: "post",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      form_ids: [...formsIds],
    }),
  });
}
