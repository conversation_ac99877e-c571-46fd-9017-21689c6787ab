import React, { useState } from "react";
import { ElementsViewer } from "@wuilt/section-preview";
import { ElementProps } from "../Element";
import styled, { css } from "styled-components";
import FormSettings from "./components/FormSettings";
import { FormattedMessage } from "react-intl";
import { SettingsGeneralIcon } from "@wuilt/react-icons";
import { Box, Button } from "@chakra-ui/react";

export interface FormEditProps extends ElementProps {
  applyHoverStyles?: boolean;
  dontCallUpdateApi?: boolean;
  customClassNames?: {
    fieldContainerClass?: string;
    textClass?: string;
    textareaClass?: string;
    actionContainerClass?: string;
    buttonClass?: string;
  };
}

const FormEdit: React.FC<FormEditProps> = ({
  applyHoverStyles,
  dontCallUpdateApi,
  element,
  wuiltContext,
  customClassNames,
  text,
  updateTextUi,
  ...restProps
}) => {
  const [isSettingsOpened, setIsSettingsOpened] = useState(false);

  const handleToggleSetting = () => {
    setIsSettingsOpened((prev) => !prev);
  };

  return (
    <Box id={element?.id} data-test="FormEdit" position="relative" p="4px 0">
      <StyledHoverBox applyHoverStyles={applyHoverStyles}>
        <StyledToggleSettings onClick={handleToggleSetting} />
        <Button
          leftIcon={<SettingsGeneralIcon size="16px" />}
          bg="white"
          color="black"
          borderRadius="36px"
          size="sm"
        >
          <FormattedMessage defaultMessage="Form Settings" id="3Dfbln" />
        </Button>
        <FormSettings
          element={element}
          wuiltContext={wuiltContext}
          isSettingsOpened={isSettingsOpened}
          setIsSettingsOpened={setIsSettingsOpened}
          text={text}
          updateTextUi={updateTextUi}
          dontCallUpdateApi={dontCallUpdateApi}
          {...restProps}
        />
      </StyledHoverBox>
      <ElementsViewer.Form
        element={element}
        wuiltContext={wuiltContext}
        customClassNames={customClassNames}
        text={text}
      />
    </Box>
  );
};

export default FormEdit;

/**
 * Styles
 */

const StyledToggleSettings = styled.div`
  inset: 0;
  z-index: 1;
  position: absolute;
  cursor: pointer;
`;

const StyledHoverBox = styled(Box)<{ applyHoverStyles: boolean }>`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;

  display: grid;
  place-items: center;

  cursor: pointer;

  > *,
  > button {
    display: none;
  }

  &:hover {
    background-color: #0000001f;

    ${({ applyHoverStyles }) =>
      applyHoverStyles &&
      css`
        outline: solid 1px #0e9384;
        outline-offset: 4px;
      `}

    > *, > button {
      display: flex;
    }
  }
`;
