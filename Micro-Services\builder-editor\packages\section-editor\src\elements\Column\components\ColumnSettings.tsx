import React from "react";
import { FormattedMessage } from "react-intl";
import ColumnAdvancedSettings from "./ColumnAdvancedSettings";
import ColumnBackground from "./ColumnBackground";
import ColumnLayout from "./ColumnLayout";
import ColumnStyle from "./ColumnStyle";
import { Column, Settings } from "@wuilt/section-preview";
import {
  Stack,
  Tab,
  Tab<PERSON>ist,
  TabPanel,
  TabPanels,
  Tabs,
} from "@chakra-ui/react";

enum ColumnSettingsTabs {
  "Layout",
  "Style",
  "Background",
  "Advanced",
}

const TABS = [
  {
    content: <FormattedMessage defaultMessage="Layout" id="RQ4EKT" />,
    id: ColumnSettingsTabs.Layout,
  },
  {
    content: <FormattedMessage defaultMessage="Style" id="7mL9QE" />,
    id: ColumnSettingsTabs.Style,
  },
  {
    content: <FormattedMessage defaultMessage="Background" id="XQZA8e" />,
    id: ColumnSettingsTabs.Background,
  },
  {
    content: <FormattedMessage defaultMessage="Advanced" id="3Rx6Qo" />,
    id: ColumnSettingsTabs.Advanced,
  },
];

interface ColumnSettingsProps {
  rowGap: number;
  settings: Settings;
  isSiteRtl: boolean;
  updateRowGap: (gap: number) => void;
  updateSettings: (v: Column["settings"]) => void;
  onUploadImage: (cb: (src: string) => void) => void;
}

const ColumnSettings: React.FC<ColumnSettingsProps> = ({
  rowGap,
  settings,
  isSiteRtl,
  updateRowGap,
  updateSettings,
  onUploadImage,
}) => {
  return (
    <Stack>
      <Tabs>
        <TabList>
          {TABS.map(({ content, id }) => {
            return <Tab key={id}>{content}</Tab>;
          })}
        </TabList>
        <TabPanels>
          <TabPanel paddingInline={0} paddingBottom={0}>
            <ColumnLayout
              rowGap={rowGap}
              isSiteRtl={isSiteRtl}
              updateRowGap={updateRowGap}
              settings={settings}
              updateSettings={updateSettings}
            />
          </TabPanel>
          <TabPanel paddingInline={0} paddingBottom={0}>
            <ColumnStyle settings={settings} updateSettings={updateSettings} />
          </TabPanel>
          <TabPanel paddingInline={0} paddingBottom={0}>
            <ColumnBackground
              settings={settings}
              updateSettings={updateSettings}
              onUploadImage={onUploadImage}
              source="COLUMN"
            />
          </TabPanel>
          <TabPanel paddingInline={0} paddingBottom={0}>
            <ColumnAdvancedSettings
              settings={settings}
              updateSettings={updateSettings}
            />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Stack>
  );
};

export default ColumnSettings;
