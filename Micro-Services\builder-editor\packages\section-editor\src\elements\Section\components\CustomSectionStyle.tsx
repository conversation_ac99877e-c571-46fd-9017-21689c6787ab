import React from "react";
import ColumnStyle from "../../Column/components/ColumnStyle";
import { Settings } from "@wuilt/section-preview";

interface CustomSectionStyleProps {
  settings: Settings | undefined;
  updateSettings: (v: Settings) => void;
}

const CustomSectionStyle: React.FC<CustomSectionStyleProps> = ({
  settings,
  updateSettings,
}) => {
  return <ColumnStyle settings={settings} updateSettings={updateSettings} />;
};

export default CustomSectionStyle;
