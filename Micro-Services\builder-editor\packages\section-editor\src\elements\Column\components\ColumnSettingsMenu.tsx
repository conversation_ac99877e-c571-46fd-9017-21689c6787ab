import React from "react";
import { FormattedMessage } from "react-intl";
import styled from "styled-components";
import ColumnActions from "./ColumnActions";
import ColumnSettings from "./ColumnSettings";
import { Drag<PERSON>andle } from "@wuilt/quilt";
import { Column, UpdateDataFunc } from "@wuilt/section-preview";
import { Popup } from "../../../components/Popup";
import { ExpandIcon, RowsIcon, SettingsGeneralIcon } from "@wuilt/react-icons";
import { IconButton, Tooltip } from "@chakra-ui/react";

interface ColumnSettingsMenuProps {
  rowGap: number;
  column: Column;
  disableAdding: boolean;
  disableDeleting: boolean;
  isEmpty: boolean;
  isAppRtl: boolean;
  isSiteRtl: boolean;
  updateRowGap: (gap: number) => void;
  duplicateColumn: () => void;
  deleteColumn: () => void;
  updateSettings: (newSettings: Column["settings"]) => void;
  onClosePopup: UpdateDataFunc;
  onUploadImage: (cb: (src: string) => void) => void;
}

const ColumnSettingsMenu: React.FC<ColumnSettingsMenuProps> = ({
  rowGap,
  column,
  disableAdding,
  disableDeleting,
  isEmpty,
  isAppRtl,
  isSiteRtl,
  updateRowGap,
  deleteColumn,
  duplicateColumn,
  updateSettings,
  onClosePopup,
  onUploadImage,
}) => {
  return (
    <StyledSettingsButtons
      className="ui"
      isAppRtl={isAppRtl}
      data-test="ColumnSettingsMenu"
    >
      <StyledIconWrapper>
        <RowsIcon size="16px" rotate={90} />
        <FormattedMessage defaultMessage="Column" id="4I730h" />
      </StyledIconWrapper>
      <StyledIconWrapper>
        <Tooltip
          placement="top"
          hasArrow
          label={<FormattedMessage defaultMessage="Move column" id="5svkjq" />}
        >
          <span>
            <DragHandle
              id={column?.id}
              DragIcon={<ExpandIcon size="16px" />}
              buttonIconProps={{
                color: "white",
                stopOpacity: true,
                size: "small",
              }}
            />
          </span>
        </Tooltip>
      </StyledIconWrapper>
      {!isEmpty && (
        <StyledIconWrapper>
          <Popup
            onPopupClose={onClosePopup}
            activator={
              <span>
                <Tooltip
                  placement="top"
                  hasArrow
                  label={
                    <FormattedMessage
                      defaultMessage="Column settings"
                      id="HuVqoY"
                    />
                  }
                >
                  <IconButton
                    aria-label="Row settings"
                    size="16px"
                    variant="plain"
                  >
                    <SettingsGeneralIcon size="16px" />
                  </IconButton>
                </Tooltip>
              </span>
            }
          >
            <Popup.Header>
              <FormattedMessage defaultMessage="Column Settings" id="iCwqHt" />
            </Popup.Header>
            <Popup.Body width="420px" pt="0">
              <ColumnSettings
                rowGap={rowGap}
                isSiteRtl={isSiteRtl}
                settings={column?.settings!}
                updateRowGap={updateRowGap}
                updateSettings={updateSettings}
                onUploadImage={onUploadImage}
              />
            </Popup.Body>
          </Popup>
        </StyledIconWrapper>
      )}
      <StyledIconWrapper>
        <Tooltip
          placement="top"
          hasArrow
          label={<FormattedMessage defaultMessage="Actions" id="wL7VAE" />}
        >
          <span onFocus={(e) => e.preventDefault()}>
            <ColumnActions
              duplicateColumn={duplicateColumn}
              deleteColumn={deleteColumn}
              disableAdding={disableAdding}
              disableDeleting={disableDeleting}
            />
          </span>
        </Tooltip>
      </StyledIconWrapper>
    </StyledSettingsButtons>
  );
};

export default ColumnSettingsMenu;

export const StyledSettingsButtons = styled.div<{ isAppRtl: boolean }>`
  background-color: white;
  border: solid 1px #ee46bc;
  border-end-end-radius: "4px";

  direction: ${({ isAppRtl }) => (isAppRtl ? "rtl" : "ltr")};
  position: absolute;
  left: ${({ isAppRtl }) => (isAppRtl ? "unset" : 0)};
  right: ${({ isAppRtl }) => (isAppRtl ? 0 : "unset")};
  top: 0px;
  display: flex;
  z-index: 101;
  height: 24px;
`;

const StyledIconWrapper = styled.div`
  cursor: pointer;
  padding-inline: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  color: "var(--chakra-colors-ink-light)";
  font-size: 10px;
  gap: 4px;
  height: 100%;

  ${({ theme }) =>
    `${
      theme.rtl
        ? "border-right: solid 1px #ee46bc;"
        : "border-left: solid 1px #ee46bc;"
    }`};

  &:nth-child(1) {
    background-color: #ee46bc;
    color: white;
  }

  button,
  button:hover {
    padding: 0;
    background: none;
    border: none;
    box-shadow: none;
  }
`;
