import React, { useState } from "react";
import { FormattedMessage } from "react-intl";
import {
  addFieldMutation,
  addNotificationsEmailMutation,
  deleteFieldMutation,
  deleteNotificationsEmailMutation,
  updateFormApiMutation,
  sortFieldsMutation,
  updateFieldSettingsMutation,
  updateFormSettingsMutation,
  updateNotificationsEmailMutation,
} from "../../../shared/mutations/formMutations";
import ButtonDesignSettings from "../../Button/components/ButtonDesign";
import AddField from "./fieldsListTab/AddField";
import FieldSettings from "./fieldSettings/FieldSettings";
import FieldsListTab from "./fieldsListTab/FieldsListTab";
import FormTab from "./formTab/FormTab";
import PostSubmit from "./formTab/PostSubmit";
import NotificationsTab from "./notificationsTab/NotificationsTab";
import SelectableOptionsList from "./SelectableOptionsTab/SelectableOptionsList";
import {
  SectionElement,
  <PERSON>ilt<PERSON>ontext,
  FormSettings as TFormSettings,
  ButtonSettings,
  FormPostSubmit,
  FormField,
  TextObject,
} from "@wuilt/section-preview";
import {
  Button,
  Heading,
  Stack,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
} from "@chakra-ui/react";
import { ChevronLeftIcon } from "@wuilt/react-icons";
import { useTheme } from "styled-components";
import { Popup } from "../../../components/Popup";

export enum FormSettingsViews {
  "Tabs",
  "FormButton",
  "PostSubmit",
  "FieldSettings",
  "AddField",
  "SelectableFieldOptions",
}

enum FormSettingsTabs {
  "Form",
  "Fields",
  "Notifications",
}

interface FormSettingsProps {
  element: SectionElement;
  wuiltContext: WuiltContext | undefined;
  isSettingsOpened: boolean;
  dontCallUpdateApi: boolean;
  setIsSettingsOpened: React.Dispatch<React.SetStateAction<boolean>>;
  updateElementUi: (v: SectionElement, newTexts?: TextObject) => void;
  mutateElementApi: (v: SectionElement, newTexts?: TextObject) => void;
  updateTextUi?: (newTexts?: TextObject | undefined) => void;
  text: TextObject;
}

const TABS = [
  {
    content: <FormattedMessage defaultMessage="Form" id="baRFiF" />,
    id: FormSettingsTabs.Form,
  },
  {
    content: <FormattedMessage defaultMessage="Fields" id="iJMNSQ" />,
    id: FormSettingsTabs.Fields,
  },
  {
    content: <FormattedMessage defaultMessage="Notifications" id="NAidKb" />,
    id: FormSettingsTabs.Notifications,
  },
];

const FormSettings: React.FC<FormSettingsProps> = ({
  element,
  wuiltContext,
  isSettingsOpened,
  dontCallUpdateApi,
  setIsSettingsOpened,
  updateElementUi,
  mutateElementApi,
  updateTextUi,
  text,
}) => {
  const siteId = wuiltContext?.props?.siteId;
  const isAppRtl = wuiltContext?.props?.appDirection === "rtl";
  const [selectedTab, setSelectedTab] = useState(TABS[0]);
  const [view, setView] = useState(FormSettingsViews.Tabs);
  const [fieldIndex, setFieldIndex] = useState(0);
  const formSettings = element?.settings as TFormSettings;
  const activeField = formSettings?.fields?.[fieldIndex];
  const rtlDir = useTheme()?.rtl;
  const updateSettings = (
    newFormSettings: TFormSettings,
    newTexts?: TextObject
  ) => {
    updateElementUi(
      updateFormSettingsMutation(element, {
        ...formSettings,
        ...newFormSettings,
      }),
      newTexts
    );
  };

  const updateFormButton = (newFormButton: Partial<ButtonSettings>) => {
    updateSettings({
      formButton: {
        ...formSettings?.formButton,
        ...newFormButton,
        id: formSettings?.formButton?.id!,
      },
    });
  };

  const updateFormPostSubmit = (newFormPostSubmit: FormPostSubmit) => {
    updateSettings({
      formPostSubmit: {
        ...formSettings?.formPostSubmit,
        ...newFormPostSubmit,
      },
    });
  };

  const updateFieldSettings = (
    newField: FormField,
    index: number,
    newTexts?: TextObject
  ) => {
    updateElementUi(
      updateFieldSettingsMutation(element, newField, index),
      newTexts
    );
  };

  const addField = (newField: FormField, newTexts: TextObject) => {
    updateElementUi(addFieldMutation(element, newField), newTexts);
  };

  const deleteField = (id: string, newTexts: TextObject) => {
    updateElementUi(deleteFieldMutation(element, id), newTexts);
  };

  const sortFields = (sortedFields: FormField[]) => {
    updateElementUi(sortFieldsMutation(element, sortedFields));
  };

  const updateFormNotificationsEmail = (
    updatedEmail: string,
    emailIndex: number
  ) => {
    updateElementUi(
      updateNotificationsEmailMutation(element, updatedEmail, emailIndex)
    );
  };

  const addFormNotificationsEmail = (newEmail: string) => {
    updateElementUi(addNotificationsEmailMutation(element, newEmail));
  };

  const deleteFormNotificationsEmail = (emailIndex: number) => {
    updateElementUi(deleteNotificationsEmailMutation(element, emailIndex));
  };

  const onPopupClose = () => {
    if (!dontCallUpdateApi) {
      updateFormApiMutation(element, wuiltContext);
    }
    mutateElementApi(element);
  };

  return (
    <Popup
      onPopupClose={onPopupClose}
      open={isSettingsOpened}
      setOpen={setIsSettingsOpened}
    >
      <Popup.Header
        headerContentControls={{
          justifyContent: `${
            view === FormSettingsViews.Tabs &&
            selectedTab.id === FormSettingsTabs.Fields
              ? "space-between"
              : "start"
          }`,
        }}
      >
        {view === FormSettingsViews.Tabs && (
          <Heading variant="h5">
            <FormattedMessage defaultMessage="Form settings" id="C7h90Y" />
          </Heading>
        )}
        {view !== FormSettingsViews.Tabs && (
          <Button
            variant="plain"
            size="sm"
            paddingInline={0}
            onClick={() =>
              setView(
                view === FormSettingsViews.SelectableFieldOptions
                  ? FormSettingsViews.FieldSettings
                  : FormSettingsViews.Tabs
              )
            }
            leftIcon={
              <ChevronLeftIcon
                size="16px"
                transform={rtlDir && "rotate(180deg)"}
              />
            }
          >
            <FormattedMessage defaultMessage="Back" id="cyR7Kh" />
          </Button>
        )}
        {view === FormSettingsViews.FormButton && (
          <Heading variant="h5" p="10px">
            <FormattedMessage defaultMessage="Form action button" id="4IT7Jk" />
          </Heading>
        )}
        {view === FormSettingsViews.PostSubmit && (
          <Heading variant="h5" p="10px">
            <FormattedMessage defaultMessage="Post-submit" id="qmnDsn" />
          </Heading>
        )}

        {view === FormSettingsViews.AddField && (
          <Heading variant="h5" p="10px">
            <FormattedMessage defaultMessage="Add Field" id="JypYwa" />
          </Heading>
        )}
        {view === FormSettingsViews.FieldSettings && (
          <Heading variant="h5" p="10px">
            {text[activeField?.settings?.label?.textId!] ||
              text[activeField?.settings?.placeholder?.textId!] ||
              ""}
          </Heading>
        )}
      </Popup.Header>
      <Popup.Body width="320px" padding="0">
        {view === FormSettingsViews.Tabs && (
          <Stack gap="16px">
            <Tabs
              isLazy
              isFitted
              defaultIndex={selectedTab.id}
              onChange={(index) => setSelectedTab(TABS[index])}
            >
              <TabList>
                {TABS.map(({ content, id }) => {
                  return (
                    <Tab key={id}>
                      <Text variant="textSm" fontWeight="semibold">
                        {content}
                      </Text>
                    </Tab>
                  );
                })}
              </TabList>
              <TabPanels padding="0px">
                <TabPanel padding="0px">
                  <FormTab
                    siteId={siteId}
                    isAppRtl={isAppRtl}
                    formSettings={formSettings}
                    setView={setView}
                    updateSettings={updateSettings}
                  />
                </TabPanel>
                <TabPanel padding="0px">
                  <FieldsListTab
                    text={text}
                    isAppRtl={isAppRtl}
                    fields={formSettings?.fields}
                    setView={setView}
                    setFieldIndex={setFieldIndex}
                    deleteField={deleteField}
                    sortFields={sortFields}
                  />
                </TabPanel>
                <TabPanel padding="0px">
                  <NotificationsTab
                    emails={formSettings?.notifications?.emails!}
                    updateEmail={updateFormNotificationsEmail}
                    addEmail={addFormNotificationsEmail}
                    deleteEmail={deleteFormNotificationsEmail}
                  />
                </TabPanel>
              </TabPanels>
            </Tabs>
          </Stack>
        )}
        {view === FormSettingsViews.FormButton && (
          <ButtonDesignSettings
            text={text}
            enableContentEdit
            design={formSettings?.formButton?.design}
            updateText={updateTextUi}
            buttonTextId={formSettings?.formButton?.textId}
            updateDesign={(design) => updateFormButton({ design })}
            wrapperStackProps={{ padding: "16px", gap: "12px" }}
          />
        )}
        {view === FormSettingsViews.PostSubmit && (
          <PostSubmit
            formSettings={formSettings}
            pages={wuiltContext?.props?.pages}
            updateFormPostSubmit={updateFormPostSubmit}
            text={text}
            updateText={updateTextUi}
          />
        )}
        {view === FormSettingsViews.AddField && (
          <AddField
            addField={addField}
            setView={setView}
            wuiltContext={wuiltContext}
          />
        )}
        {view === FormSettingsViews.FieldSettings && (
          <FieldSettings
            text={text}
            isAppRtl={isAppRtl}
            field={activeField}
            wuiltContext={wuiltContext}
            fieldIndex={fieldIndex}
            setView={setView}
            updateFieldSettings={updateFieldSettings}
            deleteField={deleteField}
            updateText={updateTextUi}
          />
        )}
        {view === FormSettingsViews.SelectableFieldOptions && (
          <SelectableOptionsList
            options={
              formSettings?.fields?.[fieldIndex]?.settings?.options || []
            }
            text={text}
            updateText={updateTextUi}
            updateOptions={(options, newTexts) => {
              updateFieldSettings(
                {
                  ...formSettings?.fields?.[fieldIndex]!,
                  settings: {
                    ...formSettings?.fields?.[fieldIndex]?.settings,
                    options,
                  },
                },
                fieldIndex,
                newTexts
              );
            }}
          />
        )}
      </Popup.Body>
    </Popup>
  );
};

export default FormSettings;
