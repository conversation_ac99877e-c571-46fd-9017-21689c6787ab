import {
  Box,
  Label,
  Stack,
  Tooltip,
  ButtonIcon,
  GarbageIcon,
  Button,
  UploadIcon,
} from "@wuilt/quilt";
import React, { ReactNode } from "react";
import { FormattedMessage } from "react-intl";

interface ImageUploadProps {
  label?: ReactNode;
  value: string | undefined | null;
  onChange: (v: string) => void;
  onUploadImage: (cb: (src: string) => void) => void;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  label,
  value = "",
  onChange,
  onUploadImage,
}) => {
  const backgroundImageStyles = value
    ? {
        backgroundImage: `url(${value})`,
        backgroundPosition: "center center",
        backgroundSize: "cover",
      }
    : {};

  return (
    <Box>
      {label && <Label>{label}</Label>}
      <Stack
        justify={value ? "start" : "center"}
        align={value ? "end" : "center"}
        width="100%"
        padding="6px"
        height="150px"
        borderRadius="6px"
        border="1px solid"
        borderColor="overlay"
        backgroundColor={{ cloud: "light" }}
        style={backgroundImageStyles}
        onClick={(event: any) => {
          event.stopPropagation();
          const callback = (src: string) => {
            onChange(src);
          };
          onUploadImage?.(callback);
        }}
      >
        {value ? (
          <Tooltip
            content={
              <FormattedMessage defaultMessage="Remove image" id="7vM5rK" />
            }
          >
            <ButtonIcon
              size="small"
              color="white"
              onClick={(event) => {
                event.stopPropagation();
                onChange("");
              }}
            >
              <GarbageIcon />
            </ButtonIcon>
          </Tooltip>
        ) : (
          <Button
            color="white"
            size="small"
            squared={!!value}
            prefixIcon={<UploadIcon />}
          >
            <FormattedMessage defaultMessage="Upload" id="p4N05H" />
          </Button>
        )}
      </Stack>
    </Box>
  );
};

export default ImageUpload;
