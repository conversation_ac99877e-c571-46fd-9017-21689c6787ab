import React, { Dispatch, SetStateAction } from "react";
import { FormattedMessage } from "react-intl";
import { FormSettingsViews } from "../FormSettings";
import { FIELDS_TYPES } from "./AddField";
import { FormField, TextObject } from "@wuilt/section-preview";
import {
  AddIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  TrashIcon,
} from "@wuilt/react-icons";
import { VStack, Text, Button, Flex, IconButton } from "@chakra-ui/react";
import { DragHandle, VerticalSort } from "../../../../components/DragAndDrop";
import { getAllFieldTexts } from "../../../../shared/utils/getTexts";
import trackEvent from "../../../../shared/utils/trackEvent";

interface FieldsListTabProps {
  text: TextObject;
  isAppRtl: boolean;
  fields?: FormField[];
  setView: Dispatch<SetStateAction<FormSettingsViews>>;
  setFieldIndex: React.Dispatch<React.SetStateAction<number>>;
  deleteField: (id: string, deletedTexts: TextObject) => void;
  sortFields: (sortedFields: FormField[]) => void;
}
const FieldsListTab: React.FC<FieldsListTabProps> = ({
  text,
  isAppRtl,
  fields,
  setView,
  setFieldIndex,
  deleteField,
  sortFields,
}) => {
  return (
    <VStack gap="0px">
      <VerticalSort
        useHandleOnly
        value={fields!}
        boxProps={{ width: "full" }}
        onChange={(sortedFields: any) => sortFields(sortedFields)}
      >
        {({ item: field, index }) => (
          <Button
            size="sm"
            width="full"
            padding="8px 16px"
            variant="linkGray"
            borderRadius="0px"
            borderBottom="1px solid"
            borderColor="gray.200"
            backgroundColor="white"
            justifyContent="space-between"
            leftIcon={
              <Flex gap="6px" alignItems="center">
                <DragHandle id={field?.id} />
                {
                  FIELDS_TYPES?.find(
                    (fieldType) => fieldType?.value === field?.type
                  )?.icon
                }
              </Flex>
            }
            rightIcon={
              <Flex gap="12px" alignItems="center">
                <IconButton
                  size="sm"
                  padding="8px"
                  borderRadius="8px"
                  // eslint-disable-next-line formatjs/no-literal-string-in-jsx
                  aria-label="Delete"
                  variant="errorTertiaryColor"
                  isDisabled={fields?.length === 1}
                  onClick={(event) => {
                    event.stopPropagation();
                    const deletedFieldTexts = getAllFieldTexts(field);
                    deletedFieldTexts?.forEach((textId) => delete text[textId]);
                    deleteField(field?.id!, text);
                    trackEvent("formFieldDeleted", {
                      "Field name": field?.type as string,
                    });
                  }}
                >
                  <TrashIcon size="20px" color="error.600" />
                </IconButton>
                {isAppRtl ? (
                  <ChevronLeftIcon size="20px" color="gray.500" />
                ) : (
                  <ChevronRightIcon size="20px" color="gray.500" />
                )}
              </Flex>
            }
            onClick={() => {
              setFieldIndex(index);
              setView(FormSettingsViews.FieldSettings);
            }}
          >
            <Text
              width="full"
              variant="textSm"
              fontWeight="medium"
              color="gray.700"
            >
              {text[field?.settings?.label?.textId!] ||
                text[field?.settings?.placeholder?.textId!] ||
                ""}
            </Text>
          </Button>
        )}
      </VerticalSort>
      <Button
        size="sm"
        width="full"
        padding="16px"
        borderRadius="0px"
        justifyContent="start"
        variant="tertiaryColor"
        leftIcon={<AddIcon size="20px" />}
        onClick={() => setView(FormSettingsViews.AddField)}
      >
        <Text variant="textSm" fontWeight="semibold">
          <FormattedMessage defaultMessage="Add field" id="eSYQTy" />
        </Text>
      </Button>
    </VStack>
  );
};

export default FieldsListTab;
