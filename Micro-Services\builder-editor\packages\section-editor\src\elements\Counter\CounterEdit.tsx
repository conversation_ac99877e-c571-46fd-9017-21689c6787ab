import React from "react";
import { ElementsViewer } from "@wuilt/section-preview";
import { ElementProps } from "../Element";
import { Box } from "@wuilt/quilt";

export interface CounterEditProps extends ElementProps {}
const CounterEdit: React.FC<CounterEditProps> = ({ element, wuiltContext }) => {
  return (
    <Box id={element?.id} dataTest="CounterEdit" p="4px 0">
      <ElementsViewer.Counter
        element={element}
        wuiltContext={wuiltContext}
      ></ElementsViewer.Counter>
    </Box>
  );
};

export default CounterEdit;
