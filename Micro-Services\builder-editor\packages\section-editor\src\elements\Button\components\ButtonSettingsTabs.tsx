import React from "react";
import { FormattedMessage } from "react-intl";
import ButtonAction from "./ButtonAction";
import ButtonDesign from "./ButtonDesign";
import {
  ButtonSettings,
  SectionElement,
  ButtonAction as ButtonActionType,
  ButtonDesign as ButtonDesignType,
  WuiltContext,
  TextObject,
} from "@wuilt/section-preview";
import {
  Tab,
  <PERSON>b<PERSON>ist,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
} from "@chakra-ui/react";

enum ButtonSettingsViews {
  "Action",
  "Design",
}

const TABS = [
  {
    content: <FormattedMessage defaultMessage="Design" id="zmjMfM" />,
    id: ButtonSettingsViews.Design,
  },
  {
    content: <FormattedMessage defaultMessage="Action" id="QlsDcr" />,
    id: ButtonSettingsViews.Action,
  },
];

interface ButtonSettingsTabsProps {
  text?: TextObject;
  element: SectionElement;
  pages?: any[];
  wuiltContext?: WuiltContext;
  updateTextUi?: (newTexts?: TextObject) => void;
  updateElementUi: (v: SectionElement) => void;
  mutateElementApi: (v: SectionElement) => void;
}

const ButtonSettingsTabs: React.FC<ButtonSettingsTabsProps> = ({
  text,
  element,
  pages,
  wuiltContext,
  updateTextUi,
  updateElementUi,
}) => {
  const settings = element?.settings as ButtonSettings;

  const updateAction = (action: ButtonActionType) => {
    updateElementUi({
      ...element,
      settings: { ...element?.settings, id: element?.id, action },
    });
  };

  const updateDesign = (design: ButtonDesignType) => {
    updateElementUi({ ...element, settings: { ...element?.settings, design } });
  };

  return (
    <Tabs isFitted>
      <TabList>
        {TABS.map(({ content, id }) => {
          return (
            <Tab key={id}>
              <Text variant="textSm" fontWeight="semibold">
                {content}
              </Text>
            </Tab>
          );
        })}
      </TabList>
      <TabPanels padding="16px">
        <TabPanel padding="0px">
          <ButtonDesign
            text={text}
            design={settings?.design}
            buttonTextId={settings?.textId}
            wuiltContext={wuiltContext}
            updateText={updateTextUi}
            updateDesign={updateDesign}
            wrapperStackProps={{ gap: "16px" }}
          />
        </TabPanel>
        <TabPanel padding="0px">
          <ButtonAction
            action={settings?.action}
            pages={pages}
            updateAction={updateAction}
          />
        </TabPanel>
      </TabPanels>
    </Tabs>
  );
};

export default ButtonSettingsTabs;
