import React from "react";
import { SelectTab, SelectTabs, Stack } from "@wuilt/quilt";
import { FormattedMessage } from "react-intl";
import ShadowInputs from "./ShadowInputs";
import { ShadowType, Shadow } from "@wuilt/section-preview";

interface ShadowTabProps {
  shadow?: Shadow;
  updateShadow: (shadow: Shadow) => void;
}
const ShadowTab: React.FC<ShadowTabProps> = ({ updateShadow, shadow }) => {
  return (
    <Stack width="100%">
      <SelectTabs
        value={shadow?.type || ShadowType.none}
        name="shadow"
        onChange={(type) => {
          updateShadow({
            ...shadow,
            type,
          });
        }}
      >
        <SelectTab
          label={<FormattedMessage defaultMessage="None" id="450Fty" />}
          value={ShadowType.none}
        />
        <SelectTab
          label={<FormattedMessage defaultMessage="Outer" id="h31MfG" />}
          value={ShadowType.outer}
        />
        <SelectTab
          label={<FormattedMessage defaultMessage="Inner" id="1OyUdA" />}
          value={ShadowType.inner}
        />
      </SelectTabs>

      {shadow?.type === ShadowType.outer && (
        <ShadowInputs
          values={shadow?.outerShadow!}
          onChange={(outerShadow) => updateShadow({ ...shadow, outerShadow })}
        />
      )}
      {shadow?.type === ShadowType.inner && (
        <ShadowInputs
          values={shadow?.innerShadow!}
          onChange={(innerShadow) => updateShadow({ ...shadow, innerShadow })}
        />
      )}
    </Stack>
  );
};

export default ShadowTab;
