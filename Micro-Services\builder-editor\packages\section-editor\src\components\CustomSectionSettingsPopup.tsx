import React from "react";
import { Heading } from "@wuilt/quilt";
import { FormattedMessage } from "react-intl";
import {
  UpdateDataArgs,
  CustomSection,
  UpdateDataFunc,
} from "@wuilt/section-preview";
import CustomSectionSettings from "../elements/Section/components/CustomSectionSettings";
import { Popup } from "./Popup";

async function getUpdatedData(
  callback: UpdateDataArgs | undefined,
  data: CustomSection
) {
  return typeof callback === "undefined"
    ? data
    : typeof callback === "function"
    ? await callback(data)
    : callback;
}

interface CustomSectionSettingsPopupProps {
  section: CustomSection;
  activator: React.ReactElement;
  updateUi: (args: CustomSection) => void;
  mutateApi: (args: CustomSection) => void;
  onUploadImage: (cb: (src: string) => void) => void;
}

const CustomSectionSettingsPopup: React.FC<CustomSectionSettingsPopupProps> = ({
  section,
  activator,
  updateUi: updateUiProp,
  mutateApi: mutateApiProp,
  onUploadImage: onUploadImageProp,
}) => {
  const updateUi: UpdateDataFunc = async (callback) => {
    const updatedData = await getUpdatedData(callback, section);
    updateUiProp?.(updatedData);
  };

  const mutateApi: UpdateDataFunc = async (callback) => {
    const updatedData = await getUpdatedData(callback, section);
    mutateApiProp?.(updatedData);
  };

  const onUploadImage = (cb: (src: string) => void) => {
    onUploadImageProp?.(cb);
  };

  return (
    <Popup onPopupClose={() => mutateApi(section)} activator={activator}>
      <Popup.Header>
        <Heading color="white">
          <FormattedMessage defaultMessage="Section Settings" id="FX+5jN" />
        </Heading>
      </Popup.Header>
      <Popup.Body width="420px" pt="0">
        <CustomSectionSettings
          section={section}
          updateUi={updateUi}
          mutateApi={mutateApi}
          onUploadImage={onUploadImage}
        />
      </Popup.Body>
    </Popup>
  );
};

export { CustomSectionSettingsPopup };
