import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>,
} from "@chakra-ui/react";
import { FormattedMessage } from "react-intl";
import CustomSectionAdvancedSettings from "./CustomSectionAdvancedSettings";
import CustomSectionBackground from "./CustomSectionBackground";
import CustomSectionLayout from "./CustomSectionLayout";
import CustomSectionStyle from "./CustomSectionStyle";
import {
  CustomSection,
  UpdateDataFunc,
  Settings,
} from "@wuilt/section-preview";

enum SectionSettingsTabs {
  "Layout",
  "Style",
  "Background",
  "Advanced",
}

const TABS = [
  {
    content: <FormattedMessage defaultMessage="Layout" id="RQ4EKT" />,
    id: SectionSettingsTabs.Layout,
  },
  {
    content: <FormattedMessage defaultMessage="Style" id="7mL9QE" />,
    id: SectionSettingsTabs.Style,
  },
  {
    content: <FormattedMessage defaultMessage="Background" id="XQZA8e" />,
    id: SectionSettingsTabs.Background,
  },
  {
    content: <FormattedMessage defaultMessage="Advanced" id="3Rx6Qo" />,
    id: SectionSettingsTabs.Advanced,
  },
];

interface CustomSectionSettingsProps {
  section: CustomSection;
  updateUi: UpdateDataFunc;
  mutateApi: UpdateDataFunc;
  onUploadImage: (cb: (src: string) => void) => void;
}

const CustomSectionSettings: React.FC<CustomSectionSettingsProps> = ({
  section,
  updateUi,
  onUploadImage,
}) => {
  const updateSettings = (newSettings: Settings) => {
    updateUi((prev) => {
      return { ...prev, settings: newSettings };
    });
  };

  return (
    <Stack gap="16px">
      <Tabs>
        <TabList>
          {TABS.map(({ content, id }) => {
            return <Tab key={id}>{content}</Tab>;
          })}
        </TabList>
        <TabPanels>
          <TabPanel paddingInline={0} paddingBottom={0}>
            <CustomSectionLayout
              layout={section?.settings?.layout}
              updateUi={updateUi}
            />
          </TabPanel>
          <TabPanel paddingInline={0} paddingBottom={0}>
            <CustomSectionStyle
              settings={section?.settings}
              updateSettings={updateSettings}
            />
          </TabPanel>
          <TabPanel paddingInline={0} paddingBottom={0}>
            <CustomSectionBackground
              settings={section?.settings}
              updateSettings={updateSettings}
              onUploadImage={onUploadImage}
            />
          </TabPanel>
          <TabPanel paddingInline={0} paddingBottom={0}>
            <CustomSectionAdvancedSettings
              settings={section?.settings}
              updateSettings={updateSettings}
            />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Stack>
  );
};

export default CustomSectionSettings;
