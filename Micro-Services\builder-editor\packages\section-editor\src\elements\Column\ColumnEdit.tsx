import React, { useRef } from "react";
import styled from "styled-components";
import {
  ElementsViewer,
  Column,
  WuiltContext,
  UpdateDataFunc,
  SectionElement,
  TextObject,
} from "@wuilt/section-preview";
import AddResizeColumnButtons from "./components/AddResizeColumnButtons";
import ColumnSettingsMenu, {
  StyledSettingsButtons,
} from "./components/ColumnSettingsMenu";
import EmptyColumn from "./components/EmptyColumn";
import {
  addColumnMutation,
  deleteColumnMutation,
  duplicateColumnMutation,
  updateColumnMutation,
  deleteRowMutation,
  updateRowGapMutation,
  addElementMutation,
  updateRowGridTemplatesMutation,
} from "../../shared/mutations";
import { checkAvailability } from "../../shared/utils/checkAvailability";
export interface ColumnEditProps {
  column: Column;
  rowIndex: number;
  rowGap: number;
  columnIndex: number;
  children: React.ReactNode;
  disableAdding: boolean;
  isSingleRow: boolean;
  isSingleColumn: boolean;
  isLastColumn: boolean;
  wuiltContext?: WuiltContext;
  rowGridTemplates?: number[];
  rowFullWidth?: boolean;
  updateUi: UpdateDataFunc;
  mutateApi: UpdateDataFunc;
  updateAndMutate: UpdateDataFunc;
  onUploadImage: (cb: (src: string) => void) => void;
}

const ColumnEdit: React.FC<ColumnEditProps> = ({
  column,
  rowIndex,
  rowGap,
  columnIndex,
  children,
  disableAdding,
  isSingleRow,
  isSingleColumn,
  isLastColumn,
  wuiltContext,
  rowGridTemplates,
  updateUi,
  mutateApi,
  updateAndMutate,
  onUploadImage,
}) => {
  const resizableElementRef = useRef<HTMLDivElement>(null);
  const isNotAvailable = checkAvailability(wuiltContext);

  const isAppRtl = wuiltContext?.props?.appDirection === "rtl";
  const isSiteRtl = wuiltContext?.props?.siteDirection === "rtl";

  const updateSettings = (newSettings: Column["settings"]) => {
    updateUi((prev) => {
      return updateColumnMutation(prev, rowIndex, columnIndex, newSettings);
    });
  };

  const updateRowGridTemplates = (gridTemplates: number[]) => {
    updateUi((prev) => {
      return updateRowGridTemplatesMutation(prev, rowIndex, gridTemplates);
    });
  };

  const updateRowGap = (gap: number) => {
    updateUi((prev) => {
      return updateRowGapMutation(prev, rowIndex, gap);
    });
  };

  const addColumn = (location?: "before" | "after") => {
    if (disableAdding) return;
    updateAndMutate((prev) => {
      return addColumnMutation(prev, rowIndex, columnIndex, location);
    });
  };

  const duplicateColumn = () => {
    if (disableAdding) return;
    updateAndMutate((prev) => {
      return duplicateColumnMutation(
        prev,
        rowIndex,
        columnIndex,
        false,
        wuiltContext
      );
    });
  };

  const deleteRow = () => {
    if (isSingleRow) return;
    updateAndMutate((prev) => {
      return deleteRowMutation(prev, rowIndex, wuiltContext);
    });
  };

  const deleteColumn = () => {
    if (isSingleColumn) return deleteRow();
    updateAndMutate((prev) => {
      return deleteColumnMutation(prev, rowIndex, columnIndex, wuiltContext);
    });
  };

  const addElement = (newElement: SectionElement, newTexts?: TextObject) => {
    updateAndMutate((prev) => {
      return addElementMutation(
        prev,
        rowIndex,
        columnIndex,
        newElement,
        0,
        newTexts
      );
    });
  };

  const isEmpty = !column?.elements?.length;

  return (
    <div
      id={column?.id}
      data-test="ColumnEdit"
      style={{
        position: "relative",
        height: "100%",
        pointerEvents: isNotAvailable ? "none" : "auto",
      }}
      className="dnd-item"
      ref={resizableElementRef}
    >
      {columnIndex === 0 && (
        <AddResizeColumnButtons
          siteDirection={wuiltContext?.props?.siteDirection!}
          disableAdding={disableAdding}
          disableResizing
          addColumnBefore
          addColumn={addColumn}
        />
      )}
      <StyledHoverBox>
        <ColumnSettingsMenu
          isEmpty={isEmpty}
          isAppRtl={isAppRtl}
          isSiteRtl={isSiteRtl}
          column={column}
          duplicateColumn={duplicateColumn}
          deleteColumn={deleteColumn}
          updateSettings={updateSettings}
          onClosePopup={() => mutateApi()}
          onUploadImage={onUploadImage}
          disableAdding={disableAdding}
          rowGap={rowGap}
          updateRowGap={updateRowGap}
          disableDeleting={isSingleRow && isSingleColumn}
        />
        {isEmpty ? (
          <EmptyColumn
            addElement={addElement}
            wuiltContext={wuiltContext}
            isSingleColumnEmpty={isSingleRow && isSingleColumn && isEmpty}
          />
        ) : (
          <ElementsViewer.Column column={column}>
            {children}
          </ElementsViewer.Column>
        )}
      </StyledHoverBox>
      <AddResizeColumnButtons
        disableAdding={disableAdding}
        addColumn={addColumn}
        columnIndex={columnIndex}
        rowGridTemplates={rowGridTemplates}
        disableResizing={isLastColumn}
        resizableElementRef={resizableElementRef}
        mutateApi={mutateApi}
        updateRowGridTemplates={updateRowGridTemplates}
        siteDirection={wuiltContext?.props?.siteDirection!}
      />
    </div>
  );
};

export { ColumnEdit };

/**
 * Styles
 */

const StyledHoverBox = styled.div`
  position: relative;
  height: 100%;

  ${StyledSettingsButtons} {
    display: flex;
    visibility: hidden;
  }

  &:hover {
    outline: solid 1px #ee46bc;
    z-index: 1;
    ${StyledSettingsButtons} {
      display: flex;
      visibility: visible;
    }
  }
`;
