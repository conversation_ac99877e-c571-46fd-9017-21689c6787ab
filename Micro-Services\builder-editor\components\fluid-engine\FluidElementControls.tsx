import React, { useState } from 'react';
import { <PERSON>, Icon<PERSON>utton, HStack, Tooltip } from '@chakra-ui/react';
import { DeleteIcon, CopyIcon, DragHandleIcon } from '@chakra-ui/icons';

interface FluidElementControlsProps {
  element: any;
  isSelected: boolean;
  onResize: (direction: string, delta: { x: number; y: number }) => void;
  onDelete: () => void;
  onDuplicate: () => void;
  setIsResizing: (resizing: boolean) => void;
}

export const FluidElementControls: React.FC<FluidElementControlsProps> = ({
  element,
  isSelected,
  onResize,
  onDelete,
  onDuplicate,
  setIsResizing,
}) => {
  const [resizeStart, setResizeStart] = useState<{
    direction: string;
    startX: number;
    startY: number;
  } | null>(null);

  const handleResizeStart = (direction: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setResizeStart({
      direction,
      startX: e.clientX,
      startY: e.clientY,
    });
    setIsResizing(true);
  };

  const handleResizeMove = (e: MouseEvent) => {
    if (!resizeStart) return;

    const deltaX = e.clientX - resizeStart.startX;
    const deltaY = e.clientY - resizeStart.startY;

    onResize(resizeStart.direction, { x: deltaX, y: deltaY });
  };

  const handleResizeEnd = () => {
    setResizeStart(null);
    setIsResizing(false);
  };

  React.useEffect(() => {
    if (resizeStart) {
      document.addEventListener('mousemove', handleResizeMove);
      document.addEventListener('mouseup', handleResizeEnd);
      return () => {
        document.removeEventListener('mousemove', handleResizeMove);
        document.removeEventListener('mouseup', handleResizeEnd);
      };
    }
  }, [resizeStart]);

  return (
    <>
      {/* Top Controls Bar */}
      {isSelected && (
        <Box
          position="absolute"
          top="-40px"
          left="0"
          right="0"
          height="32px"
          bg="white"
          border="1px solid"
          borderColor="gray.200"
          borderRadius="4px"
          boxShadow="sm"
          zIndex={1001}
        >
          <HStack spacing={1} height="100%" px={2}>
            <Tooltip label="Drag to move">
              <IconButton
                aria-label="Move element"
                icon={<DragHandleIcon />}
                size="xs"
                variant="ghost"
                cursor="grab"
              />
            </Tooltip>
            
            <Tooltip label="Duplicate element">
              <IconButton
                aria-label="Duplicate element"
                icon={<CopyIcon />}
                size="xs"
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  onDuplicate();
                }}
              />
            </Tooltip>
            
            <Tooltip label="Delete element">
              <IconButton
                aria-label="Delete element"
                icon={<DeleteIcon />}
                size="xs"
                variant="ghost"
                colorScheme="red"
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete();
                }}
              />
            </Tooltip>
          </HStack>
        </Box>
      )}

      {/* Resize Handles */}
      {isSelected && (
        <>
          {/* Corner Resize Handles */}
          <Box
            position="absolute"
            top="-4px"
            left="-4px"
            width="8px"
            height="8px"
            bg="blue.500"
            border="1px solid white"
            borderRadius="2px"
            cursor="nw-resize"
            zIndex={1002}
            onMouseDown={(e) => handleResizeStart('nw', e)}
          />
          
          <Box
            position="absolute"
            top="-4px"
            right="-4px"
            width="8px"
            height="8px"
            bg="blue.500"
            border="1px solid white"
            borderRadius="2px"
            cursor="ne-resize"
            zIndex={1002}
            onMouseDown={(e) => handleResizeStart('ne', e)}
          />
          
          <Box
            position="absolute"
            bottom="-4px"
            left="-4px"
            width="8px"
            height="8px"
            bg="blue.500"
            border="1px solid white"
            borderRadius="2px"
            cursor="sw-resize"
            zIndex={1002}
            onMouseDown={(e) => handleResizeStart('sw', e)}
          />
          
          <Box
            position="absolute"
            bottom="-4px"
            right="-4px"
            width="8px"
            height="8px"
            bg="blue.500"
            border="1px solid white"
            borderRadius="2px"
            cursor="se-resize"
            zIndex={1002}
            onMouseDown={(e) => handleResizeStart('se', e)}
          />

          {/* Edge Resize Handles */}
          <Box
            position="absolute"
            top="-4px"
            left="50%"
            transform="translateX(-50%)"
            width="8px"
            height="8px"
            bg="blue.500"
            border="1px solid white"
            borderRadius="2px"
            cursor="n-resize"
            zIndex={1002}
            onMouseDown={(e) => handleResizeStart('n', e)}
          />
          
          <Box
            position="absolute"
            bottom="-4px"
            left="50%"
            transform="translateX(-50%)"
            width="8px"
            height="8px"
            bg="blue.500"
            border="1px solid white"
            borderRadius="2px"
            cursor="s-resize"
            zIndex={1002}
            onMouseDown={(e) => handleResizeStart('s', e)}
          />
          
          <Box
            position="absolute"
            left="-4px"
            top="50%"
            transform="translateY(-50%)"
            width="8px"
            height="8px"
            bg="blue.500"
            border="1px solid white"
            borderRadius="2px"
            cursor="w-resize"
            zIndex={1002}
            onMouseDown={(e) => handleResizeStart('w', e)}
          />
          
          <Box
            position="absolute"
            right="-4px"
            top="50%"
            transform="translateY(-50%)"
            width="8px"
            height="8px"
            bg="blue.500"
            border="1px solid white"
            borderRadius="2px"
            cursor="e-resize"
            zIndex={1002}
            onMouseDown={(e) => handleResizeStart('e', e)}
          />
        </>
      )}
    </>
  );
};
