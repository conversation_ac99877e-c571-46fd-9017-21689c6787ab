'use client'

import { useState } from 'react'
import { Box, Flex } from '@chakra-ui/react'
import { EditorTopBar } from './EditorTopBar'
import { EditorCanvas } from './EditorCanvas'
import { EditorRightPanel } from './EditorRightPanel'
import { OldBuilderSidebar } from '../Sidebar/OldBuilderSidebar'
import { useEditorStore } from '@/lib/stores/editorStore'

// Import global components
import KeyboardShortcuts from '../KeyboardShortcuts/KeyboardShortcuts'
import AutoSave from '../AutoSave/AutoSave'

export function EditorLayout() {
  const [leftSidebarCollapsed, setLeftSidebarCollapsed] = useState(false)
  const [rightPanelCollapsed, setRightPanelCollapsed] = useState(false)

  // Old Builder exact colors
  const bgColor = '#f9fafb'
  const { isPreviewMode } = useEditorStore()

  // Editor layout style matching Old Builder exactly
  const editorStyle: React.CSSProperties = {
    height: '100vh',
    position: 'absolute',
    userSelect: 'none',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: bgColor,
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
  }

  return (
    <Box style={editorStyle}>
      {/* Top Bar - matches Old Builder's TopBar */}
      <EditorTopBar />
      
      {/* Main Editor Area - Old Builder Layout */}
      <Flex height="calc(100vh - 50px)" overflow="hidden">
        {/* Left Sidebar - Old Builder Style with expandable panels */}
        {!isPreviewMode && (
          <OldBuilderSidebar />
        )}

        {/* Main Canvas Area - matches Old Builder's EditorIframe */}
        <EditorCanvas />

        {/* Right Panel - Properties and Layers (hidden by default like Old Builder) */}
        {!isPreviewMode && rightPanelCollapsed === false && (
          <EditorRightPanel
            collapsed={rightPanelCollapsed}
            onToggleCollapse={() => setRightPanelCollapsed(!rightPanelCollapsed)}
          />
        )}
      </Flex>

      {/* Global Components */}
      <KeyboardShortcuts />
      <AutoSave enabled={false} interval={30000} />
      
      {/* Portal for modals and overlays */}
      <div id="editor-portal" />
    </Box>
  )
}
