import {
  Box,
  FormLabel,
  Stack,
  Text,
  RadioGroup,
  Radio,
} from "@chakra-ui/react";
import { ImageObjectFit } from "@wuilt/section-preview";
import React from "react";
import { FormattedMessage } from "react-intl";

interface ImagePositionProps {
  value: ImageObjectFit;
  onChange: (v: ImageObjectFit) => void;
}

const ImagePosition: React.FC<ImagePositionProps> = ({ value, onChange }) => {
  return (
    <Box>
      <FormLabel mb="4px" fontSize="16px" fontWeight="400">
        <FormattedMessage defaultMessage="Image position" id="BsHOrE" />
      </FormLabel>
      <RadioGroup
        value={value}
        onChange={onChange}
        onClick={(e) => e.stopPropagation()}
        colorScheme="teal"
      >
        <Radio value={ImageObjectFit.Cover}>
          <Stack gap="4px">
            <Text fontSize="14px" fontWeight="600">
              <FormattedMessage defaultMessage="Cover" id="hl9bd4" />
            </Text>
            <Text fontSize="12px" color="gray.500">
              <FormattedMessage
                defaultMessage="Resize the image to cover the entire container"
                id="Ms3Zt2"
              />
            </Text>
          </Stack>
        </Radio>
        <Radio value={ImageObjectFit.Contain}>
          <Stack gap="4px">
            <Text fontSize="14px" fontWeight="600">
              <FormattedMessage defaultMessage="Contain" id="U9AgWv" />
            </Text>
            <Text fontSize="12px" color="gray.500">
              <FormattedMessage
                defaultMessage="Resize the image to make sure the image is fully visible"
                id="XOZ0cv"
              />
            </Text>
          </Stack>
        </Radio>

        <Radio value={ImageObjectFit.None}>
          <Stack gap="4px">
            <Text fontSize="14px" fontWeight="600">
              <FormattedMessage defaultMessage="Auto" id="NXI/XL" />
            </Text>
            <Text fontSize="12px" color="gray.500">
              <FormattedMessage
                defaultMessage="Keep the image in its original size"
                id="sa7qmc"
              />
            </Text>
          </Stack>
        </Radio>
      </RadioGroup>
    </Box>
  );
};

export default ImagePosition;
