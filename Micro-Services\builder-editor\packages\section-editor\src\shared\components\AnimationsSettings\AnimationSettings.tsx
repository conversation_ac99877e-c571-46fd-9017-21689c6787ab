import {
  Box,
  Divider,
  InputField,
  Label,
  Select,
  SelectTab,
  SelectTabs,
  Stack,
} from "@wuilt/quilt";
import React from "react";
import { FormattedMessage } from "react-intl";
import {
  GROUPED_ANIMATIONS_OPTIONS,
  FLAT_ANIMATIONS_OPTIONS,
  NONE_OPTION,
  EASING_OPTIONS,
} from "./animation-data";
import { AnimationType } from "@wuilt/section-preview";
import { components } from "react-select";

interface AnimationSettingsProps {
  animation: AnimationType | undefined;
  updateAnimation: (animation: AnimationType) => void;
}

const CustomOption = ({ children, ...props }) => {
  return (
    // eslint-disable-next-line jsx-a11y/no-static-element-interactions, jsx-a11y/click-events-have-key-events
    <div onClick={(e) => e.stopPropagation()}>
      {/* @ts-ignore */}
      <components.Option {...props}>{children}</components.Option>
    </div>
  );
};

const AnimationSettings: React.FC<AnimationSettingsProps> = ({
  animation,
  updateAnimation,
}) => {
  return (
    <Stack>
      <Box>
        <Label>
          <FormattedMessage defaultMessage="Animation" id="g80VV8" />
        </Label>
        <Select
          options={GROUPED_ANIMATIONS_OPTIONS as any}
          isClearable={false}
          value={
            FLAT_ANIMATIONS_OPTIONS.find((o) => o.value === animation?.name) ||
            NONE_OPTION
          }
          onChange={(o) => updateAnimation({ name: o?.value })}
          components={{
            Option: CustomOption,
          }}
        />
      </Box>
      <Box>
        <Label>
          <FormattedMessage defaultMessage="Easing" id="ZOmuJO" />
        </Label>
        <Select
          options={EASING_OPTIONS}
          isClearable={false}
          value={
            EASING_OPTIONS.find((o) => o.value === animation?.ease) ||
            EASING_OPTIONS[0]
          }
          onChange={(o) => updateAnimation({ ease: o?.value })}
        />
      </Box>
      <Stack direction="row">
        <Box>
          <Label>
            <FormattedMessage defaultMessage="Duration" id="IuFETn" />
          </Label>
          <InputField
            type="number"
            suffix="ms"
            value={animation?.duration || 500}
            onChange={(duration: any) => updateAnimation({ duration })}
          />
        </Box>
        <Box>
          <Label>
            <FormattedMessage defaultMessage="Delay" id="R86IN4" />
          </Label>
          <InputField
            type="number"
            suffix="ms"
            value={animation?.delay || 0}
            onChange={(delay: any) => updateAnimation({ delay })}
          />
        </Box>
      </Stack>
      <Divider />
      <Box>
        <Label>
          <FormattedMessage defaultMessage="Repeat" id="tw5j2+" />
        </Label>
        <SelectTabs
          value={+animation?.repeat! || 0}
          name="animation-repeat"
          onChange={(repeat) => {
            updateAnimation({ repeat: !!repeat });
          }}
        >
          <SelectTab
            label={<FormattedMessage defaultMessage="Once" id="DqTQOp" />}
            value={0}
          />
          <SelectTab
            label={<FormattedMessage defaultMessage="Repeat" id="tw5j2+" />}
            value={1}
          />
        </SelectTabs>
      </Box>
      <Divider />
      <Box>
        <Label>
          <FormattedMessage defaultMessage="Animation out" id="Bax/bh" />
        </Label>
        <SelectTabs
          value={+animation?.mirror! || 0}
          name="animation-mirror"
          onChange={(mirror) => {
            updateAnimation({ mirror: !!mirror });
          }}
        >
          <SelectTab
            label={<FormattedMessage defaultMessage="None" id="450Fty" />}
            value={0}
          />
          <SelectTab
            label={<FormattedMessage defaultMessage="Mirror" id="mSDQif" />}
            value={1}
          />
        </SelectTabs>
      </Box>
    </Stack>
  );
};

export default AnimationSettings;
