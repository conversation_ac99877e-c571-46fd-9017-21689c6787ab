import { nanoid } from "nanoid";
import React from "react";
import _cloneDeep from "lodash/cloneDeep";
import { FormattedMessage } from "react-intl";
import { migrateColumnSettings } from "../migrations/column-migration";
import styled, { css } from "styled-components";
import BackgroundTypeSettings from "../../../shared/components/BackgroundTypeSettings";
import {
  BackgroundType,
  BackgroundPosition,
  BackgroundRepeat,
  BackgroundScrollEffect,
  BackgroundSize,
  Background,
  Settings,
  VideoBackground,
  Gradient,
} from "@wuilt/section-preview";
import trackEvent from "../../../shared/utils/trackEvent";
import VideoBackgroundSettings from "../../../shared/components/VideoBackgroundSettings";
import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
  Button,
  Divider,
  IconButton,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Text,
} from "@chakra-ui/react";
import { Drag<PERSON>andle, VerticalSort } from "../../../components/DragAndDrop";
import {
  AddIcon,
  ColorDropIcon,
  ColorsIcon,
  GarbageIcon,
  ImageIcon,
} from "@wuilt/react-icons";

const defaultBackground: Background = {
  id: "will be overridden",
  type: BackgroundType.Color,
  color: "#FFFFFF01",
  gradient: {
    angle: 45,
    startColor: "#0000ff",
    endColor: "#00ffae",
    startPosition: 0,
    endPosition: 100,
  },
  image: {
    position: BackgroundPosition["center center"],
    repeat: BackgroundRepeat["repeat"],
    scrollEffect: BackgroundScrollEffect["fixed"],
    size: BackgroundSize.Auto,
  },
};

const LABELS = {
  [BackgroundType.Color]: (
    <FormattedMessage defaultMessage="Color" id="uMhpKe" />
  ),
  [BackgroundType.Gradient]: (
    <FormattedMessage defaultMessage="Gradient" id="y/FCma" />
  ),
  [BackgroundType.Image]: (
    <FormattedMessage defaultMessage="Image" id="+0zv6g" />
  ),
};

const ICONS = {
  [BackgroundType.Color]: <ColorsIcon color="ink.light" size="20px" />,
  [BackgroundType.Gradient]: <ColorDropIcon color="ink.light" size="20px" />,
  [BackgroundType.Image]: <ImageIcon color="ink.light" size="20px" />,
};

const BACKGROUND_TYPES = [
  BackgroundType.Color,
  BackgroundType.Gradient,
  BackgroundType.Image,
];

interface ColumnBackgroundProps {
  settings: Settings | undefined;
  updateSettings: (v: Settings) => void;
  onUploadImage: (cb: (src: string) => void) => void;
  source?: "SECTION" | "COLUMN" | "ROW";
}

const ColumnBackground: React.FC<ColumnBackgroundProps> = ({
  settings: oldSettings,
  updateSettings,
  onUploadImage,
  source,
}) => {
  const settings = migrateColumnSettings(oldSettings);
  const backgrounds = settings?.background || [];

  const updateBackgrounds = (newBackgrounds: Background[]) => {
    updateSettings({ ...settings, background: newBackgrounds });
  };

  const addBackground = () => {
    updateBackgrounds([{ ...defaultBackground, id: nanoid() }, ...backgrounds]);
  };

  const deleteBackground = (id: string) => {
    updateBackgrounds(backgrounds.filter((i: any) => i?.id !== id));
  };

  const updateBackground = (
    newBackground: Partial<Background>,
    index: number
  ) => {
    const cloned = _cloneDeep(backgrounds);
    cloned[index] = {
      ...cloned[index],
      ...newBackground,
    };
    updateBackgrounds(cloned);
  };

  const updateVideoBackground = (
    newVideoBackgroundSettings: VideoBackground
  ) => {
    updateSettings({
      ...settings,
      videoBackground: {
        ...settings?.videoBackground,
        ...newVideoBackgroundSettings,
      },
    });
  };

  return (
    <Stack gap="16px">
      <Box>
        <Button
          variant="plain"
          color="primary.500"
          size="sm"
          paddingInline={0}
          leftIcon={<AddIcon size="16px" />}
          onClick={addBackground}
        >
          <FormattedMessage defaultMessage="Add background" id="8Nz2DS" />
        </Button>
      </Box>
      <Accordion allowToggle>
        <VerticalSort
          value={backgrounds}
          onChange={updateBackgrounds}
          useHandleOnly
        >
          {({ item, index }) => (
            <AccordionItem key={index}>
              <AccordionButton py="0" _expanded={{ bg: "gray.100" }} bg="white">
                <Box flex="1" textAlign="left">
                  <Stack direction="row" align="center" width="100%" gap="12px">
                    <DragHandle id={item?.id} />
                    {ICONS[item?.type]}
                    <StyledDisplay
                      bgColor={item?.color || "transparent"}
                      imageSrc={item?.image?.src || ""}
                      gradient={item?.gradient || {}}
                      type={item?.type || BackgroundType.Color}
                    />
                    <Stack
                      flex="2"
                      direction="row"
                      align="center"
                      justify="space-between"
                      gap="none"
                    >
                      <Text color="secondary" fontSize="14px" fontWeight="400">
                        {LABELS[item?.type]}
                      </Text>

                      <AccordionIcon />
                    </Stack>
                    <Divider orientation="vertical" height="30px" />
                    <IconButton
                      aria-label="Delete Background"
                      size="small"
                      variant="plain"
                      onClick={(e) => {
                        e.stopPropagation();
                        trackEvent(
                          source === "SECTION"
                            ? "sectionBackgroundDeleted"
                            : source === "ROW"
                            ? "rowBackgroundDeleted"
                            : "columnBackgroundDeleted",
                          {
                            "background type": item?.type,
                          }
                        );
                        deleteBackground(item?.id);
                      }}
                    >
                      <GarbageIcon size="16px" color="error.500" />
                    </IconButton>
                  </Stack>
                </Box>
              </AccordionButton>
              <AccordionPanel background="gray.100">
                <Stack gap="16px" mt="10px">
                  <Stack gap="16px" align="center">
                    <Tabs
                      isFitted
                      variant="selectTabs"
                      index={BACKGROUND_TYPES.indexOf(item?.type) || 0}
                      onChange={(tabIndex) => {
                        const type = BACKGROUND_TYPES[tabIndex];
                        updateBackground({ type }, index);
                        trackEvent(
                          source === "SECTION"
                            ? "sectionBackgroundAdded"
                            : source === "ROW"
                            ? "rowBackgroundAdded"
                            : "columnBackgroundAdded",
                          {
                            "background type": type,
                          }
                        );
                      }}
                    >
                      <TabList>
                        {Object.keys(LABELS)?.map((tab) => (
                          <Tab key={tab}>{(LABELS as any)[tab]}</Tab>
                        ))}
                      </TabList>
                    </Tabs>
                  </Stack>
                  <BackgroundTypeSettings
                    background={item}
                    updateBackground={(newBackground: Partial<Background>) => {
                      updateBackground(newBackground, index);
                    }}
                    onUploadImage={onUploadImage}
                  />
                </Stack>
              </AccordionPanel>
            </AccordionItem>
          )}
        </VerticalSort>
      </Accordion>

      <VideoBackgroundSettings
        source={source}
        videoBackground={settings?.videoBackground}
        updateVideoBackground={updateVideoBackground}
      />
    </Stack>
  );
};

export default ColumnBackground;

/**
 * Styles
 */

const StyledDisplay = styled.div<{
  bgColor: string;
  imageSrc: string;
  gradient: Gradient;
  type: BackgroundType;
}>`
  width: 20px;
  height: 20px;
  border: 1px solid var(--chakra-colors-cloud-normal);
  border-radius: 4px;

  ${({ type, bgColor }) =>
    type === BackgroundType.Color &&
    css`
      background-color: ${bgColor};
    `}

  ${({ type, imageSrc }) =>
    type === BackgroundType.Image &&
    css`
      background-image: url(${imageSrc});
      background-size: cover;
    `}

  ${({
    type,
    gradient: { angle, startColor, endColor, startPosition, endPosition },
  }) =>
    type === BackgroundType.Gradient &&
    css`
      background-image: linear-gradient(
        ${angle}deg,
        ${startColor} ${startPosition}%,
        ${endColor} ${endPosition}%
      );
    `}
`;
