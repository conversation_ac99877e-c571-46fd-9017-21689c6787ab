import React, { useState } from "react";
import { FormattedMessage } from "react-intl";
import type { ElementSettingsProps } from "../../Element/components/ElementSettings";
import AddSocialLink from "./AddSocialLink";
import {
  IconStyle,
  SocialLinkSettings,
  SocialLinksSettings as SocialLinksSettingsType,
} from "@wuilt/section-preview";
import ChooseIcon from "./ChooseIcon";
import IconsList from "./IconsList";
import IconStyles from "./IconStyles";
import {
  Button,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Stack,
  Heading,
} from "@chakra-ui/react";
import { ChevronLeftIcon } from "@wuilt/react-icons";
import { useTheme } from "styled-components";
import { Popup } from "../../../components/Popup";

export enum SocialLinksSettingsViews {
  "List",
  "ChooseIcon",
}

enum SocialLinksSettingsTabs {
  "Icons",
  "Style",
}

const TABS = [
  {
    content: <FormattedMessage defaultMessage="Icons" id="+wvMYM" />,
    id: SocialLinksSettingsTabs.Icons,
  },
  {
    content: <FormattedMessage defaultMessage="Style" id="7mL9QE" />,
    id: SocialLinksSettingsTabs.Style,
  },
];

interface SocialLinksSettingsProps extends ElementSettingsProps {}

const SocialLinksSettings: React.FC<SocialLinksSettingsProps> = ({
  element,
  updateElementUi,
}) => {
  const [view, setView] = useState(SocialLinksSettingsViews.List);
  const elementSettings = element?.settings as SocialLinksSettingsType;
  const dir = useTheme()?.dir;

  const addSocialLinkIcon = (socialLinkButton: SocialLinkSettings) => {
    updateElementUi({
      ...element,
      settings: {
        ...element?.settings,
        socialLinks: [...elementSettings.socialLinks!, socialLinkButton],
      },
    });
  };

  const updateSocialLinkAction = (socialLink: SocialLinkSettings) => {
    const updatedSocialLinks: SocialLinkSettings[] =
      elementSettings?.socialLinks?.map((link) =>
        link?.id === socialLink?.id ? socialLink : link
      )!;
    updateElementUi({
      ...element,
      settings: {
        ...elementSettings,
        socialLinks: updatedSocialLinks,
      },
    });
  };

  const updateSocialLinkStyle = (iconStyle: IconStyle) => {
    updateElementUi({
      ...element,
      settings: { ...elementSettings, iconStyle },
    });
  };

  const deleteSocialLink = (id: string) => {
    const updatedSocialLinks: SocialLinkSettings[] =
      elementSettings?.socialLinks?.filter((link) => link?.id !== id)!;
    updateElementUi({
      ...element,
      settings: {
        ...elementSettings,
        socialLinks: updatedSocialLinks,
      },
    });
  };

  const sortSocialLinksUi = (links: SocialLinkSettings[]) => {
    updateElementUi({
      ...element,
      settings: {
        ...elementSettings,
        socialLinks: links,
      },
    });
  };

  return (
    <>
      <Popup.Header
        headerContentControls={{
          justifyContent: `start`,
        }}
      >
        {view === SocialLinksSettingsViews.ChooseIcon ? (
          <>
            <Button
              variant="plain"
              size="sm"
              paddingInline={0}
              color="white"
              onClick={() => setView(SocialLinksSettingsViews.List)}
              leftIcon={
                <ChevronLeftIcon
                  size="16px"
                  transform={dir === "rtl" && "rotate(180deg)"}
                />
              }
            >
              <FormattedMessage defaultMessage="Back" id="cyR7Kh" />
            </Button>
            <Heading variant="h5" p="10px">
              <FormattedMessage defaultMessage="Choose icon" id="hnLUGX" />
            </Heading>
          </>
        ) : (
          <>
            <Heading variant="h5">
              <FormattedMessage
                defaultMessage="Social icons settings"
                id="EsV1qu"
              />
            </Heading>
          </>
        )}
      </Popup.Header>
      <Popup.Body pt={0} width="370px">
        <Stack gap="16px">
          {view === SocialLinksSettingsViews.ChooseIcon ? (
            <ChooseIcon
              addSocialLinkIcon={addSocialLinkIcon}
              setView={setView}
            />
          ) : (
            <Tabs>
              <TabList>
                {TABS.map(({ content, id }) => {
                  return (
                    <Tab
                      _selected={{
                        borderColor: "primary.500",
                        color: "primary.500",
                      }}
                      _focusWithin={{ outline: "none" }}
                      key={id}
                    >
                      {content}
                    </Tab>
                  );
                })}
              </TabList>
              <TabPanels>
                <TabPanel paddingInline={0} paddingBottom={0}>
                  <IconsList
                    socialLinks={elementSettings?.socialLinks}
                    sortSocialLinksUi={sortSocialLinksUi}
                    deleteSocialLink={deleteSocialLink}
                    updateSocialLinkAction={updateSocialLinkAction}
                  />
                  <AddSocialLink setView={setView} />
                </TabPanel>
                <TabPanel paddingInline={0} paddingBottom={0}>
                  <IconStyles
                    iconStyle={elementSettings?.iconStyle}
                    updateSocialLinkStyle={updateSocialLinkStyle}
                  />
                </TabPanel>
              </TabPanels>
            </Tabs>
          )}
        </Stack>
      </Popup.Body>
    </>
  );
};

export default SocialLinksSettings;
