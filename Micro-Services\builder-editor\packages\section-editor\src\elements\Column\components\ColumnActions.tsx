import React, { useRef, useState } from "react";
import { FormattedMessage } from "react-intl";
import trackEvent from "../../../shared/utils/trackEvent";
import {
  AlertDialog,
  AlertDialogBody,
  AlertDialogCloseButton,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogOverlay,
  Box,
  IconButton,
  Popover,
  PopoverBody,
  PopoverContent,
  PopoverTrigger,
  Button,
  Portal,
  useDisclosure,
  useOutsideClick,
  Stack,
} from "@chakra-ui/react";
import { DuplicateIcon, GarbageIcon, MoreHorizIcon } from "@wuilt/react-icons";

interface ColumnActionsProps {
  disableAdding: boolean;
  disableDeleting: boolean;
  deleteColumn: () => void;
  duplicateColumn: () => void;
}

const ColumnActions: React.FC<ColumnActionsProps> = ({
  disableAdding,
  disableDeleting,
  duplicateColumn,
  deleteColumn,
}) => {
  const [openConfirmModal, setOpenConfirmModal] = useState(false);
  const { isOpen, onToggle, onClose } = useDisclosure();
  const popoverRef = useRef(null);
  const cancelRef = useRef();

  useOutsideClick({
    ref: popoverRef,
    handler: onClose,
  });
  return (
    <>
      <Popover isOpen={isOpen} onClose={onClose}>
        <PopoverTrigger>
          <IconButton
            aria-label="Options"
            variant="plain"
            size="none"
            onClick={onToggle}
            onFocus={(e) => e.preventDefault()}
          >
            <MoreHorizIcon size="16px" />
          </IconButton>
        </PopoverTrigger>
        <Portal>
          <Box zIndex="popover">
            <PopoverContent ref={popoverRef} maxW="max-content">
              <PopoverBody>
                <Stack gap="16px" direction="column">
                  {!disableAdding && (
                    <Button
                      variant="plain"
                      size="sm"
                      paddingInline={0}
                      leftIcon={<DuplicateIcon color="black" size="16px" />}
                      onClick={() => {
                        duplicateColumn();
                        trackEvent("columnDuplicated");
                        onClose();
                      }}
                    >
                      <FormattedMessage
                        defaultMessage="Duplicate"
                        id="4fHiNl"
                      />
                    </Button>
                  )}
                  {!disableDeleting && (
                    <Button
                      variant="plain"
                      size="sm"
                      color="error.500"
                      paddingInline={0}
                      onClick={() => {
                        setOpenConfirmModal(true);
                        onClose();
                      }}
                      leftIcon={<GarbageIcon size="16px" />}
                    >
                      <FormattedMessage defaultMessage="Delete" id="K3r6DQ" />
                    </Button>
                  )}
                </Stack>
              </PopoverBody>
            </PopoverContent>
          </Box>
        </Portal>
      </Popover>

      <AlertDialog
        motionPreset="slideInBottom"
        leastDestructiveRef={cancelRef}
        onClose={() => setOpenConfirmModal(false)}
        isOpen={openConfirmModal}
        isCentered
        returnFocusOnClose={false}
      >
        <AlertDialogOverlay />

        <AlertDialogContent>
          <AlertDialogHeader>
            <FormattedMessage defaultMessage="Deleting Column" id="gBDmQQ" />
          </AlertDialogHeader>
          <AlertDialogCloseButton />
          <AlertDialogBody>
            <p>
              <FormattedMessage
                defaultMessage="Are you sure you want to delete this column? This action cannot be undone."
                id="Bp1pG0"
              />
            </p>
          </AlertDialogBody>
          <AlertDialogFooter>
            <Button
              size="sm"
              variant="outline"
              ref={cancelRef}
              onClick={() => setOpenConfirmModal(false)}
            >
              <FormattedMessage defaultMessage="Cancel" id="47FYwb" />
            </Button>
            <Button
              size="sm"
              colorScheme="red"
              onClick={() => {
                deleteColumn();
                trackEvent("rowDeleted");
              }}
              ml={3}
            >
              <FormattedMessage defaultMessage="Delete" id="K3r6DQ" />
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default ColumnActions;
