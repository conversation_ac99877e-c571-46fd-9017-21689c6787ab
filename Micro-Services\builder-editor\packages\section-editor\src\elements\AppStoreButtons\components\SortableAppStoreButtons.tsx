import React from "react";
import { FormattedMessage } from "react-intl";
import { AppStoreButtonSettings } from "@wuilt/section-preview";
import { DragHandle, VerticalSort } from "../../../components/DragAndDrop";
import { ChevronRightIcon, TrashIcon } from "@wuilt/react-icons";
import { Divider, IconButton, Stack, Text } from "@chakra-ui/react";
import { useTheme } from "styled-components";

interface AppStoreButtonsProps {
  appStoreButtons?: AppStoreButtonSettings[];
  sortAppStoreButtonsUi: (arg: AppStoreButtonSettings[]) => void;
  deleteAppStoreButton: (arg: string) => void;
  setActiveButtonHandler: (button: AppStoreButtonSettings) => void;
}

const ButtonsTypes: any = {
  Apple: <FormattedMessage defaultMessage="Apple App Store" id="R0hki2" />,
  Google: <FormattedMessage defaultMessage="Google Play Store" id="xUJMg2" />,
  Samsung: (
    <FormattedMessage defaultMessage="Samsung Galaxy Store" id="FYlJzX" />
  ),
  Huawei: <FormattedMessage defaultMessage="Huawei App Gallery" id="4YxAWu" />,
};

const SortableAppStoreButtons: React.FC<AppStoreButtonsProps> = ({
  appStoreButtons,
  sortAppStoreButtonsUi,
  deleteAppStoreButton,
  setActiveButtonHandler,
}) => {
  const dir = useTheme()?.dir;
  return (
    <VerticalSort
      useHandleOnly
      value={appStoreButtons!}
      onChange={(appStoreButtons) => sortAppStoreButtonsUi(appStoreButtons)}
    >
      {({ item: appStoreButton }) => (
        <>
          <Stack
            gap="16px"
            direction="row"
            justify="space-between"
            align="center"
            cursor="pointer"
            className="dnd-item"
            height={50}
            onClick={() => setActiveButtonHandler(appStoreButton)}
          >
            <Stack gap="16px" direction="row" align="center">
              <DragHandle id={appStoreButton?.id} />
              <Text color="black" fontSize="14px" fontWeight="500" mb={0}>
                {ButtonsTypes[appStoreButton?.action?.type!]}
              </Text>
            </Stack>
            <Stack
              gap={"12px"}
              direction={"row"}
              alignItems={"center"}
              justifyContent={"flex-end"}
            >
              <IconButton
                aria-label="Delete"
                size="16px"
                variant="plain"
                color={"red"}
                disabled={appStoreButtons?.length === 1}
                onClick={(e) => {
                  e.stopPropagation();
                  deleteAppStoreButton(appStoreButton?.id!);
                }}
              >
                <TrashIcon size="18px" />
              </IconButton>
              <ChevronRightIcon
                size="16px"
                transform={dir === "rtl" && "rotate(180deg)"}
              />
            </Stack>
          </Stack>
          <Divider m={0} />
        </>
      )}
    </VerticalSort>
  );
};

export default SortableAppStoreButtons;
