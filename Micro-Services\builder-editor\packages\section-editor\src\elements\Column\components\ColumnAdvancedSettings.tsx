import React from "react";
import { FormattedMessage } from "react-intl";
import AnimationSettings from "../../../shared/components/AnimationsSettings/AnimationSettings";
import { Settings } from "@wuilt/section-preview";
import {
  Divider,
  Heading,
  Accordion,
  AccordionItem,
  AccordionButton,
  Box,
  AccordionIcon,
  AccordionPanel,
} from "@chakra-ui/react";

interface ColumnAdvancedSettingsProps {
  settings: Settings | undefined;
  updateSettings: (v: Settings) => void;
}

const ColumnAdvancedSettings: React.FC<ColumnAdvancedSettingsProps> = ({
  settings,
  updateSettings,
}) => {
  const StylesTabs = [
    {
      id: 1,
      header: (
        <FormattedMessage defaultMessage="Animation on scroll" id="YWuJ7v" />
      ),
      body: (
        <AnimationSettings
          animation={settings?.animation}
          updateAnimation={(animation) => {
            updateSettings({
              ...settings,
              animation: { ...settings?.animation, ...animation },
            });
          }}
        />
      ),
    },
  ];

  return (
    <div>
      <Accordion allowToggle>
        {StylesTabs.map(({ header, body, id }) => {
          return (
            <AccordionItem key={id}>
              <AccordionButton
                _expanded={{ color: "primary.500", bg: "gray.100" }}
                bg="white"
              >
                <Box flex="1" textAlign="left">
                  <Heading color="inherit" fontSize="16px" fontWeight="700">
                    {header}
                  </Heading>
                </Box>
                <AccordionIcon />
              </AccordionButton>
              <AccordionPanel background="gray.100">
                <Divider borderBottomColor="gray.300" mb="12px" />
                {body}
              </AccordionPanel>
            </AccordionItem>
          );
        })}
      </Accordion>
    </div>
  );
};

export default ColumnAdvancedSettings;
