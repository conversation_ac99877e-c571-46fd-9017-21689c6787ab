import React from "react";
import { FormattedMessage } from "react-intl";
import RowStyle from "./RowStyle";
import { updateRowMutation } from "../../../shared/mutations";
import RowAdvancedSettings from "./RowAdvancedSettings";
import RowBackground from "./RowBackground";
import RowLayout from "./RowLayout";
import { Row, UpdateDataFunc } from "@wuilt/section-preview";
import {
  Stack,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
} from "@chakra-ui/react";

enum RowSettingsTabs {
  "Layout",
  "Style",
  "Background",
  "Advanced",
}

const TABS = [
  {
    content: <FormattedMessage defaultMessage="Layout" id="RQ4EKT" />,
    id: RowSettingsTabs.Layout,
  },
  {
    content: <FormattedMessage defaultMessage="Style" id="7mL9QE" />,
    id: RowSettingsTabs.Style,
  },
  {
    content: <FormattedMessage defaultMessage="Background" id="XQZA8e" />,
    id: RowSettingsTabs.Background,
  },
  {
    content: <FormattedMessage defaultMessage="Advanced" id="3Rx6Qo" />,
    id: RowSettingsTabs.Advanced,
  },
];

interface RowSettingsProps {
  row: Row;
  rowIndex: number;
  updateUi: UpdateDataFunc;
  onUploadImage: (cb: (src: string) => void) => void;
}

const RowSettings: React.FC<RowSettingsProps> = ({
  row,
  rowIndex,
  updateUi,
  onUploadImage,
}) => {
  const updateSettings = (newSettings: Row["settings"]) => {
    updateUi((prev) => {
      return updateRowMutation(prev, rowIndex, newSettings);
    });
  };

  return (
    <Stack>
      <Tabs>
        <TabList>
          {TABS.map(({ content, id }) => {
            return <Tab key={id}>{content}</Tab>;
          })}
        </TabList>
        <TabPanels>
          <TabPanel paddingInline={0} paddingBottom={0}>
            <RowLayout
              settings={row?.settings!}
              updateSettings={updateSettings}
            />
          </TabPanel>
          <TabPanel paddingInline={0} paddingBottom={0}>
            <RowStyle
              settings={row?.settings!}
              updateSettings={updateSettings}
            />
          </TabPanel>
          <TabPanel paddingInline={0} paddingBottom={0}>
            <RowBackground
              settings={row?.settings!}
              updateSettings={updateSettings}
              onUploadImage={onUploadImage}
            />
          </TabPanel>
          <TabPanel paddingInline={0} paddingBottom={0}>
            <RowAdvancedSettings
              settings={row?.settings!}
              updateSettings={updateSettings}
            />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Stack>
  );
};

export default RowSettings;
