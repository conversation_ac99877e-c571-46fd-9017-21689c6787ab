import React from "react";
import { FormattedMessage } from "react-intl";
import { Button, Flex, IconButton, Text, VStack } from "@chakra-ui/react";
import { AddIcon, TrashIcon } from "@wuilt/react-icons";
import InputField from "../../../../components/InputField";
import trackEvent from "../../../../shared/utils/trackEvent";

interface NotificationsTabProps {
  emails: string[];
  addEmail: (newEmail: string) => void;
  updateEmail: (updatedEmail: string, emailIndex: number) => void;
  deleteEmail: (emailIndex: number) => void;
}

const NotificationsTab: React.FC<NotificationsTabProps> = ({
  emails,
  addEmail,
  updateEmail,
  deleteEmail,
}) => {
  return (
    <VStack gap="8px" padding="16px">
      <Text width="full" variant="textSm" fontWeight="medium" color="gray.800">
        <FormattedMessage
          defaultMessage="Send submission to e-mail"
          id="letKUr"
        />
      </Text>
      {emails?.map((email, index) => (
        <Flex
          key={index}
          gap="6px"
          width="full"
          alignItems="center"
          justifyContent="space-between"
        >
          <InputField
            value={email}
            placeholder={
              <FormattedMessage
                defaultMessage="ex. <EMAIL>"
                id="/GUpEN"
              />
            }
            onChange={(event) => updateEmail(event.target.value, index)}
          />
          <IconButton
            size="sm"
            padding="8px"
            borderRadius="8px"
            // eslint-disable-next-line formatjs/no-literal-string-in-jsx
            aria-label="Delete"
            variant="errorTertiaryColor"
            isDisabled={emails?.length === 1}
            onClick={() => {
              deleteEmail(index);
              trackEvent("formSubmissionEmailDeleted");
            }}
          >
            <TrashIcon size="20px" color="error.600" />
          </IconButton>
        </Flex>
      ))}
      <Button
        size="sm"
        width="full"
        padding="16px"
        borderRadius="8px"
        justifyContent="start"
        variant="tertiaryColor"
        leftIcon={<AddIcon size="20px" />}
        onClick={() => {
          addEmail("");
          trackEvent("formSubmissionEmailAdded");
        }}
        isDisabled={emails.some((email) => !email)}
      >
        <FormattedMessage defaultMessage="Add another e-mail" id="BWtPCq" />
      </Button>
    </VStack>
  );
};

export default NotificationsTab;
