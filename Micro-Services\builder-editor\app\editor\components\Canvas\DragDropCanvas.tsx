'use client'

import { useCallback, useRef, useState } from 'react'
import {
  Box,
  Flex,
  Text,
  useColorModeValue,
  VStack,
  Button,
  Icon
} from '@chakra-ui/react'
import { motion, AnimatePresence } from 'framer-motion'
import { useEditorStore } from '@/lib/stores/editorStore'
import { SectionRenderer } from './SectionRenderer'
import { DropZone } from './DropZone'
import { AddIcon } from '@chakra-ui/icons'
import { AddSectionFloatingSidebar } from '../AddSection/AddSectionFloatingSidebar'

export function DragDropCanvas() {
  const canvasRef = useRef<HTMLDivElement>(null)
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null)
  const [showAddSectionSidebar, setShowAddSectionSidebar] = useState(false)
  const [addSectionAfter, setAddSectionAfter] = useState<string | undefined>(undefined)

  const {
    currentPage,
    selectedElement,
    selectedSection,
    currentBreakpoint,
    isDragging,
    selectElement,
    selectSection,
    addSection,
    moveSection
  } = useEditorStore()

  const bgColor = useColorModeValue('gray.50', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  const handleDragOver = useCallback((e: React.DragEvent, index: number) => {
    e.preventDefault()
    setDragOverIndex(index)
  }, [])

  const handleDragLeave = useCallback(() => {
    setDragOverIndex(null)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent, index: number) => {
    e.preventDefault()
    setDragOverIndex(null)
    
    const dragData = e.dataTransfer.getData('application/json')
    if (dragData) {
      try {
        const data = JSON.parse(dragData)
        
        if (data.type === 'section') {
          // Create new section from palette with default props
          const newSection = {
            id: `section-${Date.now()}`,
            type: data.sectionType,
            name: data.name,
            elements: [], // Start with empty elements, will be populated based on defaultProps
            style: data.defaultStyle || {},
            responsive: {
              desktop: {},
              tablet: {},
              mobile: {}
            },
            // Store the design props for rendering
            designId: data.designId,
            props: data.defaultProps || {}
          }
          addSection(newSection, index)
        } else if (data.type === 'move-section') {
          // Move existing section
          moveSection(data.sectionId, index)
        }
      } catch (error) {
        console.error('Error parsing drag data:', error)
      }
    }
  }, [addSection, moveSection])

  const handleAddSection = useCallback((afterSectionId?: string) => {
    setAddSectionAfter(afterSectionId)
    setShowAddSectionSidebar(true)
  }, [])

  const handleCloseSidebar = useCallback(() => {
    setShowAddSectionSidebar(false)
    setAddSectionAfter(undefined)
  }, [])

  const handleCanvasClick = useCallback((e: React.MouseEvent) => {
    // Deselect elements when clicking on empty canvas
    if (e.target === e.currentTarget) {
      selectElement(null)
      selectSection(null)
    }
  }, [selectElement, selectSection])

  if (!currentPage) {
    return (
      <Flex
        flex="1"
        align="center"
        justify="center"
        bg={bgColor}
        direction="column"
        gap={4}
      >
        <Text fontSize="xl" color="gray.500">
          No page selected
        </Text>
        <Text fontSize="sm" color="gray.400">
          Create or select a page to start editing
        </Text>
      </Flex>
    )
  }

  return (
    <Box
      ref={canvasRef}
      flex="1"
      bg={bgColor}
      position="relative"
      overflow="auto"
      onClick={handleCanvasClick}
    >
      {/* Canvas Header */}
      <Flex
        position="sticky"
        top="0"
        zIndex="10"
        bg="white"
        borderBottom="1px"
        borderColor={borderColor}
        px={4}
        py={2}
        align="center"
        justify="space-between"
      >
        <Text fontWeight="semibold" color="gray.700">
          {currentPage.name}
        </Text>
        <Flex align="center" gap={2}>
          <Text fontSize="sm" color="gray.500">
            {currentBreakpoint.charAt(0).toUpperCase() + currentBreakpoint.slice(1)} View
          </Text>
        </Flex>
      </Flex>

      {/* Canvas Content */}
      <Box
        minH="calc(100vh - 120px)"
        position="relative"
        mx="auto"
        maxW={
          currentBreakpoint === 'desktop' ? '1200px' :
          currentBreakpoint === 'tablet' ? '768px' : '375px'
        }
        transition="max-width 0.3s ease"
      >
        {currentPage.sections.length === 0 ? (
          // Empty state
          <Flex
            minH="400px"
            align="center"
            justify="center"
            direction="column"
            gap={4}
            border="2px dashed"
            borderColor="gray.300"
            borderRadius="lg"
            m={4}
          >
            <Icon as={AddIcon} boxSize={8} color="gray.400" />
            <Text fontSize="lg" color="gray.500" textAlign="center">
              Start building your page
            </Text>
            <Text fontSize="sm" color="gray.400" textAlign="center" maxW="300px">
              Drag sections from the left panel or click the button below to add your first section
            </Text>
            <Button
              leftIcon={<AddIcon />}
              bg="#1f9aeb"
              color="white"
              borderRadius="4px"
              fontWeight="600"
              px="20px"
              py="12px"
              _hover={{
                bg: "#177ab8",
              }}
              onClick={() => handleAddSection()}
            >
              Add Section
            </Button>
          </Flex>
        ) : (
          // Sections list
          <VStack spacing={0} align="stretch">
            {currentPage.sections.map((section, index) => (
              <Box key={section.id} position="relative">
                {/* Drop zone above section */}
                <DropZone
                  index={index}
                  isActive={dragOverIndex === index}
                  onDragOver={(e) => handleDragOver(e, index)}
                  onDragLeave={handleDragLeave}
                  onDrop={(e) => handleDrop(e, index)}
                />
                
                {/* Section component */}
                <motion.div
                  layout
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <SectionRenderer
                    section={section}
                    isSelected={selectedSection?.id === section.id}
                    onSelect={() => selectSection(section)}
                  />
                </motion.div>
              </Box>
            ))}
            
            {/* Drop zone at the end */}
            <DropZone
              index={currentPage.sections.length}
              isActive={dragOverIndex === currentPage.sections.length}
              onDragOver={(e) => handleDragOver(e, currentPage.sections.length)}
              onDragLeave={handleDragLeave}
              onDrop={(e) => handleDrop(e, currentPage.sections.length)}
            />
          </VStack>
        )}
      </Box>

      {/* Floating Add Section Button - Old Builder Style */}
      {currentPage.sections.length > 0 && (
        <Box
          position="fixed"
          bottom="20px"
          left="50%"
          transform="translateX(-50%)"
          zIndex={30}
        >
          <Button
            leftIcon={<AddIcon />}
            bg="#1f9aeb"
            color="white"
            size="md"
            borderRadius="25px"
            px={6}
            py={3}
            fontWeight="600"
            fontSize="sm"
            shadow="0 4px 12px rgba(31, 154, 235, 0.4)"
            border="2px solid white"
            onClick={() => handleAddSection()}
            _hover={{
              bg: "#177ab8",
              transform: 'scale(1.05)',
              shadow: "0 6px 16px rgba(31, 154, 235, 0.5)"
            }}
            transition="all 0.2s"
          >
            Add Section
          </Button>
        </Box>
      )}

      {/* Add Section Floating Sidebar */}
      <AddSectionFloatingSidebar
        isOpen={showAddSectionSidebar}
        onClose={handleCloseSidebar}
        afterSectionId={addSectionAfter}
      />

      {/* Drag overlay */}
      <AnimatePresence>
        {isDragging && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
              pointerEvents: 'none',
              zIndex: 20
            }}
          />
        )}
      </AnimatePresence>
    </Box>
  )
}
