import {
  Alert,
  AlertDescription,
  AlertTitle,
  Box,
  FormLabel,
  Stack,
  Text,
  Tooltip,
  Switch,
  InputLeftElement,
  InputGroup,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  Divider,
} from "@chakra-ui/react";
import {
  FacebookGrayScaleIcon,
  InfoIcon,
  TwitchIcon,
  VimeoIcon,
  YoutubeIcon,
} from "@wuilt/react-icons";
import { VideoSettings } from "@wuilt/section-preview";
import React from "react";
import { FormattedMessage, injectIntl, IntlShape } from "react-intl";
import NumberInput from "../../../components/NumberInput";
import InputField from "../../../components/InputField";

const PERCENT = "%";

interface VideoDetailsProps {
  videoSettings: VideoSettings;
  intl: IntlShape;
  updateVideoSettings: (videoSettings: VideoSettings) => void;
}

const VideoDetails: React.FC<VideoDetailsProps> = ({
  videoSettings,
  intl,
  updateVideoSettings,
}) => {
  return (
    <Stack>
      <Tabs>
        <TabList>
          <Tab fontSize={"14px"} width={"100%"}>
            Content
          </Tab>
          <Tab fontSize={"14px"} width={"100%"}>
            Style
          </Tab>
        </TabList>

        <TabPanels>
          <TabPanel>
            <Box>
              <FormLabel>
                <FormattedMessage defaultMessage="Video link" id="LX0iAn" />
              </FormLabel>
              <InputField
                placeholder={intl.formatMessage({
                  defaultMessage: "https://",
                  id: "Vthr0a",
                })}
                value={videoSettings.url}
                onChange={(e) => {
                  updateVideoSettings({
                    ...videoSettings,
                    url: e.target.value,
                  });
                }}
              />
            </Box>
            <Stack
              direction={"row"}
              justifyContent={"space-between"}
              mt={"8px"}
            >
              <Stack direction="row" align="center" gap="8px">
                <Text fontSize="14px" fontWeight="500" color="black">
                  <FormattedMessage
                    defaultMessage="Supported platforms"
                    id="cKXXDA"
                  />
                </Text>
                <Tooltip
                  placement="top"
                  hasArrow
                  label={
                    <Text fontSize={12} color="white">
                      <FormattedMessage
                        defaultMessage={`Acceptable URL formats {br} https://youtube.com/watch?v=xxxxx {br} https://youtube.com/embed/xxxxx {br} https://vimeo.com/xxxxx`}
                        values={{ br: <br /> }}
                        id="zeLmIk"
                      />
                    </Text>
                  }
                >
                  <span>
                    <InfoIcon size="16px" color="gray.500" />
                  </span>
                </Tooltip>
              </Stack>
              <Stack
                gap="12px"
                direction="row"
                justify="start"
                align="start"
                height="100%"
              >
                <Tooltip
                  placement="top"
                  hasArrow
                  label={
                    <FormattedMessage defaultMessage="Youtube" id="ix27PQ" />
                  }
                >
                  <span>
                    <YoutubeIcon color="gray.500" size="18px" />
                  </span>
                </Tooltip>
                <Tooltip
                  placement="top"
                  hasArrow
                  label={
                    <FormattedMessage defaultMessage="Vimeo" id="D0ecjA" />
                  }
                >
                  <span>
                    <VimeoIcon color="gray.500" size="18px" />
                  </span>
                </Tooltip>
                <Tooltip
                  placement="top"
                  hasArrow
                  label={
                    <FormattedMessage defaultMessage="Facebook" id="EmpHyB" />
                  }
                >
                  <span>
                    <FacebookGrayScaleIcon color="gray.500" size="18px" />
                  </span>
                </Tooltip>
                <Tooltip
                  placement="top"
                  hasArrow
                  label={
                    <FormattedMessage defaultMessage="Twitch" id="3LuP+7" />
                  }
                >
                  <span>
                    <TwitchIcon color="gray.500" size="18px" />
                  </span>
                </Tooltip>
              </Stack>
            </Stack>
            <Divider mt={"12px"} mb={"12px"} />
            <Alert
              bgColor="gray.25"
              border="2px solid"
              borderColor="gray.300"
              borderRadius="8px"
              status="info"
            >
              <Box>
                <AlertTitle fontSize="14px" color="gray.500">
                  <FormattedMessage
                    defaultMessage="Video not Showing?"
                    id="jjJpmM"
                  />
                </AlertTitle>
                <AlertDescription fontSize={16} fontWeight={400}>
                  <FormattedMessage
                    defaultMessage="If the link is not showing results, or you wish to use a platform that is not supported, we recommend using the HTML Embed element instead"
                    id="Gzz4Jm"
                  />
                </AlertDescription>
              </Box>
            </Alert>
          </TabPanel>
          <TabPanel>
            <Stack gap="16px" direction="row">
              <Box>
                <FormLabel>
                  <FormattedMessage defaultMessage="Width" id="5IP7AP" />
                </FormLabel>
                <InputGroup>
                  <InputLeftElement color="GrayText">
                    {PERCENT}
                  </InputLeftElement>
                  <NumberInput
                    inputFieldProps={{
                      placeholder: "100",
                      paddingInlineStart: "35px",
                    }}
                    value={videoSettings.width?.replace("%", "")}
                    max={100}
                    min={0}
                    onChange={(value) => {
                      updateVideoSettings({
                        ...videoSettings,
                        width: `${value}%`,
                      });
                    }}
                  />
                </InputGroup>
              </Box>
              <Box>
                <FormLabel>
                  <FormattedMessage defaultMessage="Height" id="teLZyZ" />
                </FormLabel>
                <InputField
                  placeholder={intl.formatMessage({
                    defaultMessage: "500px | 100%",
                    id: "cl0C1J",
                  })}
                  value={videoSettings.height}
                  onChange={(e) => {
                    updateVideoSettings({
                      ...videoSettings,
                      height: e.target.value,
                    });
                  }}
                />
              </Box>
            </Stack>
            <Divider mt={"12px"} mb={"12px"} />
            <Stack gap="16px">
              <Stack
                direction="row-reverse"
                justifyContent={"space-between"}
                onClick={(e) => e.stopPropagation()}
                align="center"
                gap="8px"
                zIndex={0}
              >
                <Switch
                  mb="0"
                  size="md"
                  isChecked={videoSettings.autoplay}
                  onChange={(e) => {
                    updateVideoSettings({
                      ...videoSettings,
                      autoplay: e.target.checked,
                    });
                  }}
                />
                <Text as="span" fontSize="16px" color="gray.700">
                  <FormattedMessage defaultMessage="Autoplay" id="NBbGIg" />
                </Text>
              </Stack>

              <Stack
                direction="row-reverse"
                justifyContent={"space-between"}
                onClick={(e) => e.stopPropagation()}
                align="center"
                gap="8px"
                zIndex={0}
              >
                <Switch
                  mb="0"
                  size="md"
                  isChecked={videoSettings.muted}
                  onChange={(e) => {
                    updateVideoSettings({
                      ...videoSettings,
                      muted: e.target.checked,
                    });
                  }}
                />
                <Text as="span" fontSize="16px" color="gray.700">
                  <FormattedMessage defaultMessage="Mute video" id="0l3qW0" />
                </Text>
              </Stack>

              <Stack
                direction="row-reverse"
                justifyContent={"space-between"}
                onClick={(e) => e.stopPropagation()}
                align="center"
                gap="8px"
                zIndex={0}
              >
                <Switch
                  mb="0"
                  size="md"
                  isChecked={videoSettings.loop}
                  onChange={(e) => {
                    updateVideoSettings({
                      ...videoSettings,
                      loop: e.target.checked,
                    });
                  }}
                />
                <Text as="span" fontSize="16px" color="gray.700">
                  <FormattedMessage defaultMessage="Loop" id="9XiW3x" />
                </Text>
              </Stack>

              <Stack
                direction="row-reverse"
                justifyContent={"space-between"}
                onClick={(e) => e.stopPropagation()}
                align="center"
                gap="8px"
                zIndex={0}
              >
                <Switch
                  mb="0"
                  size="md"
                  isChecked={videoSettings.controls}
                  onChange={(e) => {
                    updateVideoSettings({
                      ...videoSettings,
                      controls: e.target.checked,
                    });
                  }}
                />
                <Text as="span" fontSize="16px" color="gray.700">
                  <FormattedMessage
                    defaultMessage="player controls"
                    id="Pgpo28"
                  />
                </Text>
              </Stack>
            </Stack>
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Stack>
  );
};

export default injectIntl(VideoDetails);
