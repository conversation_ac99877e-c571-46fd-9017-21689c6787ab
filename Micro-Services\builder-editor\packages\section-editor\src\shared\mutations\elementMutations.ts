import _cloneDeep from "lodash/cloneDeep";
import { nanoid } from "nanoid";
import { duplicateAppStoreButtonsMutation } from "./appStoreButtonsMutation";
import { duplicateButtonsMutation } from "./buttonsMutations";
import { deleteFormApiMutation, duplicateFormMutation } from "./formMutations";
import {
  CustomSection,
  SectionElement,
  WuiltContext,
  TextObject,
  FormSettings,
} from "@wuilt/section-preview";
import { getAllElementTexts } from "../utils/getTexts";
import { duplicateTextMutation } from "./textMutations";

const CustomDuplicates = {
  AppStoreButtons: duplicateAppStoreButtonsMutation,
};

const CustomTextDuplicates = {
  Buttons: duplicateButtonsMutation,
  Form: duplicateFormMutation,
  Text: duplicateTextMutation,
};

const CustomDeleteElement = {
  Form: deleteFormApiMutation,
};

export const addElementMutation = (
  section: CustomSection,
  rowIndex: number,
  columnIndex: number,
  newElement: SectionElement,
  newElementIndex = 0,
  newTexts?: TextObject
) => {
  const cloned = _cloneDeep(section);
  const elements = cloned?.rows[rowIndex]?.columns?.[columnIndex]?.elements;
  elements?.splice(newElementIndex, 0, newElement);
  cloned.rows[rowIndex].columns[columnIndex].elements = elements;
  cloned.text = { ...cloned?.text, ...newTexts };
  return cloned;
};

export function duplicateElementMutation(
  section: CustomSection,
  rowIndex: number,
  columnIndex: number,
  elementIndex: number,
  returnNewElement: false,
  wuiltContext: WuiltContext
): Promise<CustomSection>;

export function duplicateElementMutation(
  section: CustomSection,
  rowIndex: number,
  columnIndex: number,
  elementIndex: number,
  returnNewElement: true,
  wuiltContext: WuiltContext
): Promise<{
  newElement: SectionElement;
  newElementTexts: TextObject;
}>;

export async function duplicateElementMutation(
  section: CustomSection,
  rowIndex: number,
  columnIndex: number,
  elementIndex: number,
  returnNewElement = false,
  wuiltContext: WuiltContext = {}
) {
  const newElementIndex = elementIndex + 1;
  const cloned = _cloneDeep(section);
  const elements = cloned?.rows[rowIndex]?.columns?.[columnIndex]?.elements;
  const type = elements[elementIndex]?.type!;
  const oldElement = elements[elementIndex];
  let newElement;
  let newElementTexts = cloned?.text;
  // @ts-ignore
  if (CustomDuplicates[type]) {
    // @ts-ignore
    newElement = CustomDuplicates[type](oldElement, wuiltContext);
  } else if (CustomTextDuplicates[type]) {
    // @ts-ignore
    const { newElementSettings, newTextIds } = await CustomTextDuplicates[type](
      oldElement,
      newElementTexts,
      wuiltContext
    );
    newElement = newElementSettings;
    newElementTexts = { ...newElementTexts, ...newTextIds };
    cloned.text = newElementTexts;
  } else {
    newElement = { ...oldElement, id: `${type}:${nanoid()}` };
  }
  if (returnNewElement) return { newElement, newElementTexts };
  elements?.splice(newElementIndex + 1, 0, newElement);
  cloned.rows[rowIndex].columns[columnIndex].elements = elements;
  return cloned;
}

export const updateElementMutation = (
  section: CustomSection,
  rowIndex: number,
  columnIndex: number,
  elementIndex: number,
  newElement: SectionElement,
  newTexts?: TextObject
) => {
  const cloned = _cloneDeep(section);
  const elements = cloned?.rows[rowIndex]?.columns?.[columnIndex]?.elements;
  if (!elements) return section;
  elements[elementIndex] = newElement;
  cloned.rows[rowIndex].columns[columnIndex].elements = elements;
  cloned.text = { ...cloned?.text, ...newTexts };
  return cloned;
};

export const deleteElementMutation = (
  section: CustomSection,
  rowIndex: number,
  columnIndex: number,
  elementIndex: number,
  wuiltContext: WuiltContext
) => {
  const cloned = _cloneDeep(section);
  const elements = cloned?.rows[rowIndex]?.columns?.[columnIndex]?.elements;
  const element = elements[elementIndex];
  const text = cloned?.text;
  const deletedTexts = getAllElementTexts(element);
  deletedTexts?.forEach((textId: string) => delete text[textId]);
  CustomDeleteElement?.[element?.type]?.(
    [(element?.settings as FormSettings)?.formId],
    wuiltContext
  );
  elements?.splice(elementIndex, 1);
  cloned.rows[rowIndex].columns[columnIndex].elements = elements;
  return cloned;
};
