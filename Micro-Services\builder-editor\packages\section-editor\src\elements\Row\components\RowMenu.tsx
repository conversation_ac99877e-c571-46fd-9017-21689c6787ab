import React from "react";
import styled from "styled-components";
import { Row, UpdateDataFunc } from "@wuilt/section-preview";
import AddRowButtonV2 from "./AddRowButtonV2";
import { FormattedMessage } from "react-intl";
import RowSettings from "./RowSettings";
import RowActions from "./RowActions";
import { Popup } from "../../../components/Popup";
import { Heading, Tooltip, IconButton } from "@chakra-ui/react";
import { DragHandle } from "@wuilt/quilt";
import { ExpandIcon, RowsIcon, SettingsGeneralIcon } from "@wuilt/react-icons";

interface RowMenuSettingsProps {
  row: Row;
  rowIndex: number;
  disableAdding: boolean;
  disableDeleting: boolean;
  updateUi: UpdateDataFunc;
  onClosePopup: UpdateDataFunc;
  addRow: (location?: "before" | "after") => void;
  duplicateRow: () => void;
  onUploadImage: (cb: (src: string) => void) => void;
  deleteRow: () => void;
}
const RowMenu: React.FC<RowMenuSettingsProps> = ({
  disableAdding,
  disableDeleting,
  row,
  rowIndex,
  duplicateRow,
  updateUi,
  onClosePopup,
  onUploadImage,
  addRow,
  deleteRow,
}) => {
  return (
    <StyledRowMenu className="ui">
      <StyledIconWrapper>
        <RowsIcon size="16px" />
        <FormattedMessage defaultMessage="Row" id="0VkWFp" />
      </StyledIconWrapper>
      <Tooltip
        hasArrow
        placement="top"
        label={<FormattedMessage defaultMessage="Move row" id="ML+k5/" />}
      >
        <StyledIconWrapper>
          <DragHandle
            id={row?.id}
            DragIcon={<ExpandIcon size="16px" />}
            buttonIconProps={{ color: "white", stopOpacity: true }}
          />
        </StyledIconWrapper>
      </Tooltip>
      <StyledIconWrapper>
        <Popup
          onPopupClose={onClosePopup}
          activator={
            <span>
              <Tooltip
                hasArrow
                placement="top"
                label={
                  <FormattedMessage defaultMessage="Row settings" id="s/RjjT" />
                }
              >
                <IconButton
                  aria-label="Row settings"
                  size="16px"
                  variant="plain"
                >
                  <SettingsGeneralIcon size="16px" />
                </IconButton>
              </Tooltip>
            </span>
          }
        >
          <Popup.Header>
            <Heading variant="h5">
              <FormattedMessage defaultMessage="Row Settings" id="zQD/AG" />
            </Heading>
          </Popup.Header>
          <Popup.Body width="420px" pt="0">
            <RowSettings
              row={row}
              rowIndex={rowIndex}
              onUploadImage={onUploadImage}
              updateUi={updateUi}
            />
          </Popup.Body>
        </Popup>
      </StyledIconWrapper>

      <Tooltip
        hasArrow
        placement="top"
        closeOnScroll
        closeOnClick
        closeOnPointerDown
        label={<FormattedMessage defaultMessage="Actions" id="wL7VAE" />}
      >
        <StyledIconWrapper>
          <RowActions
            duplicateRow={duplicateRow}
            deleteRow={deleteRow}
            disableAdding={disableAdding}
            disableDeleting={disableDeleting}
          />
        </StyledIconWrapper>
      </Tooltip>
      {!disableAdding && (
        <Tooltip
          hasArrow
          placement="top"
          label={<FormattedMessage defaultMessage="Add Row" id="Cc0PzF" />}
        >
          <StyledIconWrapper>
            <AddRowButtonV2 addRow={addRow} />
          </StyledIconWrapper>
        </Tooltip>
      )}
    </StyledRowMenu>
  );
};

export default RowMenu;

export const StyledRowMenu = styled.div`
  position: absolute;
  top: 0;
  transform: translateY(-100%);
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
  align-items: center;
  border: solid 1px #0ba5ec;
  border-radius: 4px 4px 0px 0px;
  width: fit-content;
  overflow: hidden;
  height: 24px;
  background-color: white;
`;

const StyledIconWrapper = styled.div`
  cursor: pointer;
  height: 100%;
  padding-inline: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  font-size: 10px;
  gap: 4px;

  &:first-child {
    background-color: #0ba5ec;
    color: white;
  }

  &:nth-child(2) {
    ${({ theme }) =>
      `${
        theme.rtl
          ? "border-right: solid 1px #0BA5EC;"
          : "border-left: solid 1px #0BA5EC;"
      }`};
    border-top: none;
    border-bottom: none;
  }

  &:nth-child(3) {
    border: 1px solid #0ba5ec;
    border-top: none;
    border-bottom: none;
  }
  &:nth-child(5) {
    background: #026aa2;
    ${({ theme }) =>
      `${
        theme.rtl
          ? "border-right: solid 1px #0BA5EC;"
          : "border-left: solid 1px #0BA5EC;"
      }`};
    border-top: none;
    border-bottom: none;
  }

  button,
  button:hover {
    padding: 0;
    background: none;
    border: none;
    box-shadow: none;
  }
`;
