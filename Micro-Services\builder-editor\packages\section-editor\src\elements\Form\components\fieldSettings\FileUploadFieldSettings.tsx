import React, { <PERSON><PERSON><PERSON>, SetStateAction } from "react";
import { FormattedMessage } from "react-intl";
import { FormSettingsViews } from "../FormSettings";
import { FormField, TextObject, WuiltContext } from "@wuilt/section-preview";
import { getAllFieldTexts } from "../../../../shared/utils/getTexts";
import UpgradeButton from "../../../../shared/components/UpgradeButton";
import InputField from "../../../../components/InputField";
import {
  Box,
  Button,
  Divider,
  FormLabel,
  Checkbox,
  Switch,
  Text,
  HStack,
  FormControl,
} from "@chakra-ui/react";
import { InfoIcon, TrashIcon } from "@wuilt/react-icons";

// export const ALL_TYPES =
//   ".doc,.docx,.docb,.dot,.dotx,.xls,.xlsx,.xlt,.xltx,.ppt,.pptx,.pot,.potx,.pps,.ppsx,.pdf,.xps,.oxps,.pub,.txt,.rtf,.odt,.ott,.ods,.ots,.odp,.otp,.odg,.jpg,.png,.gif,.jpeg,.jpe,.jfif,.bmp,.heic,.heif,.tiff,.tif,.webp,.jpeg.2000,.raw,.svg,.avi,.mpeg,.mpg,.mp4,.mkv,.webm,.mov,.ogv,.vob,.m4v,.3gp,.divx,.xvid,.flv";
interface FileUploadFiledSettingsProps {
  field?: FormField;
  text: TextObject;
  fieldIndex: number;
  isAppRtl?: boolean;
  wuiltContext: WuiltContext | undefined;
  disablePlaceHolder?: boolean;
  updateFieldSettings: (newField: FormField, index: number) => void;
  deleteField: (id: string, deletedTexts: TextObject) => void;
  setView: Dispatch<SetStateAction<FormSettingsViews>>;
  updateText: (newTexts?: TextObject) => void;
}

const FileUploadFieldSettings: React.FC<FileUploadFiledSettingsProps> = ({
  field,
  text,
  fieldIndex,
  wuiltContext,
  disablePlaceHolder,
  updateFieldSettings,
  deleteField,
  setView,
  updateText,
}) => {
  const { id, is_expired: isExpired, plan } = wuiltContext.props.site;
  const planId = plan?.id;
  const isBusiness = planId === 16 || planId === 19;
  const isUltimate = planId === 17 || planId === 20;
  const sizeLimit = isBusiness ? 10 : isUltimate ? 100 : 25;
  return (
    <>
      <InputField
        label={<FormattedMessage defaultMessage="Label" id="753yX5" />}
        placeholder={<FormattedMessage defaultMessage="label" id="RYNL+m" />}
        value={text[field?.settings?.label?.textId!]}
        onChange={(event) =>
          updateText({
            [field?.settings?.label?.textId!]: event.target.value || "",
          })
        }
      />

      <InputField
        label={<FormattedMessage defaultMessage="Description" id="Q8Qw5B" />}
        placeholder={
          <FormattedMessage
            defaultMessage="Add description under the label"
            id="p7DADn"
          />
        }
        value={text[field?.settings?.description?.textId!]}
        onChange={(event) =>
          updateText({
            [field?.settings?.description?.textId!]: event.target.value || "",
          })
        }
      />
      {!disablePlaceHolder && (
        <InputField
          label={<FormattedMessage defaultMessage="Placeholder" id="h2T7yV" />}
          placeholder={
            <FormattedMessage
              defaultMessage="ex. No File Selected"
              id="maDh3E"
            />
          }
          value={text[field?.settings?.placeholder?.textId!]}
          onChange={(event) =>
            updateText({
              [field?.settings?.placeholder?.textId!]: event.target.value || "",
            })
          }
        />
      )}
      <Divider />
      <Checkbox
        size="sm"
        gap="8px"
        width="full"
        isChecked={field?.settings?.allowSpecificFileTypes}
        onChange={(event) => {
          updateFieldSettings(
            {
              ...field!,
              settings: {
                ...field?.settings,
                allowSpecificFileTypes: event.target.checked,
                allowedExtensions: "",
              },
            },
            fieldIndex
          );
        }}
      >
        <Text variant="textSm" fontWeight="medium" color="gray.800">
          <FormattedMessage
            defaultMessage="Allow only specific file types"
            id="FhyYE0"
          />
        </Text>
      </Checkbox>
      {field?.settings?.allowSpecificFileTypes && (
        <InputField
          type="text"
          placeholder={
            <FormattedMessage
              defaultMessage="ex.: psd, pdf, doc, png..etc."
              id="IGu0FZ"
            />
          }
          value={field.settings.allowedExtensions}
          onChange={(event) => {
            updateFieldSettings(
              {
                ...field!,
                settings: {
                  ...field?.settings,
                  allowedExtensions: event.target.value,
                },
              },
              fieldIndex
            );
          }}
        />
      )}
      <HStack
        gap="4px"
        width="full"
        padding="8px"
        border="1px solid"
        borderRadius="6px"
        borderColor="gray.300"
      >
        <InfoIcon size="20px" color="gray.500" />
        <Box>
          <Text fontWeight="600" fontSize="12px" color="gray.500">
            <FormattedMessage
              values={{
                sizeLimit,
              }}
              defaultMessage="Maximum file upload size is {sizeLimit}MB"
              id="lE6VX0"
            />
          </Text>
          {isBusiness && (
            <UpgradeButton
              siteId={id}
              isExpired={isExpired}
              content={
                <FormattedMessage
                  defaultMessage="Upgrade to get more space?"
                  id="aNqMrl"
                />
              }
            />
          )}
        </Box>
      </HStack>
      <Divider />
      <InputField
        label={<FormattedMessage defaultMessage="Error text" id="P5aj1m" />}
        placeholder={
          <FormattedMessage
            defaultMessage="ex. Please enter a valid File"
            id="5P25I0"
          />
        }
        value={text[field?.settings?.errorMessage?.textId!]}
        onChange={(event) =>
          updateText({
            [field?.settings?.errorMessage?.textId!]: event.target.value || "",
          })
        }
      />
      <Divider />
      <FormControl
        display="flex"
        alignItems="center"
        justifyContent="space-between"
      >
        <FormLabel htmlFor="required-field-switch" margin="0px">
          <Text variant="textSm" fontWeight="medium" color="gray.800">
            <FormattedMessage defaultMessage="Required" id="Seanpx" />
          </Text>
        </FormLabel>
        <Switch
          size="md"
          id="required-field-switch"
          isChecked={field?.settings?.required}
          onChange={(event) =>
            updateFieldSettings(
              {
                ...field!,
                settings: {
                  ...field?.settings,
                  required: event.target.checked,
                },
              },
              fieldIndex
            )
          }
        />
      </FormControl>
      <Divider />
      <Button
        size="sm"
        width="full"
        padding="16px"
        borderRadius="8px"
        justifyContent="start"
        variant="errorTertiaryColor"
        leftIcon={<TrashIcon size="16px" />}
        onClick={() => {
          const deletedFieldTexts = getAllFieldTexts(field!);
          deletedFieldTexts?.forEach((textId) => delete text[textId]);
          deleteField(field?.id!, text);
          deleteField(field?.id!, text);
          setView(FormSettingsViews.Tabs);
        }}
      >
        <Text variant="textSm" fontWeight="semibold">
          <FormattedMessage defaultMessage="Delete field" id="s6O1xS" />
        </Text>
      </Button>
    </>
  );
};

export default FileUploadFieldSettings;
