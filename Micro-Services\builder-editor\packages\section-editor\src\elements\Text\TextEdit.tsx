import React, { useState } from "react";
import { Editor } from "@tinymce/tinymce-react";
import { ElementsViewer, TextSettings } from "@wuilt/section-preview";
import { Box, utils } from "@wuilt/quilt";
import { ElementProps } from "../Element";
import styled, { css } from "styled-components";

// TinyMCE so the global var exists
import "tinymce/tinymce";
// DOM model
import "tinymce/models/dom/model";
// Theme
import "tinymce/themes/silver";
// Toolbar icons
import "tinymce/icons/default";
// Editor styles
import "tinymce/skins/ui/oxide/skin";

// importing the plugin js.
// if you use a plugin that is not listed here the editor will fail to load
import "tinymce/plugins/autolink";
import "tinymce/plugins/link";

// importing plugin resources
import "tinymce/plugins/emoticons/js/emojis";

// Content styles, including inline UI like fake cursors
import "tinymce/skins/content/default/content";
import "tinymce/skins/ui/oxide/content";

interface TextEditProps extends ElementProps {
  simple?: boolean;
  outline?: boolean;
}

const TextEdit: React.FC<TextEditProps> = ({
  element,
  simple,
  outline,
  text,
  updateTextApi,
  wuiltContext,
}) => {
  const theme = wuiltContext?.props?.theme;

  const updateContent = (newContent: string) => {
    updateTextApi?.({ [values?.textId!]: newContent });
  };

  const values = element?.settings as TextSettings;
  const [isInit, setIsInit] = useState(false);
  const [editing, setEditing] = useState(false);

  const simpleToolbar =
    "removeformat bold italic underline strikethrough forecolor";

  const fullToolbar = `undo redo styles fontsize fontsizeinput | removeformat bold italic underline strikethrough lineheight align link forecolor backcolor `;

  const fontSizes = `8px 10px 12px 14px 16px 18px 24px 30px 36px 48px 60px 72px 96px`;
  const lineHeights = `1 1.2 1.4 1.6 1.8 2 2.5 3 4`;

  const focusStyle = `div:focus { outline: none; }`;
  const textMargin = `h1, h2, h3, h4, h5, h6, p {margin:0;}`;

  const themeColors = [
    theme?.colors?.brand1,
    "brand1",
    theme?.colors?.brand2,
    "brand2",
    theme?.colors?.brand3,
    "brand3",
    theme?.colors?.brand4,
    "brand4",
    theme?.colors?.secondary1,
    "secondary1",
    theme?.colors?.secondary2,
    "secondary2",
    theme?.colors?.secondary3,
    "secondary3",
    theme?.colors?.secondary4,
    "secondary4",
    theme?.colors?.base1,
    "base1",
    theme?.colors?.base2,
    "base2",
    theme?.colors?.base3,
    "base3",
    theme?.colors?.base4,
    "base4",
    theme?.colors?.base5,
    "base5",
    theme?.colors?.accent1,
    "accent1",
    theme?.colors?.accent2,
    "accent2",
  ];

  const styleFormats = [
    {
      title: "Heading 1",
      block: "h1",
      attributes: { id: "global-heading-1-styles" },
    },
    {
      title: "Heading 2",
      block: "h2",
      attributes: { id: "global-heading-2-styles" },
    },
    {
      title: "Heading 3",
      block: "h3",
      attributes: { id: "global-heading-3-styles" },
    },
    {
      title: "Heading 4",
      block: "h4",
      attributes: { id: "global-heading-4-styles" },
    },
    {
      title: "Heading 5",
      block: "h5",
      attributes: { id: "global-heading-5-styles" },
    },
    {
      title: "Heading 6",
      block: "h6",
      attributes: { id: "global-heading-6-styles" },
    },
    {
      title: "Paragraph 1",
      block: "p",
      attributes: { id: "global-paragraph-1-styles" },
    },
    {
      title: "Paragraph 2",
      block: "p",
      attributes: { id: "global-paragraph-2-styles" },
    },
    {
      title: "Paragraph 3",
      block: "p",
      attributes: { id: "global-paragraph-3-styles" },
    },
  ];

  if (!editing) {
    return (
      <div
        style={{
          zIndex: 2,
          position: "relative",
          minWidth: 20,
          minHeight: 20,
        }}
        onMouseEnter={() => {
          setEditing(true);
        }}
      >
        <ElementsViewer.Text element={element} text={text} />
      </div>
    );
  }

  return (
    <div>
      {!isInit && <ElementsViewer.Text element={element} text={text} />}
      <StyledBox
        id={element?.id}
        dataTest="TextEdit"
        display={isInit ? "block" : "none"}
        p={simple ? "0" : "4px 0"}
        cursor="text"
        isOutline={outline}
        position="relative"
        zIndex={2}
        minWidth={20}
      >
        <Editor
          onInit={() => {
            setIsInit(true);
          }}
          scriptLoading={{ defer: true }}
          initialValue={text[values?.textId!] || ""}
          onBlur={(_event, editor) => {
            updateContent(editor?.getContent?.() ?? "");
          }}
          onDblclick={(e) => e.stopPropagation()}
          onClick={(e) => e.stopPropagation()}
          init={{
            inline: true,
            menubar: false,
            licenseKey:
              "9D8ordfrMXw6vpuLdFzstsUFGjjpK9SZATzoUFKcHxKi3exrSx9Tapi1V8xMMmrAiBcKArefuXhCVK1plfoGMhCXW9A9LFws",
            plugins: "link autolink",
            toolbar_mode: simple ? "scrolling" : "wrap",
            toolbar_location: "top",
            newline_behavior: "linebreak",
            toolbar: simple ? simpleToolbar : fullToolbar,
            content_style: `${textMargin} ${focusStyle} `,
            forced_root_block: values?.tag ?? "p",
            style_formats: styleFormats,
            paste_as_text: true,
            invalid_elements: "div",
            color_map: themeColors,
            line_height_formats: lineHeights,
            font_size_formats: fontSizes,
          }}
        />
      </StyledBox>
    </div>
  );
};

export { TextEdit };

/**
 * Styles
 */

const StyledBox = styled(Box)<{ isOutline: boolean }>`
  ${({ isOutline }) =>
    isOutline &&
    css`
      &:hover,
      &:focus {
        outline: 1px solid ${utils.color("product")};
      }
    `}
  strong,
  strong * {
    font-weight: bold !important;
  }
  em,
  em * {
    font-style: italic !important;
  }
  s,
  s * {
    text-decoration: line-through !important;
  }
  s span[style*="text-decoration: underline;"] {
    text-decoration: underline !important;
  }
  s,
  em,
  strong,
  span:not([style*="color"]),
  span[style*="color"] *:not([style*="color"]) {
    color: inherit !important;
  }
  s,
  em,
  strong,
  span:not([style*="font-size"]),
  span[style*="font-size"] *:not([style*="font-size"]) {
    font-size: inherit !important;
  }
`;
