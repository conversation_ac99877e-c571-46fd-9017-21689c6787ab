import React from "react";
import { FormattedMessage } from "react-intl";
import { ElementSettingsProps } from "../../Element/components/ElementSettings";
import VideoDetails from "./VideoDetails";
import { VideoSettings } from "@wuilt/section-preview";
import { Heading } from "@chakra-ui/react";
import { Popup } from "../../../components/Popup";

interface VideoSettingsProps extends ElementSettingsProps {}
const VideoSettings: React.FC<VideoSettingsProps> = ({
  element,
  updateElementUi,
}) => {
  const elementSettings = element?.settings as VideoSettings;
  const updateVideoSettings = (videoSettings: VideoSettings) => {
    updateElementUi({ ...element, settings: videoSettings });
  };
  return (
    <>
      <Popup.Header>
        <Heading variant="h5">
          <FormattedMessage defaultMessage="Video Settings" id="TfForS" />
        </Heading>
      </Popup.Header>
      <Popup.Body width="350px" pt="0">
        <VideoDetails
          videoSettings={elementSettings}
          updateVideoSettings={updateVideoSettings}
        />
      </Popup.Body>
    </>
  );
};

export default VideoSettings;
