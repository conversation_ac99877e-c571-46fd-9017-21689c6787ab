import {
  Column,
  FieldType,
  FormField,
  FormSettings,
  OptionType,
  Row,
  TextObject,
} from "@wuilt/section-preview";
import { nanoid } from "nanoid";

const isSelectableField = (type: FieldType) => {
  if (
    type === FieldType.Checkbox ||
    type === FieldType.Select ||
    type === FieldType.Radio
  ) {
    return true;
  }
  return false;
};
export const getAllFieldTexts = (field: FormField) => {
  let allFieldTexts;
  const fieldTexts = [
    field?.settings?.label?.textId!,
    field?.settings?.description?.textId!,
    field?.settings?.placeholder?.textId!,
    field?.settings?.errorMessage?.textId!,
  ];
  const isSelectable = isSelectableField(field?.type);

  if (isSelectable) {
    const optionsTexts = field?.settings?.options?.map(
      (o) => o?.label?.textId!
    );

    allFieldTexts = fieldTexts.concat(optionsTexts!);
  } else {
    allFieldTexts = fieldTexts;
  }

  return allFieldTexts;
};

export const getAllElementTexts = (element: any) => {
  if (element?.type === "Text") {
    return [element?.settings?.textId];
  }

  if (element?.type === "Form") {
    const fieldsTextsIds = element?.settings?.fields?.map(
      (field: FormField) => {
        return getAllFieldTexts(field);
      }
    );

    const formButtonTextId = element?.settings?.formButton?.textId;

    const successMessageTextId =
      element?.settings?.formPostSubmit?.successMessage?.textId;

    const failureMessageTextId =
      element?.settings?.formPostSubmit?.failureMessage?.textId;

    const formTexts = [
      fieldsTextsIds,
      formButtonTextId,
      successMessageTextId,
      failureMessageTextId,
    ].flat(2);
    return formTexts;
  }

  if (element?.type === "Buttons") {
    const buttonsTexts = element?.settings?.buttons?.map((button: any) => {
      return button?.textId;
    });
    return buttonsTexts;
  }

  return;
};

export const getAllColumnTexts = (column: Column) => {
  const texts = column?.elements?.map((element) => {
    return getAllElementTexts(element);
  });
  return texts.flat(1);
};

export const getAllRowTexts = (row: Row) => {
  const texts = row?.columns?.map((column) => {
    return getAllColumnTexts(column);
  });
  return texts.flat(1);
};

export const createNewFormTexts = (
  newElementTexts: TextObject,
  formSettings: FormSettings
) => {
  const newFormButtonTextId = `Content:${nanoid()}`;
  const newSuccessMessageTextId = `Content:${nanoid()}`;
  const newFailureMessageTextId = `Content:${nanoid()}`;
  let newTextIds = {
    ...newElementTexts,
    [newFormButtonTextId]: newElementTexts[formSettings?.formButton?.textId!],
    [newSuccessMessageTextId]:
      newElementTexts[formSettings?.formPostSubmit?.successMessage?.textId!],
    [newFailureMessageTextId]:
      newElementTexts[formSettings?.formPostSubmit?.failureMessage?.textId!],
  };

  const fields = formSettings?.fields?.map((f) => {
    const newLabelTextId = `Content:${nanoid()}`;
    const newPlaceholderTextId = `Content:${nanoid()}`;
    const newDescriptionTextId = `Content:${nanoid()}`;
    const newErrorMessageTextId = `Content:${nanoid()}`;

    newTextIds = {
      ...newTextIds,
      [newLabelTextId]: newElementTexts[f?.settings?.label?.textId!],
      [newPlaceholderTextId]:
        newElementTexts[f?.settings?.placeholder?.textId!],
      [newDescriptionTextId]:
        newElementTexts[f?.settings?.description?.textId!],
      [newErrorMessageTextId]:
        newElementTexts[f?.settings?.errorMessage?.textId!],
    };
    const isSelectable = isSelectableField(f?.type);

    const options =
      isSelectable &&
      f?.settings?.options?.map((option: OptionType) => {
        const newOptionTextId = `Content:${nanoid()}`;
        newTextIds = {
          ...newTextIds,
          [newOptionTextId]: newElementTexts[option?.label?.textId!],
        };

        return {
          ...option,
          label: {
            ...option?.label,
            textId: newOptionTextId,
          },
        };
      });
    return {
      ...f,
      settings: {
        ...f?.settings,
        label: {
          textId: newLabelTextId,
        },
        placeholder: {
          textId: newPlaceholderTextId,
        },
        description: {
          textId: newDescriptionTextId,
        },
        errorMessage: {
          textId: newErrorMessageTextId,
        },
        options,
        id: `Field:${nanoid()}`,
      },
    };
  });
  const textsIds = {
    newFormButtonTextId,
    newSuccessMessageTextId,
    newFailureMessageTextId,
  };
  return {
    newTextIds,
    textsIds,
    fields,
  };
};

export const getDefaultFormTexts = () => {
  const newTextIds = {
    failureMessageTextId: `Content:${nanoid()}`,
    successMessageTextId: `Content:${nanoid()}`,
    formButtonTextId: `Content:${nanoid()}`,
    nameLabelTextId: `Content:${nanoid()}`,
    namePlaceholderTextId: `Content:${nanoid()}`,
    nameDescriptionTextId: `Content:${nanoid()}`,
    nameErrorMessageTextId: `Content:${nanoid()}`,
    emailLabelTextId: `Content:${nanoid()}`,
    emailPlaceholderTextId: `Content:${nanoid()}`,
    emailDescriptionTextId: `Content:${nanoid()}`,
    emailErrorMessageTextId: `Content:${nanoid()}`,
    phoneLabelTextId: `Content:${nanoid()}`,
    phonePlaceholderTextId: `Content:${nanoid()}`,
    phoneDescriptionTextId: `Content:${nanoid()}`,
    phoneErrorMessageTextId: `Content:${nanoid()}`,
    messageLabelTextId: `Content:${nanoid()}`,
    messagePlaceholderTextId: `Content:${nanoid()}`,
    messageDescriptionTextId: `Content:${nanoid()}`,
    messageErrorMessageTextId: `Content:${nanoid()}`,
  };

  const newTextObject = {
    [newTextIds.failureMessageTextId]: "Error, please try again!",
    [newTextIds.successMessageTextId]:
      "Message sent successfully! We will contact you soon",
    [newTextIds.formButtonTextId]: "Send",
    [newTextIds.nameLabelTextId]: "Name",
    [newTextIds.namePlaceholderTextId]: "ex. John",
    [newTextIds.nameDescriptionTextId]: "",
    [newTextIds.nameErrorMessageTextId]: "",
    [newTextIds.emailLabelTextId]: "Email",
    [newTextIds.emailPlaceholderTextId]: "ex. <EMAIL>",
    [newTextIds.emailDescriptionTextId]: "",
    [newTextIds.emailErrorMessageTextId]: "",
    [newTextIds.phoneLabelTextId]: "Phone",
    [newTextIds.phonePlaceholderTextId]: "ex. +20 ************",
    [newTextIds.phoneDescriptionTextId]: "",
    [newTextIds.phoneErrorMessageTextId]: "",
    [newTextIds.messageLabelTextId]: "Message",
    [newTextIds.messagePlaceholderTextId]: "Enter your message",
    [newTextIds.messageDescriptionTextId]: "",
    [newTextIds.messageErrorMessageTextId]: "",
  };
  return {
    newTextIds,
    newTextObject,
  };
};
