import {
  Tooltip,
  BorderAllSidesIcon,
  BorderBottomIcon,
  BorderLeftIcon,
  BorderRightIcon,
  BorderTopIcon,
  utils,
} from "@wuilt/quilt";
import { FormattedMessage } from "react-intl";
import styled, { css } from "styled-components";
import { BorderPosition, checkIndicator } from "./helper";
import { Borders } from "@wuilt/section-preview";

interface BorderProps {
  borders: Borders | undefined;
  activeBorder: BorderPosition;
  setActiveBorder: React.Dispatch<React.SetStateAction<BorderPosition>>;
}
function BorderPositionSettings({
  borders,
  activeBorder,
  setActiveBorder,
}: BorderProps) {
  const BORDER_POSITION = [
    {
      Icon: <BorderAllSidesIcon size="lg" />,
      position: BorderPosition.all,
      content: <FormattedMessage defaultMessage="Border" id="sGaEsd" />,
      indicator: !!borders?.isAllSides,
    },
    {
      Icon: <BorderLeftIcon size="lg" />,
      position: BorderPosition.left,
      content: <FormattedMessage defaultMessage="Left" id="lJEnpw" />,
      indicator: checkIndicator(borders, BorderPosition.left),
    },
    {
      Icon: <BorderTopIcon size="lg" />,
      position: BorderPosition.top,
      content: <FormattedMessage defaultMessage="Top" id="X/9XdX" />,
      indicator: checkIndicator(borders, BorderPosition.top),
    },
    {
      Icon: <BorderBottomIcon size="lg" />,
      position: BorderPosition.bottom,
      content: <FormattedMessage defaultMessage="Bottom" id="hFAjsX" />,
      indicator: checkIndicator(borders, BorderPosition.bottom),
    },
    {
      Icon: <BorderRightIcon size="lg" />,
      position: BorderPosition.right,
      content: <FormattedMessage defaultMessage="Right" id="gAnLDP" />,
      indicator: checkIndicator(borders, BorderPosition.right),
    },
  ] as const;
  return (
    <StyledRowLocal>
      {BORDER_POSITION.map(({ Icon, position, content, indicator }) => {
        return (
          <Tooltip content={content} key={position}>
            <StyledIconWrapper
              isSelected={position === activeBorder}
              onClick={() => {
                setActiveBorder(position);
              }}
            >
              {Icon}
              <Indicator indicator={indicator} />
            </StyledIconWrapper>
          </Tooltip>
        );
      })}
    </StyledRowLocal>
  );
}

export default BorderPositionSettings;
const StyledRowLocal = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid ${utils.color("ink", "lighter")};
  border-radius: 8px;
  background-color: ${utils.color("cloud", "dark")};
  height: 48px;
  padding: 4px;
`;

const StyledIconWrapper = styled.div<{
  isSelected: boolean;
}>`
  cursor: pointer;
  position: relative;
  padding: 8px 14px;
  width: 46px;
  height: 40px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${utils.color("ink", "light")};

  &:hover {
    background: ${utils.color("white", "normal")};
    color: black;
    border-radius: 6px;
  }

  ${({ theme, isSelected }) =>
    isSelected &&
    css`
      background: ${utils.color("white", "normal")};
      color: black;
      box-shadow: ${theme.base.boxShadow.xs};
      border-radius: 6px;
    `}
  &:nth-child(1) {
    padding: 8px 2px;
    ${({ theme }) =>
      `${
        theme.rtl
          ? "border-left: solid 1px #bac7d5;"
          : "border-right: solid 1px #bac7d5;"
      }`};
    border-top: none;
    border-bottom: none;
  }
`;

const Indicator = styled.div<{ indicator: boolean }>`
  ${({ indicator }) =>
    indicator &&
    css`
      position: absolute;
      width: 6px;
      height: 6px;
      right: 5px;
      top: 5px;
      background: ${utils.color("product", "normal")};
      border-radius: 50%;
    `}
`;
