import {
  Popover,
  PopoverTrigger,
  PopoverContent,
  InputGroup,
  InputLeftAddon,
  Box,
  Portal,
  useDisclosure,
  useOutsideClick,
} from "@chakra-ui/react";
import styled from "@emotion/styled";
import React, { useRef } from "react";
import { HexColorInput, HexAlphaColorPicker } from "react-colorful";

interface ColorInputProps {
  color?: string;
  onChange?: (color: string) => void;
}

const ColorInput: React.FC<ColorInputProps> = ({ color, onChange }) => {
  const { isOpen, onToggle, onClose } = useDisclosure();
  const popoverRef = useRef(null);

  useOutsideClick({
    ref: popoverRef,
    handler: onClose,
  });
  return (
    <InputGroup height="44px" borderColor="gray.300" isolation={"auto"}>
      <InputLeftAddon
        bg="transparent"
        height="100%"
        paddingInlineStart="12px"
        paddingInlineEnd="8px"
      >
        <Popover placement="bottom-start" isOpen={isOpen} onClose={onClose}>
          <PopoverTrigger>
            <Box
              cursor="pointer"
              height="24px"
              width="24px"
              minWidth="24px"
              border="1px solid"
              borderColor="gray.300"
              borderRadius="4px"
              backgroundColor={color}
              onClick={onToggle}
            />
          </PopoverTrigger>
          <Portal>
            <Box zIndex="popover" w="full" h="full" position="relative">
              <PopoverContent
                width="fit-content"
                borderRadius="10px"
                ref={popoverRef}
              >
                <HexAlphaColorPicker color={color} onChange={onChange} />
              </PopoverContent>
            </Box>
          </Portal>
        </Popover>
      </InputLeftAddon>
      <StyledColorInput
        prefixed
        inputMode="numeric"
        color={color}
        onChange={onChange}
      />
    </InputGroup>
  );
};
export default ColorInput;

const StyledColorInput = styled(HexColorInput)`
  width: 100%;
  border-radius: 8px;
  border: 1px solid var(--chakra-colors-gray-300);
  border-inline-start: 0;
  border-start-start-radius: 0;
  border-end-start-radius: 0;
  &:active,
  &:focus {
    outline: none;
  }
`;
